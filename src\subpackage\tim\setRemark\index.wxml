<custom-header customBack bind:back="onNavBack" title="{{title}}" />

<view bind:tap="onHideKey" class="body">
  <view catch:tap="onCatch" class="input-box">
    <input
      class="input"
      placeholder="{{placeholder || '请输入'}}"
      placeholder-class="placeholder"
      value="{{content}}"
      maxlength="-1"
      hold-keyboard
      adjust-position="{{false}}"
      focus="{{isFocus}}"
      bind:focus="onFocus"
      bind:blur="onHideKey"
      bind:input="onInput"
    />
    <view class="input-right">
      <view bind:tap="onClear" class="input-clear {{content.length < 1 && 'hidden'}}">
        <icon-font type="yp-search-shanchu" size="32rpx" color="rgba(0, 0, 0, 0.45)" />
      </view>
      <view class="info-num">
        <view class="{{content.length > maxContent ? 'num-err' : 'num'}} {{content.length < 1 && 'num-gray'}}">{{content.length || 0}}</view>
        <view class="num-gray">/{{maxContent}}</view>
      </view>
    </view>
  </view>
</view>

<m-button-footer custom-btn="{{!content && content === contentOld ? 'custom-btn' : ''}}"  bind:click="onSubmit" bottom="{{bottomHeight || 0}}px">保存</m-button-footer>

/*
 * @Date: 2022-01-25 14:08:51
 * @Description:
 */
import { getAgreementUrl } from '@/config/app'
import { MapStateToData, connectPage } from '@/store/index'
import { helper, tools } from '@/utils/index'
import { AesEncrypt } from '@/utils/tools/common/index'

const { validator } = tools
const { login } = helper
const { isPhone, validPassWord } = validator
const { jumpTo, detailUserLoginInfo, afterLogin } = login

const mapStateToData: MapStateToData = (state) => {
  const { storage } = state
  return {
    sourceCode: storage.sourceCode,
    sourceShare: storage.source_share,
  }
}

Page(connectPage(mapStateToData)({
  /** 页面的初始数据 */
  data: {
    // 1为验证码登录，2为密码登录
    active: 1,
    // 手机号
    phone: '',
    // 验证码
    code: '',
    // 密码
    password: '',
    // 错误提示
    errorTip: '',
    // 是否点击协议
    isCheckAgreement: false,
  },
  // 切换tab
  onChangeTab(e) {
    const { index } = e.target.dataset
    this.setData({
      active: index,
      phone: '',
      errorTip: '',
    })
  },
  // 展示验证码错误信息
  onShowErrorTip(val) {
    this.setData({
      errorTip: val.detail,
    })
  },
  // 校验手机号
  validataPhone() {
    let errorTip = ''
    if (!this.data.phone) {
      errorTip = '请输入手机号码'
    } else if (!isPhone(this.data.phone)) {
      errorTip = '请输入正确的手机号'
    } else {
      // 手机号校验通过
      errorTip = ''
    }
    return errorTip
  },
  // 校验验证码或密码
  validatePasOrCode() {
    let errorTip = ''
    if (Number(this.data.active) === 1) {
      if (!this.data.code) {
        errorTip = '请输入验证码'
      }
    } else if (Number(this.data.active) === 2) {
      if (!this.data.password) {
        errorTip = '请输入密码'
      } else if (!validPassWord(this.data.password)) {
        errorTip = '密码位数为6-20位'
      }
    }
    return errorTip
  },
  // 手机号失去焦点
  onBlurPhone() {
    // 由于blur在tap前执行，为了防止出现提示，加延迟
    this.timeId = setTimeout(() => {
      if (!this.isClear) {
        this.setData({
          errorTip: this.validataPhone(),
        })
      }
    }, 50)
  },
  // 清除输入框内容
  onClear(e) {
    const { type } = e.currentTarget.dataset
    this.isClear = true
    this.setData({
      [type]: '',
      errorTip: '',
    })
  },
  // 监听输入框值的改变
  onChange(e) {
    const { type } = e.target.dataset
    // 判断手机号是否校验通过
    const isPhonePass = !this.validataPhone()
    if (type === 'phone') {
      this.isClear = false
      if (!isPhonePass) {
        this.setData({
          errorTip: '',
        })
      }
    } else if (isPhonePass) {
      this.setData({
        errorTip: '',
      })
    }
    this.setData({
      [type]: e.detail.value,
    })
  },
  // 登录
  async onLogin() {
    // 判断没输入电话
    if (!this.data.phone) {
      return
    }

    // 判断验证码密码
    this.setData({
      errorTip: this.validatePasOrCode(),
    })

    // 判断有错误
    if (this.data.errorTip) {
      return
    }

    if (!this.data.isCheckAgreement) {
      wx.$.msg('请阅读并勾选下方协议')
      return
    }
    // 1验证码登录 2密码登录
    if (Number(this.data.active) === 1) {
      const data = {
        tel: this.data.phone,
        code: this.data.code,
        refid: this.data.sourceCode,
        source: this.data.sourceShare,
      }
      wx.$.fetch['POST/login/mobile/codeLogin'](data, { hideMsg: true }).then(res => {
        detailUserLoginInfo(res, (resp, newMember) => this.newInterfaceDealInfo(resp, newMember), (tip) => this.setData({
          errorTip: tip,
        }))
      }).catch(res => {
        wx.$.msg(res.head.msg)
      })
    } else if (Number(this.data.active) === 2) {
      const password = await AesEncrypt(this.data.password)
      const data = {
        tel: this.data.phone,
        password,
        refid: this.data.sourceCode,
        source: this.data.sourceShare,
      }
      wx.$.fetch['POST/login/mobile/pwdLogin'](data, { hideMsg: true }).then(res => {
        detailUserLoginInfo(res, (resp, newMember) => this.newInterfaceDealInfo(resp, newMember), (tip) => this.setData({
          errorTip: tip,
        }))
      }).catch(res => {
        wx.$.msg(res.head.msg)
      })
    }
  },
  // todo 整理逻辑后续可提到公共 获取账号信息
  async newInterfaceDealInfo(resp, newMember) {
    const user = {
      userId: Number(resp.data.user_id),
      token: resp?.data?.sign?.token,
      tokenTime: resp?.data?.sign?.time,
      uuid: resp.data.uuid,
      login: true,
      tel: resp.data.tel,
      role: resp.data.role,
      newMember,
    }
    afterLogin(user)
    jumpTo()
  },
  /** 生命周期函数--监听页面卸载 */
  onUnload() {
    this.timeId && clearTimeout(this.timeId)
  },
  // 跳转隐私
  onToGreement(e) {
    const { type } = e.target.dataset
    wx.$.r.push({ path: `/subpackage/web-view/index?url=${getAgreementUrl(type)}` })
  },
  // 点击协议
  onCheckAgreement() {
    this.setData({
      isCheckAgreement: !this.data.isCheckAgreement,
    })
  },
}))

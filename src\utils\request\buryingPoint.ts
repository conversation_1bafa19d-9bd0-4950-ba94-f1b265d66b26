/*
 * @Date: 2023-07-10 16:08:12
 * @Description: 接口请求和响应的埋点
 */

const urlArr = [
  'resume/v1/resumePerfect/publish',
  'reach/v1/verifyCode/simple/send',
  'message/home/<USER>',
  'login/mobile/programTelLogin',
  'job/member/init',
  'job/fastIssue/issueJobChangPhone', // 排查发布招工流程2，点击发布请求
  'job/job/v1/matchComplianceThesaurus', // 排查发布招工流程2，点击发布请求
  'job/fastIssue/issue', // 排查发布招工流程2，点击发布请求
  'job/fastIssue/complete', // 排查发布招工流程2，点击发布请求
  // 下面的用于风控，查看是否有过多用户批量请求
  'job/v3/list/job/list', // 招工列表
  'job/v2/job/info', // 招工详情
  'job/v2/search/job/search', // 招工搜索
  'resume/v3/list/app/list', // 找活列表
  'resume/v3/detail/app/otherDetail', // 找活详情
  'resume/v3/list/app/list', // 找活搜索
  'account/v1/login/codeLogin', // 验证码登录
  'account/v1/login/programTelLogin', // 小程序一键登录
]

// 请求前
export const beforeRequestPoint = (config: { url: string; requestData: any; headers: any }) => {
  const { url, headers, requestData } = config

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let interface_name = ''
  // eslint-disable-next-line array-callback-return
  urlArr.map((item) => {
    if (!(url.indexOf(item) < 0)) {
      interface_name = item
      wx.$.collectEvent.event('InterfacePull', {
        /** 接口名 */
        interface_name,
        params: JSON.stringify(requestData || {}),
        header: JSON.stringify(headers || {}),
        /** 这个签名是死的 */
        validate_sign: 'md5',
      })
    }
  })
}

// 请求后
export const afterRequestPoint = (config: { url: string; responseData: any }) => {
  const { url, responseData } = config

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let interface_name = ''
  // eslint-disable-next-line array-callback-return
  urlArr.map((item) => {
    if (!(url.indexOf(item) < 0)) {
      interface_name = item
      wx.$.collectEvent.event('InterfaceReturn', {
        /** 接口名 */
        interface_name,
        /** 返回数据 */
        data: JSON.stringify(responseData || {}),
      })
    }
  })
}

// 请求失败
export const responseErrPoint = (config: { url: string; err: any, requestType: string}) => {
  const { url, err, requestType } = config
  wx.$.collectEvent.event('InterfaceResponseErr', {
    /** 接口名 */
    interface_name: url,
    requestType,
    /** 返回数据 */
    data: JSON.stringify(err || {}),
  })
}

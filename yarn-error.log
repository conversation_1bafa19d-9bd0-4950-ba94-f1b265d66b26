Arguments: 
  /usr/local/bin/node /Users/<USER>/.yarn/bin/yarn.js

PATH: 
  /Users/<USER>/.cargo/bin:/Users/<USER>/.yarn/bin:/Users/<USER>/.config/yarn/global/node_modules/.bin:/Library/Frameworks/Python.framework/Versions/3.7/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/usr/local/go/bin:/usr/local/share/dotnet:/opt/X11/bin:~/.dotnet/tools:/Library/Apple/usr/bin:/Library/Frameworks/Mono.framework/Versions/Current/Commands:/usr/local/bin/:/usr/local/go/bin:/Users/<USER>/work/java/maven/apache-maven-3.6.0/bin:/Users/<USER>/work/gowork/bin:/Users/<USER>/work/gowork/src:local:/Library/Java/JavaVirtualMachines/jdk-*********.jdk/Contents/Home/bin:/Library/Java/JavaVirtualMachines/jdk-*********.jdk/Contents/Home/lib:/usr/local/lib/pkgconfig:/Users/<USER>/work/android/sdk/emulator:/Users/<USER>/work/android/sdk/platform-tools:/Users/<USER>/work/android/sdk/cmdline-tools/latest/bin:/Users/<USER>/work/android/sdk/cmdline-tools/latest/lib

Yarn version: 
  1.19.2

Node version: 
  16.15.1

Platform: 
  darwin x64

Trace: 
  Error: EINVAL: invalid argument, mkdir '/Users/<USER>/work/yupaowang/mini/yp-mini/node_modules/mini-build-script/node_modules/@babel/cli/node_modules'

npm manifest: 
  {
    "name": "yp-mini",
    "version": "1.0.0",
    "description": "小程序",
    "bin": {
      "mini": "./packages/mini-build-script/bin/mini-build-script.js"
    },
    "scripts": {
      "cicd": "node ./start/cicd.js",
      "cit": "node ./start/cit.js",
      "start": "node ./start/index.js",
      "ts-api-doc": "node ./help/tsApiDoc.js",
      "prepare": "husky install",
      "rap_previous": "rapper --type normal --rapperPath \"src/services\" --apiUrl \"http://rap2api.taobao.org/repository/get?id=273365&token=HDQE12YNhVHZ9xxWI-0ySfTaYsKn0AHO\" --rapUrl \"http://rap2.taobao.org\" && yarn sed",
      "sed": "sed -i '' -e '1s/^//p; 1s/^.*/import { IExtra } from \"@\\/utils\\/request\\/type.d\"/' src/services/request.ts  && sed -i '' 's/commonLib.IExtra/IExtra/g' src/services/request.ts",
      "create": "node ./packages/mini-build-script/bin/mini-build-script create",
      "tips": "node ./help/tips.js",
      "mini-analyze": "node ./packages/mini-analyze/start.js",
      "eslint": "eslint --ext .ts,.js src/",
      "eslint-fix": "node ./node_modules/.bin/eslint --fix --ext .ts,.js src/"
    },
    "license": "ISC",
    "devDependencies": {
      "@datarangers/sdk-mp": "^2.12.0",
      "@reduxjs/toolkit": "^1.9.0",
      "commitizen": "^4.2.4",
      "cross-spawn": "^7.0.3",
      "eslint-plugin-sonarjs": "^0.24.0",
      "husky": "^7.0.4",
      "inquirer": "^8.2.0",
      "mini-analyze": "file:./packages/mini-analyze",
      "mini-build-script": "file:./packages/mini-build-script",
      "miniprogram-automator": "^0.12.0",
      "rap": "^1.3.1",
      "webpackbar": "^5.0.2"
    },
    "dependencies": {
      "miniprogram-ci": "^1.9.7"
    }
  }

yarn manifest: 
  No manifest

Lockfile: 
  No lockfile

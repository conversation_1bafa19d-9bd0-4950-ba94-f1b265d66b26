/* eslint-disable no-undef */
/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/no-unnecessary-type-constraint */
/*
 * @Date: 2022-04-14 18:08:12
 * @Description: 接口请求代理对象
 */

import { store } from '@/store/index'
import { request } from './request'
import * as javaReq from './request_java'
import { getNonceHeader } from '../getHomeConfig/getNonceHeader'

// 如果 Proxy 有兼容问题
// https://github.com/GoogleChrome/proxy-polyfill/blob/master/src/proxy.js
/** 接口请求 */
export const fetch = new Proxy(
  {},
  {
    get(_, p: string) {
      if (p == 'toJSON') return ''
      return async (data, extra, headers) => {
        const [type, ...urlArr] = p.split('/')
        const url = `/${urlArr.join('/')}`
        const userInfo = store.getState().storage.userState
        const ypNonce = await getNonceHeader(userInfo.token)
        const head = { ...ypNonce, ...headers }
        return request({ type, url, data, extra, head })
      }
    },
  },
)

// 如果 Proxy 有兼容问题
// https://github.com/GoogleChrome/proxy-polyfill/blob/master/src/proxy.js
/** 接口请求 */
export const javafetch = new Proxy(
  {},
  {
    get(_, p: string) {
      if (p == 'toJSON') return ''
      return async (data, extra, headers) => {
        // 'POST/message/home/<USER>'
        const [type, ...urlArr] = p.split('/')
        const url = `/${urlArr.join('/')}`
        const userInfo = store.getState().storage.userState
        const ypNonce = await getNonceHeader(userInfo.token)
        const head = { ...ypNonce, ...headers }
        return javaReq.request({ type, url, data, extra, head })
      }
    },
  },
) as JavaFetch

/**
 * 请求额外配置具体以 wx.$.javafetch 内部为主
 */
export interface IApiJavaServiceOptions {
  /** 用于请求数据中传递 wechat_token 属性且值为 miniConfig.appid */
  isNoToken?: boolean
  /** 自定义请求头部 */
  headers?: Record<string, any>
  /** 请求超时时间, 单位为毫秒 */
  timeout?: number
  /** mock
   *  @deprecated 貌似最新 java 接口废弃了(没有模拟的 mock 地址数据了)
   */
  mock?: boolean
  /**
   * 是否隐藏接口错误 wx.$.msg 消息提示(需自定义错误提示或不提示的场景)
   * @default false
   */
  hideMsg?: boolean
  [key: string]: any
}

interface IRes<U extends keyof YResponseTypes> {
  /** code 为 0 代表成功 其它则为错误码具体看对应接口情况 */
  code: number
  /** 响应提示消息 */
  message: string
  /** 响应数据 */
  data: YModels[U]['Res']['data']
  /** 当接口报错时, 后端会通过该字段进行错误排查 */
  askId: string
  // 如果 error 与 success 代表的是响应成功与错误状态(它们相互互斥的情况的话) 感觉存在一个就行了
  /** 响应错误状态 true 代表错误 false 代表成功 */
  error: boolean
  /** 响应成功状态 true 代表成功 false 代表失败 */
  success: boolean
  [key: string]: any
}

/**
 * 基于 wx.$.javafetch 封装的上层请求服务 APIService
 * @param apiUrl java 请求地址 例如: POST/xxx/v1/xxx
 * @param params java 请求参数
 * @param options.isNoToken 默认(false) 每次请求添加 appid 到请求参数wechat_token 中
 * @param options.headers 自定义头部
 * @param options.timeout 请求超时时间, 单位为毫秒
 * @param options.hideMsg 隐藏接口错误 wx.$.msg 消息提示(需自定义错误提示或不提示的场景)
 */
export async function APIJavaService<U extends keyof YResponseTypes>(
  apiUrl: U,
  params?: YModels[U]['Req'],
  options?: IApiJavaServiceOptions,
): Promise<IRes<U>> {
  try {
    const res = ((await wx.$.javafetch[apiUrl](params, options)) as unknown) as IRes<U>
    if (res.code == 0) {
      return Promise.resolve<IRes<U>>(res)
    }
    return Promise.reject<IRes<U>>(res)
  } catch (error) {
    return Promise.reject<IRes<U>>(error)
  }
}

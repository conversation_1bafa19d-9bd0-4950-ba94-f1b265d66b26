import { helper } from '@/utils/index'
import { store } from '@/store/index'

interface IResumeCardItem {
  /** 控件 id */
  id: number
  /** 控件图标 */
  icon: string
  /** 控件名称 */
  name: string
  /** 控件名称对应值 */
  value: string
}

/**
 * @name 处理用户的名称展示
 */
function getDefaultUserName(username: string, sex: string | number) {
  if (username) {
    return username
  }
  return sex == '2' ? '女士' : '先生'
}

/**
 * @name 处理性别，年龄，民族
 * @example 男 · 28岁 · 汉族
 */
function getSexToAgeToNationText(sex, age, nation) {
  const sexText = sex == '2' ? '女' : '男'
  const ageText = parseInt(age, 10) ? ` · ${parseInt(age, 10)}岁` : ''
  const nationText = String(nation).includes('族') ? ` · ${nation}` : ''
  return `${sexText}${ageText}${nationText}`
}

const teamIcons = {
  个人: 'yp-icon_zhxq_tdgc',
  班组: 'yp-icon_zhxq_bz',
  施工队: 'yp-icon_zhxq_sgd',
  突击队: 'yp-icon_zhxq_tjd',
}

/**
 * @name 获取队伍图标 1 个人 2 班组 3 施工队 4 突击队(没有 icon 则根据 teamName 获取图标)
 * @param teamName 人员构造名称
 * @example 当接口未返回 icon 时,使用人员构成名称返回对应字体图标
 * @default 个人图标
 */
function getTeamIconByName(teamName: string): string {
  const teamNameStr = String(teamName).trim()
  if (teamNameStr.startsWith('个人')) {
    return teamIcons['个人']
  }
  if (teamNameStr.startsWith('班组')) {
    return teamIcons['班组']
  }
  if (teamNameStr.startsWith('施工队')) {
    return teamIcons['施工队']
  }
  if (teamNameStr.startsWith('突击队')) {
    return teamIcons['突击队']
  }
  return teamIcons['个人']
}

/** @name 优质师傅推荐-数据清洗 */
export function dataCleanOfQualityMaster(item) {
  if (item.hide) {
    return { hide: true }
  }
  const userId = wx.$.u.getObjVal(store.getState().user, 'userInfo.userBaseObj.userId')
  const isSelf = item.userId == userId

  // ------- java 接口替换
  const controlClassifyList = item.controlClassifyList || []
  /** 后端模板字典对象(通过 controlList.id 进行分组) */
  const templateIdObj = groupResumeCardById(controlClassifyList)

  /** 期望薪资 */
  const expectedSalary = templateIdObj[6]?.value || ''
  /** 页面标题 */
  const title = `${item.name}${expectedSalary ? ` | ${expectedSalary}` : ''}`

  return {
    ...item,
    // ------ 用户基本
    /** 用户头像 */
    avatar: item.headPortrait || 'https://cdn.yupaowang.com/yupao_mini/yp_mini_zp_head_photo.png',
    /** 用户名 */
    username: getDefaultUserName(item.name, item.sex),
    /** 实名状态 */
    realNameStatus: item.realNameStatus,
    /** 是否展示进场状态(找活状态为1,预计进场时间大于0或者活跃状态为1--展示进场状态) */
    isResumeState: item.workStatus == 1 && (item.days > 0 || item.activeStatus == 1),
    /** 是否展示承诺到场 */
    isReport: item.isReport,
    /** 求职期望的预计进场时间 */
    days: item.days,
    /** 暂不找活 */
    isFindWork: item.workStatus == 2,
    /** 性别 & 年龄 & 民族组合文字  */
    sexAgeNationStr: getSexToAgeToNationText(item.sex, item.age, item.nation),

    // ------ 用户标签
    /** 用户标签 */
    userTags: getUserTags(item, templateIdObj),

    // ------ 找活期望
    /** 距离 */
    distance: helper.location.getProjectAddressDistanceTxt(`${item.longitude},${item.latitude}`),
    /** 期望薪资 */
    expectedSalary,
    /** 项目地 */
    workCity: item.hopeArea.replace(/\|/g, '、'),
    /** 工种标签 */
    craftTags: item.occupationList || [],
    /** 自我介绍 */
    introduce: item.introduce,
    /** 求职期望视频和图片 */
    expectMedia: getExpectMedia(item.resumeVideo),

    // ------ 项目经验
    /** 项目经验---只取通过审核的，并且只要存在照片，视频，描述任一即展示 */
    projectList: getProjectList(item.projectExperienceList),

    // ------ 卡片底部收藏栏
    /** 是否收藏 */
    isCollect: item.collected,
    /** 是否已经投诉过 */
    isComplained: item.complained,
    /** id */
    id: item.resumeId,
    /** 名片信息UUid */
    uuid: item.resumeUuid,
    subUuid: item.resumeSubUuid,

    // ------ 电话相关
    /** 该电话是否可以免费查看拨打： 1 - 免费 ， 0 - 不免费 */
    isFreeCall: item.checkout?.free,
    /** 剩余免费联系次数 1.3.1 的需求时再加上 */
    freeCallNum: item.checkout?.free ? item.checkout.freeCount || 0 : 0,
    /** 是否已查看手机号 */
    hasShowPhone: item.viewed,
    /** 专区 */
    specialArea: item.specialArea,

    // ------ 卡片其它信息
    /** 该找活名片是否是用户自己的 */
    isSelf,
    // ------ 未分配字段
    /** 页面标题 */
    title,

    // -------- 埋点数据
    item_idx: item.item_idx,
  }

  /** 获取求职期望视频和图片 [{ img: 'http://img.png', video: 'http://video.mp4', type: 'image|video' }], */
  function getExpectMedia(resumeVideo: any) {
    if (!resumeVideo) {
      return []
    }
    const { cover, url, duration } = resumeVideo
    return [{ type: 'video', img: cover, video: url, duration }]
  }

  function getUserTags(item, tagsInfo: any) {
    const tagList: { img?: string; icon?: string; text: string }[] = []
    // 人员构成
    const personnelInfo = tagsInfo[4]
    if (personnelInfo?.value) {
      const teamName = personnelInfo.value
      tagList.push({ img: personnelInfo.icon, icon: getTeamIconByName(teamName), text: teamName })
    }
    // 技能认证
    if (item.certList?.length) {
      tagList.push({ img: '', icon: 'yp-icon_zhxq_jnrz', text: '技能认证' })
    }
    // 工龄
    const seniorityInfo = tagsInfo[5]
    if (seniorityInfo?.value) {
      tagList.push({ img: seniorityInfo.icon, icon: 'yp-icon_zhxq_gl', text: seniorityInfo.value })
    }
    // 用工周期(长期工 短期工 长短皆可)
    const workTypeInfo = tagsInfo[1]
    if (workTypeInfo?.value) {
      tagList.push({ img: workTypeInfo.icon, icon: 'yp-icon_zhxq_cdq', text: workTypeInfo.value })
    }
    // 熟练度
    const profDegreeText = tagsInfo[3]
    if (profDegreeText?.value) {
      tagList.push({ img: profDegreeText.icon, icon: 'yp-icon_zhxq_sld', text: profDegreeText.value })
    }
    return tagList
  }

  function getProjectList(projectList: any[]) {
    if (!projectList?.length) {
      return []
    }
    // TODO zddq test check 状态测试
    // 过滤项目信息 审核通过 且 拥有项目介绍 或 项目文件信息
    // const filterItem = (it) => it.check == 2 && (it.detail || it.fileList.length)
    const filterItem = (it) => it.detail || it.fileList.length
    const list = projectList.filter(filterItem).map(mapItem) || []
    return list.slice(0, 3)

    function mapItem(item) {
      // 项目描述
      const desc = splicingDetail(item)
      // type 类型：0-图片，1-视频，2-其他
      const fileListMapItem = ({ type: sourceType, url, cover }) => {
        const isVideoType = sourceType == 1
        return { type: isVideoType ? 'video' : 'image', url, poster: isVideoType ? cover : '', cover: isVideoType ? cover : '' }
      }
      // 项目视频图片资源
      const sources = item.fileList?.map(fileListMapItem) || []
      return { desc, sources }

      /** 需要 项目名称 完工日期 城市名称 项目描述 拼接成项目描述信息 */
      function splicingDetail(values) {
        let str = ''
        if (values.projectName) {
          str += `${values.projectName}\n`
        }
        if (values.completionTime) {
          str += `${values.completionTime}完工\n`
        }
        if (values.cityName) {
          str += `${values.cityName}\n`
        }
        if (values.detail) {
          str += values.detail
        }
        return str
      }
    }
  }
}

/**
 * 根据控件 id 对获取的名片控件分类列表进行分类
 * @description 1-用工周期 2-用工方式 3-熟练度-工程 4-人员构成 5-工龄-工程 6-结算方式 7-工资经验-物流 8-期望月薪-物流 9-驾照 10-驾照分数 11-证件信息 12-熟练程度-物流 13-工种标签-司机 14-工种标签-快递
 */
export function groupResumeCardById(list: any[]): { [key: string]: IResumeCardItem } {
  try {
    const controlList: IResumeCardItem[] = list.reduce((prev, next) => ([...prev, ...(next?.controlList || [])]), [])
    const cb = (it) => (it?.id ? ({ [it.id]: it }) : ({}))
    return controlList.reduce((prev, next) => ({ ...prev, ...cb(next) }), {})
  } catch (error) {
    console.error(error)
    return {}
  }
}

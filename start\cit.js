/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-04-26 09:10:50
 * @FilePath: /yp-mini-two/start/cit.js
 * @Description:
 */
const chalk = require("./chalk");
const { getDevelopmentStr, reportVersion } = require("./utils");
const ci = require("miniprogram-ci");
const inquirer = require("inquirer");
const fs = require("fs");
const { getFeishuDocContent } = require("./feishuNotify");
const miniConfig = require("./miniConfig");
const { spawn } = require("cross-spawn");

async function main() {
  const miniType = "weapp";
  const nConfig = miniConfig[miniType];
  delete nConfig.ypjzzg;
  /** 读取配置文件 */
  const configData = fs.readFileSync("src/config/app.ts", "utf-8");
  /** 获取数据 */
  const match = configData.match(/export const REQUEST_VERSION = '(.+?)'/);
  /** 获取版本号 */
  const version = match && match[1];
  console.log("主要用途: ", chalk.green("给测试打包提测使用"));
  // const resa = await reportVersion(miniType)
  // console.log('resa', resa)
  checkList(nConfig).then(op => {
    console.log("op:",op);
    
    const option = { miniType };
    const apps = op.apps;
    const all = miniConfig[option.miniType];
    let miniName = [];
    /** 总包 */
    let totalSize = "0KB";
    /** 主包 */
    let mainSize = "0KB";
    const developmentStr = getDevelopmentStr(op.development);
    /** 备注处理 */
    op.desc = `${version}-${developmentStr}${op.desc ? ": " + op.desc : ""}`;
    op.miniName = [];
    apps.forEach(item => {
      if (all[item]) {
        op.miniName.push(all[item]);
      }
    });
    console.log("op", op);

    apps.forEach(async ky => {
      const appInfo = all[ky];
      option.sub = ky;
      option.mode = "build";
      // 强制设置为正式环境
      option.development = op.development;
      process.env.RUN_INFO = JSON.stringify(option);
      process.env.MINI_CONFIG = JSON.stringify(miniConfig);
      process.env.APPKY = `/${ky}`;
      // 运行模块引用检查，只检查模块是否能正确解析
      console.log("正在检查模块引用...");
      const importCheckResult = spawn.sync(
        "yarn",
        ["check-imports"],
        {
          stdio: "inherit",
          cwd: process.cwd()
        }
      );

      // 如果模块引用检查失败，停止构建
      if (importCheckResult.status !== 0) {
        console.error("发现模块引用错误，构建已停止！");
        process.exit(1);
      }

      console.log("模块引用检查通过，开始构建...");
      spawn.sync(
        "node",
        [
          "./packages/mini-build-script/bin/mini-build-script",
          `${option.mode}:${option.miniType}`
        ],
        {
          stdio: "inherit",
          cwd: process.cwd()
        }
      );
      let developmentStr = "";

      const project = new ci.Project({
        appid: appInfo.appid,
        type: "miniProgram",
        projectPath: `./dist/${ky}/weapp`,
        privateKeyPath: `./start/my/private.${appInfo.appid}.key`,
        ignores: []
      });
      ci.upload({
        project,
        version,
        desc: op.desc,
        threads: 50,
        setting: {
          es6: true,
          es7: true,
          minify: true,
          minifyJS: true,
          minifyWXML: true,
          minifyWXSS: true
        }
      })
        .then(res => {
          res.subPackageInfo.forEach(item => {
            if (item.name === "__APP__") {
              mainSize = `${Math.round(item.size / 1024)}KB`;
            } else if (item.name === "__FULL__") {
              totalSize = `${Math.round(item.size / 1024)}KB`;
            }
          });
          miniName.push({ ...appInfo });
        })
        .catch(err => {
          console.log("err:", err);
          miniName.push({ ...appInfo, name: `${appInfo.name}-上传失败`, error: true });
        })
        .finally(async res => {
          console.log("op", op);
          console.log("finally", res);
          if (apps.length === miniName.length) {
            getFeishuDocContent({
              ...option,
              desc: op.desc,
              miniName,
              version,
              mainSize,
              totalSize
            });
            reportVersion(miniType);
          }
          // ci.getDevSourceMap({
          //   project,
          //   robot: 1,
          //   sourceMapSavePath: `./dist/sourceMap.zip`,
          // }).then((res) => {
          //   console.log('res', res)
          // }).catch((err) => {
          //   console.log('err', err)
          // })
        });
    });
  });
}

async function checkList(config) {
  let op = {};
  const developmentData = {
    type: "list",
    message: "请选择运行环境：",
    name: "development",
    choices: answers => {
      const miniType = answers;
      const list = [
        {
          name: "测试站",
          value: "DEV"
        },
        {
          name: "开发站",
          value: "DEVELOP"
        },
        {
          name: "预发布",
          value: "PRE"
        },
        {
          name: "预发布正式站",
          value: "REL"
        },
        {
          name: "正式站",
          value: "PRO"
        }
      ];
      return list;
    }
  };
  const apps = {
    type: "checkbox",
    message: "\n请选择编译标识：",
    name: "apps",
    pageSize: 100,
    choices: () => {
      return Object.keys(config).map(item => {
        return {
          name: config[item].name,
          value: item
        };
      });
    }
  };
  const inputData = {
    type: "input",
    message: "请输入备注:",
    name: "desc"
  };

  async function checkboxApps() {
    const check = await inquirer.prompt([apps]);
    if (!check.apps || !check.apps.length) {
      return checkboxApps();
    } else {
      return check;
    }
  }
  op = await inquirer.prompt([inputData, developmentData, apps]);
  if (!op.apps || !op.apps.length) {
    const checkOp = await checkboxApps();
    op = { ...op, ...checkOp };
  }
  return op;
}

main().catch(error => {
  console.error(error);
  process.exit(1);
});

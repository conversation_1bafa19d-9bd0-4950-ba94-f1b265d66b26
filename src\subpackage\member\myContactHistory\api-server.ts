import { APIJavaService } from '@/utils/request/index'
import { storage, store } from '@/store/index'
import { PLTools } from '@/utils/index'
import { resumeCardFormatDataV4 } from '@/utils/helper/resume/index'

/** @name 获取拨打记录滑动底部默认展示天数配置信息 */
export async function getContactConfigApi() {
  const defaultDay = 7
  try {
    const res = await APIJavaService('POST/clues/v1/contact/contactConfig')
    const day = res.data.showDay || defaultDay
    return { showDay: `${day}天` }
  } catch (error) {
    console.error(error)
    return { showDay: `${defaultDay}天` }
  }
}

/** @name 获取默认选中的Tab下班 */
export async function getMyContactDefaultActiveTabIdxApi(fmtQuery) {
  // 兼容以前路径参数->如果存在 tab_index
  const { tab_index } = fmtQuery || {}
  if (tab_index !== '' && typeof tab_index !== 'undefined' && tab_index != null) {
    return [0, 1].includes(Number(tab_index)) ? Number(tab_index) : 0
  }

  try {
    const res = await APIJavaService('POST/clues/v1/contact/tabConfig')
    const data = res.data || {}
    // 1 谁联系过我 2 我联系过谁
    return data.code == 1 ? 1 : 0
  } catch (error) {
    console.error(error)
    return 0
  }
}

export async function getContactRecordService(isRefresh: boolean) {
  const { activeTabIdx } = this.data
  try {
    if (activeTabIdx == 1) {
      return getContactMeListApi.call(this, isRefresh)
    }
    return getMyContactListApi.call(this, isRefresh)
  } catch (error) {
    console.error(error)
    return Promise.resolve({})
  }
}

/** @name 我沟通的人列表数据 */
async function getMyContactListApi(isRefresh) {
  const { myContactInfo } = this.data
  const { userChooseRole } = store.getState().storage
  try {
    const tempInfo = PLTools.getReqPLStatus(myContactInfo, { isRefresh })
    this.setData({ myContactInfo: tempInfo })

    const { page, pageSize } = tempInfo
    const location = storage.getItemSync('userLocation') || ''
    const reqParams = { contactType: 2, currentPage: page, pageSize, location }
    let res: any = {}
    let oList: any = []

    // B端新接口-- 看牛人卡片
    if (userChooseRole == 1) {
      res = await APIJavaService('POST/clues/v2/contact/pageResume', reqParams, { hideMsg: true })
      oList = resumeCardFormatDataV4(res.data?.data)
      console.log('kkkkkkkkkkkkkkkkkkkkkkk', oList)
    } else {
      // C端老接口
      res = await APIJavaService('POST/clues/v1/contact/page', reqParams, { hideMsg: true })
      oList = res?.data?.data || []
    }
    return { myContactInfo: PLTools.getResPLStatus(tempInfo, { newList: oList || [],
      fmtListItem: (item: any) => ({
        ...item,
        // 手动命名字段，B端写死（B端新接口没返回，需要为了兼容老逻辑的取值，就不用一直用userChooseRole判断了）
        identityType: item?.identityType?.code == 1 ? item.identityType : { code: 2, desc: '牛人' },
        // 手动命名字段，B端接口返回的字段名称为ifReturn
        isReturn: userChooseRole == 1 ? item?.ifReturn : item?.isReturn,
        // 手动命名字段subUuid，B端接口的 resumeSubUuid 映射 subUuid 和 uuid
        subUuid: userChooseRole == 1 ? item?.resumeSubUuid : item?.subUuid,
        // 手动命名字段uuid(主要用于埋点)，B端接口的 resumeSubUuid 映射 subUuid 和 uuid
        uuid: userChooseRole == 1 ? item?.resumeSubUuid : item?.uuid,
        // 手动命名字段expenseId，B端接口expenseOrRightId积分消耗记录id或权益id
        expenseId: userChooseRole == 1 ? item?.expenseOrRightId : item?.expenseId,
        // 手动命名字段occupation，B端转成数组格式。
        occupation: userChooseRole == 1 ? (item?.occupation?.occName ? [item.occupation.occName] : []) : item?.occupation,
        // 手动命名字段occupation，B端从userInfo里取出userId。
        userId: userChooseRole == 1 ? item?.userInfo?.userId : item?.userId,
        // 手动命名字段contactInfoType，B端-- 写死找活
        contactInfoType: item?.identityType?.code == 1 ? item.contactInfoType : { code: 2, desc: '找活' },
        source_id: '11',
        source: '联系记录',
      }) }) }
  } catch (error) {
    return { myContactInfo: PLTools.getInitPLStatus(myContactInfo), loadFail: true }
  }
}

/** @name 谁沟通过我列表数据 */
async function getContactMeListApi(isRefresh) {
  const { contactMeInfo } = this.data
  const { userChooseRole } = store.getState().storage

  try {
    let tempInfo = PLTools.getReqPLStatus(contactMeInfo, { isRefresh })
    this.setData({ contactMeInfo: tempInfo })

    const { page, pageSize } = tempInfo

    let res: any = {}
    let oList: any = []
    // B端新接口-- 看牛人卡片
    if (userChooseRole == 1) {
      res = await APIJavaService('POST/clues/v2/contact/pageResume', { contactType: 1, currentPage: page, pageSize }, { hideMsg: true })
      oList = resumeCardFormatDataV4(res.data?.data)
    } else {
      // C端老接口
      res = await APIJavaService('POST/clues/v1/contact/page', { contactType: 1, currentPage: page, pageSize }, { hideMsg: true })
      oList = res.data?.data
    }
    const workerRoleItem = (item: any) => ({
      ...item,
      // 手动命名字段，B端写死（B端新接口没返回，需要为了兼容老逻辑的取值，就不用一直用userChooseRole判断了）
      identityType: item?.identityType?.code == 1 ? item.identityType : { code: 2, desc: '牛人' },
      // 手动命名字段，B端接口返回的字段名称为ifReturn
      isReturn: userChooseRole == 1 ? item?.ifReturn : item?.isReturn,
      // 手动命名字段subUuid，B端接口的 resumeSubUuid 映射 subUuid 和 uuid
      subUuid: userChooseRole == 1 ? item?.resumeSubUuid : item?.subUuid,
      // 手动命名字段uuid(主要用于埋点)，B端接口的 resumeSubUuid 映射 subUuid 和 uuid
      uuid: userChooseRole == 1 ? item?.resumeSubUuid : item?.uuid,
      // 手动命名字段expenseId，B端接口expenseOrRightId积分消耗记录id或权益id
      expenseId: userChooseRole == 1 ? item?.expenseOrRightId : item?.expenseId,
      // 手动命名字段occupation，B端转成数组格式。
      occupation: userChooseRole == 1 ? (item?.occupation?.occName ? [item.occupation.occName] : []) : item?.occupation,
      // 手动命名字段occupation，B端从userInfo里取出userId。
      userId: userChooseRole == 1 ? item?.userInfo?.userId : item?.userId,
      // 手动命名字段contactInfoType，B端--写死招工（这里是与后端确认过的，这个值就是招工）
      contactInfoType: item?.identityType?.code == 1 ? item.contactInfoType : { code: 1, desc: '招工' },
    })
    tempInfo = PLTools.getResPLStatus(tempInfo, { newList: oList || [], fmtListItem: userChooseRole == 1 ? workerRoleItem : (item: any) => ({ ...item }) })
    // fmtListItem: userChooseRole == 1 ? workerRoleItem : (item: any) => ({ ...item })

    console.log('897878888888888888', tempInfo)
    return { contactMeInfo: tempInfo }
  } catch (error) {
    return { contactMeInfo: PLTools.getInitPLStatus(contactMeInfo), loadFail: true }
  }
}

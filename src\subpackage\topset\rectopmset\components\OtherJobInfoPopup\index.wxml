<drawer bind:close="onClosePopup" visible>
  <view class="box">
    <view class="bg" />
    <view class="header">
      <!-- <view class="header-right" bind:tap="toPublished">{{isHandleChoose ? '其他职位信息' : '其他信息'}}</view> -->
      <text>选择职位信息</text>
      <view bind:tap="onClosePopup" class="closeIcon">
        <icon-font type="yp-huchudanchuangguanbianniubeifen" size="rpx" color="#" custom-style="display: flex" />
      </view>
    </view>
    <scroll-view scroll-y="{{true}}" catch:tap="touchMove" class="scroll">
      <view wx:for="{{recommendList}}" wx:for-item="item" wx:key="index">
        <TopJobCard bind:setTop="setTop" item="{{item}}" />
      </view>
      <view class="bottom">{{"- 暂无更多的职位信息 -"}}</view>
      <view class="PH" />
    </scroll-view>
  </view>
</drawer>

.backTip {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 50;
  width: 100%;
  height: 100%;

  .flexRCC();
}

.maskBg {
  position: fixed;
  top: 0;
  left: 0;

  opacity: 0;
  transition: opacity 0.24s;

  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);

  &.open {
    opacity: 1;
  }
}

.box {
  position: relative;

  width: 606rpx;
  padding: 48rpx 32rpx 32rpx;
  border-radius: 16rpx;
  background: #fff;
  overflow: hidden;

  .bg {
    position: absolute;
    left: 0;
    top: 0;

    width: 100%;
    height: 160rpx;
    background: linear-gradient(180deg, #d1ebff 0%, #fff 100%);
  }

  .title {
    position: relative;
    z-index: 1;
    height: 54rpx;
    line-height: 54rpx;

    color: @text85;
    font-size: 38rpx;
    font-weight: bold;
    text-align: center;
  }

  .subTitle {
    position: relative;
    z-index: 1;
    height: 42rpx;
    line-height: 42rpx;
    margin-top: 16rpx;

    color: @text65;
    font-size: 30rpx;
    text-align: center;
  }

  .primaryColor {
    color: @primary-color;
    font-weight: bold;
    margin-left: 8rpx;
  }
}

.randomTopInfoRow {
  position: relative;
  z-index: 1;

  display: flex;
  flex-direction: column;
  justify-content: center;

  height: 128rpx;
  border-radius: 16rpx;
  background: linear-gradient(88deg, #e3f4ff 0%, #edf9ff 100%);
  padding: 0 24rpx;
  margin-top: 32rpx;

  .topUserNums {
    .flexRC();
    height: 36rpx;
    line-height: 36rpx;

    color: @text65;
    font-size: 26rpx;
  }

  .topUserNum {
    color: @text85;
    margin: 0 8rpx;
  }

  .topUserImgs {
    .flexRC();

    margin-top: 8rpx;
  }

  .avatarBox {
    .flexRCC();

    width: 40rpx;
    height: 40rpx;
    border-radius: 40rpx;
    overflow: hidden;
    border: 2rpx solid #fff;
  }

  .avatarBox1 {
    margin-left: -8rpx;
  }

  .avatar {
    width: 100%;
    height: 100%;
  }
}

.topImg {
  position: absolute;
  top: 0;
  right: 0;

  width: 128rpx;
  height: 128rpx;
}

.btns {
  .flexRC();
  margin-top: 32rpx;

  .cancel {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 100%;
    height: 80rpx;
    border: 2rpx solid @primary-color;
    border-radius: 12rpx;
    background-color: #fff;

    color: @primary-color;
    font-size: 30rpx;
    margin-right: 22rpx;
  }
}

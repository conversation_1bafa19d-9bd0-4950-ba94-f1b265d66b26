<view class="out-v {{msgInfo.isSelf ? '' : 'out-start'}}">
  <msg-head-img wx:if="{{!msgInfo.isSelf && msgInfo.isAvatar}}" msgInfo="{{msgInfo}}" />
  <msg-state wx:if="{{msgInfo.isSelf}}" style="height: 100%;" margin="right" msgInfo="{{msgInfo}}" status="{{msgInfo.status}}"/>
  <view class="recuit-content {{!msgInfo.isSelf?'recuit-content-l':''}}" catch:tap="onClick">
    <view class="recruit-title">{{info.title}}</view>
    <view class="classly-p" wx:if="{{classNames.length > 0}}">
      <view class="classly-content">
        <view class="classly-txt" wx:for="{{classNames}}" wx:for-index="idx" wx:for-item="itemName" wx:key="idx">
          {{itemName}}
        </view>
      </view>
    </view>
    <view class="address-txt" wx:if="{{info.address}}">{{info.address}}</view>
    <view class="detail-txt" wx:if="{{info.detail}}">{{info.detail}}</view>
  </view>
  <msg-head-img wx:if="{{msgInfo.isSelf && msgInfo.isAvatar}}" msgInfo="{{msgInfo}}" />
</view>

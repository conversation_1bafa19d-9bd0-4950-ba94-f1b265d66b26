/*
 * @Author: 汤启民
 * @Date: 2022-03-07 10:03:38
 * @Description: 视频上传
 */

import { MapStateToData, connectPage, dispatch, actions } from '@/store/index'
import { getMenuButtonBoundingClientRect, getSystemInfoSync } from '@/utils/tools/common/index'
import { resourceProcess } from '@/utils/tools/upload/index'
import { addOrEditVideo } from './utils'

const { top: menuTop, height: menuHeight } = getMenuButtonBoundingClientRect()
/** 获取可使用窗口宽度、高度 */
const { windowWidth, windowHeight, statusBarHeight } = getSystemInfoSync()
/** 视频的总时长 */
let videoDuration = 0
/** 是否是断点续传 */
const isProceed = false
const mapStateToData: MapStateToData = (state) => {
  return {
    userState: state.storage.userState,
    resumeState: state.resume,
    storageState: state.storage,
    videoState: state.video,
  }
}

Page(
  connectPage(mapStateToData)({
    data: {
      pageTop: menuTop + menuHeight,
      /** 窗口的宽高 */
      windowSize: {
        windowWidth,
        windowHeight,
        statusBarHeight,
      },
      /** 视频类型：1 - 面试视频简历 ，2 - 项目视频 */
      videoType: '1',
      /** java接口上传必传项 入口场景id：默认100001，公开文件：100001，私有文件：100002 */
      entryId: 100001,
      /** 上传视频封面的入口场景id：为空时使用 entryId的值 */
      entryIdVideo: null,
      /**
       * 页面来源，表示从哪一个页面过来的
       * 1-找活名片;
       * 2-个人中心头部引导;
       * 3-会员中心我的服务;
       * 4-任务列表；
       * 5-IM;
       * 6-站内信/推送
       * */
      pageSource: '',
      /** 顶部的提示文字 */
      noticeText: '视频拍摄完成，你可以播放预览，满意后请点击【立即上传】，不满意请点击【重新录制】',
      /** video实例 */
      videoCtx: null,
      /** 拍摄视频的本地临时路径 */
      videoPath: '',
      /** 拍摄视频的本地临时封面 */
      thumbPath: '',
      /** 格式化后的视频时长 */
      videoTime: '00:00',
      /** 视频的展示方式 */
      videoObjectFit: 'cover',
      /** 视频的动态样式 */
      videoStyle: '',
      /** 视频外部的view标签高度 */
      videoHeight: 0,
      /** 视频的真实高度 */
      videoRealHeight: 0,
      /** 播放状态 */
      playState: false,
      /** 用于生成水印的canvas */
      watermarkCanvas: null,
      /** 视频播放进度条 */
      progress: 0,
      /** 上传视频弹窗 */
      showProgressModal: false,
      /** 上传状态： 0 - 上传中 ， 1 - 上传失败 ， 2 - 上传成功 */
      uploadState: 0,
      /** 上传进度 */
      uploadProgress: 0,
      /** 水印图 */
      watermarkImg: '',
      /** 阿里云上传示例 */
      uploader: null,
      /** 是否显示播放按钮 */
      showPlayBtn: true,
    },
    /** 返回重新录制 */
    onBack() {
      if (this.data.playState) {
        this.onClickVideo({ currentTarget: { dataset: { type: '' } } })
      }
      this.setData({
        showPlayBtn: false,
      })
      wx.$.confirm({
        content: '重新录制将无法再发布本次拍摄的视频，请谨慎操作！',
        cancelText: '重新录制',
        confirmText: '继续发布',
      })
        .catch(() => {
          wx.$.r.back()
        })
        .finally(() => {
          this.setData({
            showPlayBtn: true,
          })
        })
    },
    /** 上传成功回调 */
    onUploadSuccess() {
      this.setData({
        showProgressModal: false,
        showPlayBtn: true,
      })
      wx.$.r.back(2)
    },
    /** 切换视频播放状态 */
    onClickVideo(e) {
      const { type } = e.currentTarget.dataset
      // 如果点击的是水印不播放视频
      if (type && type == 'mark') {
        return
      }
      this.data.videoCtx[this.data.playState ? 'pause' : 'play']()
      this.setData({
        playState: !this.data.playState,
        showPlayBtn: !this.data.showPlayBtn,
      })
    },
    /** 当视频播放中会触发,计算剩余时间 */
    onTimeUpdate(e) {
      this.setData({
        videoTime: wx.$.u.transformationTime(e.detail.duration - e.detail.currentTime),
        progress: (e.detail.currentTime / e.detail.duration) * 100,
      })
    },
    /** 当视频播放完毕时 */
    onEnded() {
      this.setData({
        showPlayBtn: true,
        playState: false,
        videoTime: wx.$.u.transformationTime(videoDuration),
        progress: 0,
      })
    },
    /** 视频数据加载完成时触发 */
    onLoadVideoData(e) {
      videoDuration = e.detail.duration || videoDuration
      // eslint-disable-next-line prefer-const
      let { videoObjectFit, videoStyle, videoHeight, videoRealHeight } = this.data
      if (e.detail?.width > e.detail.height) {
        // 说明是横屏
        videoObjectFit = 'fill'
        videoRealHeight = videoHeight
        videoStyle = `width:${videoRealHeight || this.data.windowSize.windowHeight}px;height:100vw;transform: rotate(90deg);transform-origin: 50vw 50vw;`
      } else {
        videoRealHeight = this.data.windowSize.windowWidth * (e.detail.height / (e.detail?.width || e.detail.height))
        videoStyle = `width:100%;height:${videoRealHeight || this.data.windowSize.windowHeight}px;`
      }
      this.setData({
        videoTime: wx.$.u.transformationTime(e.detail.duration),
        videoStyle,
        videoObjectFit,
        videoRealHeight,
      })
      this.getCanvasInfo(e.detail?.width, e.detail.height)
    },
    /** 上传视频 */
    async onClickUpload() {
      if (this.data.playState) {
        this.onClickVideo({ currentTarget: { dataset: { type: '' } } })
      }
      this.setData({
        showPlayBtn: false,
      })
      /** 这是针对【项目视频】没有落地页的情况，只把视频保留在本地作为素材 */
      if (this.data.videoType === '2' && this.data.pageSource === '0') {
        this.saveVideoToLocal()
        return
      }
      /** 获取网络 */
      const { networkType } = await wx.getNetworkType()
      if (!networkType || networkType === 'none') {
        wx.showToast({
          title: '未连接到网络，请重新连接！',
          icon: 'none',
        })
        this.setData({
          showPlayBtn: true,
          uploadState: 1,
        })
        return
      }
      wx.onNetworkStatusChange((res) => {
        if (!res.isConnected) {
          wx.showToast({
            title: '未连接到网络，请重新连接！',
            icon: 'none',
          })
          this.setData({
            showPlayBtn: true,
            uploadState: 1,
          })
          wx.offNetworkStatusChange(() => {})
        } else {
          this.setData({
            uploadState: 0,
          })
        }
      })
      /** 准备开始上传 */
      this.setData({
        uploadState: 0,
      })
      /** 继续上传-阿里上传SDK自动断点续传 */
      if (isProceed) {
        return
      }
      /** 重置进度条，开启上传进度弹窗 */
      this.setData({
        uploadProgress: 0,
        showProgressModal: true,
        showPlayBtn: false,
      })
      this.drawWatermark()
    },
    /** 将拍摄的视频保存到本地 */
    saveVideoToLocal() {
      wx.saveVideoToPhotosAlbum({
        filePath: this.data.videoPath,
        success: () => {
          if (this.data.pageSource === '0') {
            wx.$.alert({
              title: '温馨提醒',
              content: '您拍摄的视频已保存到手机相册，您可以前往手机相册查看',
              confirmText: '我知道了',
            }).then(() => {
              this.setData({
                showPlayBtn: true,
              })
              wx.$.r.back(2)
            })
          } else {
            wx.$.msg('视频已保存到手机相册')
          }
        },
      })
    },
    /**
     * @description 上传视频
     * @param {reload} boolean 是否是重新上传，父组件调用
     */
    async handleUploadVideo(watermarkImg) {
      const suffixName = this.data.videoPath.split('.').pop()
      const { entryId, appId, entryIdVideo } = this.data
      const resUpload: any = await resourceProcess({
        type: 1,
        format: suffixName,
        entryId,
        entryIdVideo: entryId == 1004 ? 555648 : entryIdVideo,
        processType: 6,
        videoTempFilePath: this.data.videoPath,
        watermarkTempFilePath: watermarkImg,
      }, { appId })
      if (!resUpload || resUpload.error) {
        this.setData({
          uploadState: 1,
          showProgressModal: false,
        })
      } else if (this.data.videoType === '1') {
        // 面试视频上传成功逻辑-调用新的java接口，接口需要的文件相关参数可以通过【wx.getVideoInfo】获取 duration: `${videoDuration}`,
        addOrEditVideo(resUpload).then((res) => {
          const isSuc = !res.error
          this.setData({
            uploadState: isSuc ? 2 : 1,
          })
          wx.offNetworkStatusChange(() => { })
        }).catch((err) => {
          this.setData({ uploadState: 1 })
          wx.offNetworkStatusChange(() => { })
        })
      } else {
        // TODO 项目视频上传成功逻辑-调用新的java接口，接口需要的文件相关参数可以通过【wx.getVideoInfo】获取
        this.saveVideoToLocal()
        const resApplyData = resUpload.data
        const { uploadUrlInfo, watermarkUploadUrlInfo } = resApplyData.extraInfo
        if (this.data.pageSource !== '0') {
          dispatch(
            actions.videoActions.setMediaFile([
              {
                tempFilePath: this.data.videoPath,
                resApplyData,
                resourceId: resUpload.data.resourceId,
                path: resUpload.data.extraInfo.accessUrl,
                thumbTempFilePath: this.data.thumbPath,
                videoId: resUpload.data.resourceId,
                watermark: this.data.watermarkImg,
                originImageUrl: watermarkUploadUrlInfo.formData.key,
                originVideoUrl: uploadUrlInfo.formData.key,
              },
            ]),
          )
          wx.$.r.back(2)
        }
      }
    },
    /** 绘制水印图 */
    drawWatermark() {
      wx.getImageInfo({
        src: 'https://staticscdn.zgzpsjz.com/miniprogram/images/wjj/yp-mini_video_logo_image.png',
        success: (res) => {
          const { watermarkCanvas } = this.data
          if (!watermarkCanvas) {
            return
          }
          let tempWidth = windowWidth
          let tempHeight = windowHeight
          if (watermarkCanvas.width === windowHeight) {
            // eslint-disable-next-line no-mixed-operators
            const dpr = ((watermarkCanvas.width || 0) / watermarkCanvas.height) * (2 / 3)
            tempWidth = (watermarkCanvas.width || 0) / dpr
            tempHeight = watermarkCanvas.height / dpr
          }
          const ctx = watermarkCanvas.getContext('2d')
          /** 绘制图像到画布 */
          const img = watermarkCanvas.createImage()
          img.src = res.path
          img.onload = async () => {
            /** 保存当前画布 */
            ctx.save()
            /** 画布旋转45°，准备画水印 */
            ctx.rotate(-Math.PI / 4)
            ctx.globalAlpha = 0.3
            ctx.font = '18px normal'
            ctx.fillStyle = '#ffffff'
            ctx.beginPath()
            /** 绘制logo和用户编号到画布上 */
            for (let j = tempWidth * 3 * -1; j < tempWidth * 3; j += 708) {
              for (let i = 0; i < tempHeight * 2.5; i += 80) {
                /** 绘制图像到画布 */
                // eslint-disable-next-line no-await-in-loop
                await ctx.drawImage(img, j, i, 68, 30) // 绘制图像到画布
                /** 填写文字到画布（ios0.1透明度文字不显示） */
                const { userId } = this.data.userState || {}
                ctx.fillText(`鱼泡网会员：${userId || ''}`, j + 128, i + 24)
                ctx.fillText(`该视频仅用于鱼泡网${this.data.videoType === '2' ? '找人' : ''}找活`, j + 410, i + 24)
              }
            }
            /** 恢复之前保存的画布 */
            ctx.restore()
            wx.canvasToTempFilePath({
              canvas: this.data.watermarkCanvas,
              success: (resCanvas) => {
                this.handleUploadVideo(resCanvas.tempFilePath)
              },
            })
          }
        },
      })
    },
    getCanvasInfo(width: number, height: number) {
      wx.createSelectorQuery()
        .select('#watermark')
        .fields({
          node: true,
          size: true,
        })
        .exec((res) => {
          /** 获取canvas实例 */
          let dpr = getSystemInfoSync().pixelRatio
          const watermarkCanvas = res[0].node
          if (width > height) {
            watermarkCanvas.width = windowHeight
            watermarkCanvas.height = windowWidth
            // eslint-disable-next-line no-mixed-operators
            dpr = (watermarkCanvas.width / watermarkCanvas.height) * (2 / 3)
          } else {
            watermarkCanvas.width = res[0].width * dpr
            watermarkCanvas.height = res[0].height * dpr
          }
          const watermarkCanvasCtx = watermarkCanvas.getContext('2d')
          watermarkCanvasCtx.scale(dpr, dpr)
          this.setData({
            watermarkCanvas,
          })
        })
    },
    /** 点击进度条触发 */
    onChangeProgress(e) {
      const { value } = e.detail
      const second = videoDuration * (value / 100)
      this.setData({
        videoTime: wx.$.u.transformationTime(second),
        progress: value,
      })
      this.data.videoCtx.seek(second)
    },
    /** 获取布局信息，设置包裹video标签的view要展示的高度 */
    initLayout() {
      const videoCtx = wx.createVideoContext('upload-video')
      /** 获取cameraCircle宽度 */
      wx.createSelectorQuery()
        .select('#video-upload-btn')
        .boundingClientRect((rect) => {
          if (rect) {
            this.setData({
              videoCtx,
              videoHeight: windowHeight - rect.height - (statusBarHeight || 0),
            })
          }
        })
        .exec()
    },
    onLoad(query) {
      // 每次进页面都要重置该值，防止缓存
      const { videoType, source, videoPath, entryId, appId, thumbPath, direction, entryIdVideo } = query
      let noticeText = '视频拍摄完成，你可以播放预览，满意后请点击【立即使用】，不满意请点击【重新录制】'
      if (videoType === '1') {
        noticeText = '视频拍摄完成，你可以播放预览，满意后请点击【立即上传】，不满意请点击【重新录制】'
      } else if (source === '0') {
        noticeText = '视频拍摄完成，你可以播放预览，满意后请点击【保存视频】，不满意请点击【重新录制】'
      }
      this.setData({
        noticeText,
        videoType,
        entryId: entryId || 1004,
        entryIdVideo: entryIdVideo || null,
        appId: appId || 102,
        pageSource: source || '1',
        videoPath: decodeURIComponent(videoPath),
        thumbPath,
      })
      videoDuration = direction || 0
      this.initLayout()
    },
    /** 关闭【面试视频】上传视频弹窗 */
    onCloseUpload() {
      this.setData({
        showProgressModal: false,
      })
    },
  }),
)

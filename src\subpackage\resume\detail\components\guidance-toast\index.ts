Component(class GuidanceToast extends wx.$.Component<GuidanceToast> {
  properties = {
    visible: {
      type: Boolean,
      value: true,
    },
    duration: {
      type: Number,
      value: 10000,
    },
  }

  pageLifetimes = {
    show() {
      this.onVisible()
    },

  }

  timer: any

  lifetimes = {
    attached() {
      this.onVisible()
    },
  }

  onVisible() {
    console.log('GuidanceToast:onVisible', this.data.visible)
    if (this.data.visible) {
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.setData({ visible: false })
      }, this.data.duration)
    }
  }

  observers: Record<string, (value: any) => void> = {
    visible() {
      this.onVisible()
    },
  }
})

<drawer wx:if="{{loadedDrawerComp}}" zIndex="{{zIndex}}" isMaskClose="{{false}}" visible="{{visible}}" bind:hide="collapsedDrawer">
  <view class="wrap">
    <view class="wrapHeader">
      <m-tab tabs="{{ tabs }}" activeTab="{{activeTab}}" bind:tabChange="onTabChange" isInitTrigger customStyle="height:88rpx;margin:0 64rpx;background:transparent;" normalStyles="font-size:34rpx;" underlineStyles="height:6rpx" />
    </view>
    <image class="topCloseIcon" data-mark="手动关闭" bind:tap="onClose" src="https://cdn.yupaowang.com/yp_mini/images/zb/yp-mini_close_icon.png" />
    <scroll-view scroll-y="{{true}}" class="warpContent">
      <IntroduceView wx:if="{{activeTab==1}}"></IntroduceView>
      <EvaluateView wx:elif="{{activeTab==2}}"></EvaluateView>
      <RulesView wx:else="{{activeTab==3}}"></RulesView>
    </scroll-view>
  </view>
</drawer>

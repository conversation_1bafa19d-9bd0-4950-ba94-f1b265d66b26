/*
 * @Author: ji<PERSON><PERSON>
 * @LastEditors: wyl <EMAIL>
 */
/** 用户信息数据 */
type IBaseInfo = {
  /** 姓名 年龄 名族 */
  userStr: '',
  /** 工资和找活状态 */
  resumeInfo: '',
  /** 工种信息 */
  classifyStr: '',
} | null

/** 处理baseInfo数据 */
export function getBaseInfo(detail): IBaseInfo {
  let baseInfo = null
  if (!detail || !detail.userInfoResp || !detail.basicResp) {
    return baseInfo
  }

  try {
    const { userInfoResp, basicResp } = detail
    baseInfo = { userStr: '', resumeInfo: '', classifyStr: '' }
    const age = userInfoResp.age ? `${userInfoResp.age}岁` : ''

    if (detail.name) {
      baseInfo.userStr = [detail.name, age, userInfoResp.nation].filter(text => !!text).join(' · ')
    }
    if (basicResp.subs && basicResp.subs.length) {
      baseInfo.resumeInfo += `${basicResp.subs[0].salaryExpectation || ''}`
      baseInfo.classifyStr = `${basicResp.subs[0].occupationInfo.occName}`
    }

    if (basicResp.workStatus == 1) {
      if (baseInfo.resumeInfo) {
        baseInfo.resumeInfo += ' ｜ '
      }
      baseInfo.resumeInfo += '随时进场'
    }
  } catch (error) { }

  return baseInfo
}

/** 刷新找活详情 */
export function refreshDetail() {
  const { page } = wx.$.r.getPrevPage(2) as any
  if (page && page.route == 'subpackage/resume/detail/index') {
    page.init && page.init()
  }
}

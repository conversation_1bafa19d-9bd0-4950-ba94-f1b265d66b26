<!-- 基本信息 -->
<view class="card">
  <view class="card-container">
    <view class="resume-header">
      <view class="header-left">
        <view class="header-left-top">
          <text class="name">{{nameText}}</text>
          <!-- <view wx:if="{{detail.realNameStatus}}" class="real-name-tag" catch:tap="onShowModal">已实名</view> -->
          <view wx:if="{{detail.viewed}}" class="text">已联系</view>
        </view>
        <view class="header-left-bottom">{{detail.userDesc||query.sex||"男"}}</view>
      </view>
      <view class="header-right">
        <image class="image {{maskImage ? 'filter' : ''}}" mode='aspectFill' src="{{detail.avatar||query.avatar||'https://cdn.yupaowang.com/yupao_mini/yp_mini_zp_head_photo.png'}}" />
      </view>
    </view>
  </view>
</view>
<!-- 找工作状态 -->
<view class="card">
  <view class="card-container">
    <view class="status-block">
      <view class="status-block-header">
        <view class="subtitle">{{detail.workStatusTxt}}</view>
        <!-- 已查看且不来自于whoContacted页面才展示投诉 -->
        <view wx:if="{{detail.isShowComplainedBtn &&  query.sourceType !== 'whoContactedMeToResume'}}" class="tag {{detail.isComplained? 'grey' : ''}}" bind:tap="onComplain">
          <image class="icon-img" mode='aspectFill' src="{{detail.isComplained ? 'https://cdn.yupaowang.com/yupao_mini/yp_mini_gray_tan3x.png' : 'https://cdn.yupaowang.com/yupao_mini/yp_mini_tan3x.png'}}" />
          <view>{{detail.isComplained?'已投诉':'投诉'}}</view>
        </view>
        <view wx:else class="head-tag-box">
          <block wx:if="{{isActiveAb}}">
            <block wx:for="{{detail.userInfoResp.activeLabels}}" wx:key="index" wx:for-item="obj">
              <!-- 只展示一个 -->
              <view wx:if="{{index == 0}}" class="head-tag tag-{{obj.color}}">
                <view wx:if="{{obj.labelCode == 13}}" class="tag-dot"></view>
                <view>{{obj.labelName}}</view>
              </view>
            </block>
          </block>
          <block wx:if="{{!isActiveAb}}">
            <view wx:if="{{detail.online == 2}}" class="head-tag tag-green">
              <view class="tag-dot"></view>
              <view>在线</view>
            </view>
            <block wx:elif="{{detail.activeStatusText}}">
              <view class="head-tag">{{detail.activeStatusText}}</view>
            </block>
          </block>
        </view>
      </view>
      <view wx:if="{{myBrowseJobCount || browseMeResumeCount}}" class="status-block-tags">
        <view wx:if="{{myBrowseJobCount}}" class="tag">近期查看职位{{myBrowseJobCount}}次</view>
        <view wx:if="{{browseMeResumeCount}}" class="tag">近期被查看{{browseMeResumeCount}}次</view>
      </view>
    </view>
  </view>
</view>
<!-- 求职期望 -->
<view class="card" wx:if="{{detail.subs.length}}">
  <view class="card-container">
    <view class="card-subtitle">求职期望</view>
    <view class="job-expect-block">
      <view class="list">
        <view class="list-item" wx:for="{{[detail.subs[0]]}}" wx:key="index">
          <view class="list-item-header">
            <view class="text">{{item.positionTypeTxt?item.positionTypeTxt+" · ":""}}{{item.occupationInfo.occName}}</view>
            <view class="highlight-text">{{item.salaryExpectation||""}}</view>
          </view>
          <view class="list-item-tags" wx:if="{{item.tags.length}}">
            <view class="tag-box {{!isShowMore ? 'moreheight' : ''}}" id="tag-box">
              <view class="tag-cover" id="tag-cover">
                <view class="tag" wx:for="{{item.tags}}" wx:key="index">{{item}}</view>
              </view>
            </view>
            <view class="more-box" wx:if="{{isShowMore}}" bind:tap="onClickMore">
              <text class="text">展开</text>
              <icon-font type="yp-icon_tab_sx_nor" size="32rpx" color="rgba(0, 0, 0, 0.45)" />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
<view class="card" wx:if="{{detail.hopeAreaTxt}}">
  <view class="card-container">
    <view class="card-subtitle">工作城市</view>
    <view class="hope-area-text">{{detail.hopeAreaTxt}}</view>
  </view>
</view>
<view class="card" wx:if="{{detail.introduce}}">
  <view class="card-container">
    <view class="card-subtitle">个人优势</view>
    <view class="introduce {{showAllIntroduce?'show-all':''}}" id="introduce">
      <view class="text-pre" id="introduce-content">{{detail.introduce}}</view>
      <view class="expand" wx:if="{{showExpandBtn}}" bind:tap="handleShowAllIntroduce">查看全部</view>
    </view>
  </view>
</view>
<view class="card" wx:if="{{detail.workExpResp.length}}">
  <view class="card-container">
    <view class="card-subtitle">工作经历</view>
    <view class="work-experience">
      <view class="list">
        <block wx:for="{{detail.workExpResp}}" wx:key="uuid">
          <view class="list-item {{(index > 2 && !showExpandAllExp) ? 'list-item-hide' : ''}}">
            <view class="list-item-header">
              <view class="text">{{item.companyName}}</view>
              <view class="secondary-text">{{item.timeStr}}</view>
            </view>
            <view class="list-item-text" wx:if="{{item.occName||item.departmentName}}">
              {{item.occName}}
              <block wx:if="{{item.departmentName}}">{{(item.occName?" · ":"")+item.departmentName}}</block>
            </view>
            <view class="list-item-desc" wx:if="{{item.jobPerformance||item.jobContent}}">
              <block wx:if="{{maskAndVisible.mask.workExp}}">
                <image src="https://cdn.yupaowang.com/yupao_common/ff36b9b4.png" class="job-exp-mask" mode="aspectFill" />
              </block>
              <block wx:else>
                <block wx:if="{{item.jobPerformance}}">
                  <view class="full-width">业绩：</view>
                  <view class="text-pre">{{item.jobPerformance}}</view>
                </block>
                <block wx:if="{{item.jobContent}}">
                  <view class="full-width">内容：</view>
                  <view class="text-pre">{{item.jobContent}}</view>
                </block>
              </block>
            </view>
          </view>
        </block>
      </view>
      <view class="bottom-btn" wx:if="{{detail.workExpResp.length>3&&!showExpandAllExp}}" bind:tap="handleShowAllExp">展开全部{{detail.workExpResp.length}}个工作经历</view>
    </view>
  </view>
</view>
<view class="card" wx:if="{{detail.projectExpResp.length}}">
  <view class="card-container">
    <view class="card-subtitle">项目经历</view>
    <view class="project-exp-card" wx:for="{{detail.projectExpResp}}" wx:key="uuid">
      <detail-work-card-view item="{{item}}" isHiddenMode="{{maskAndVisible.mask.projExp}}"></detail-work-card-view>
    </view>
  </view>
</view>
<view class="card" wx:if="{{detail.eduExpResp.length}}">
  <view class="card-container">
    <view class="card-subtitle">教育经历</view>
    <view class="edu-experience">
      <view class="list">
        <view class="list-item" wx:for="{{detail.eduExpResp}}" wx:key="uuid">
          <view class="list-item-header">
            <block wx:if="{{maskAndVisible.mask.edu}}"><image src="https://cdn.yupaowang.com/yupao_common/3ce1fbac.png" class="school-name-mask" mode="aspectFill" /></block>
            <view class="text" wx:else>{{item.schoolName}}</view>
            <view class="secondary-text">{{item.timeStr}}</view>
          </view>
          <view class="list-item-text" wx:if="{{(item.majorName||item.eduBackgroundTxt) && !maskAndVisible.mask.edu}}">
            {{item.majorName}}
            <block wx:if="{{item.eduBackgroundTxt}}">{{(item.majorName?" · ":"")+item.eduBackgroundTxt}}</block>
          </view>
          <view class="list-item-desc" wx:if="{{item.experience&& !maskAndVisible.mask.edu}}">{{item.experience}}</view>
        </view>
      </view>
    </view>
  </view>
</view>
<view class="card" wx:if="{{detail.userCertificateInfos.length}}">
  <view class="card-container">
    <view class="card-subtitle">资格证书</view>
    <block wx:if="{{maskAndVisible.mask.cert}}"><image src="https://cdn.yupaowang.com/yupao_common/6330573b.png" class="cates-list-mask" mode="aspectFill"/></block>
    <view class="list-item-cates-tags" wx:else>
      <view class="tag-box {{!isShowMoreCates ? 'moreheight' : ''}}" id="tag-cates-box">
        <view class="tag-cover" id="tag-cates-cover">
          <view class="tag" wx:for="{{detail.userCertificateInfos}}" wx:key="index">{{item.certificateName}}</view>
        </view>
      </view>
      <view class="more-box" wx:if="{{isShowMoreCates && detail.userCertificateInfos.length > 0}}" bind:tap="onClickMoreCates">
        <text class="text">展开全部{{detail.userCertificateInfos.length}}个资格证书</text>
      </view>
    </view>
  </view>
</view>
<view class="card" wx:if="{{detail.videoResp && !maskAndVisible.hidden.video}}">
  <view class="card-container">
    <view class="card-subtitle no-dot">面试视频</view>
    <view class="video">
      <view class="content">
        <view class="interview-video" bind:tap="onClickVideo">
          <image class="play-btn" src="https://staticscdn.zgzpsjz.com/miniprogram/images/ygd/yp-mini_icon-video-btn.png" />
          <image class="cover-img" mode="aspectFill" src="{{detail.videoResp.attach.cover}}"></image>
          <image class="video-poster" src="{{detail.videoResp.attach.cover}}" />
          <view class="video-duration">{{detail.videoResp.duration}}s</view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 勿删--解决兼容性问题 -->
<view class="hidden-box" id="tag-stabilization">
  <view class="tag">占位数据勿删占位数据勿删占位数据勿删</view>
  <view class="tag">占位数据勿删占位数据勿删占位数据勿删</view>
  <view class="tag">占位数据勿删占位数据勿删占位数据勿删</view>
  <view class="tag">占位数据勿删占位数据勿删占位数据勿删</view>
</view>

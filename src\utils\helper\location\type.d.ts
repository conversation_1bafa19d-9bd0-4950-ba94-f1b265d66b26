/*
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-01-05 16:33:40
 * @Description:
 */

export interface LocationDataType {
  address: string
  city: string
  province: string
  provinces: string
  provinces_txt?: string
  name: string
  location: string
  district: string
  streetName: string
  adcode?: string | number
  citycode?: string
  oadcode?: string
  longitude?: string
  latitude?: string
  wardenryid?: string
  regionone?: string
  current_area?: string
  formattedAddress?: string
  errCode?: string
}

// 获取全部城市数据
export interface AllAreasDataItem {
  ad_code: string | number
  id: string | number
  name: string
  children?: AllAreasDataItem[]
  ad_name?: string
  city?: string
  level?: number
  parents?: CityUseData[]
  [key: string]: any
}

// 城市使用数据
export interface CityUseData {
  /** 城市id */
  id: string
  /** 城市名称 */
  name: string
  /** 城市letter */
  letter?: string
}

/** 高德返回的tips列表中对象的数据结构 */
export interface InputPoiListTips {
  /** 地区adcode */
  adcode: string
  /** 详细地区 */
  address: string
  /** 详细地址 */
  district: string
  /** 经纬度信息 */
  location: string
  /** 地区名字 */
  name: string
  /** 距离 */
  distance: string
  /** 城市名称 */
  cityName: string
  areaId?: string
  ad_name?: string
  id?: string
}

/** 获取关键词地区列表的数据 */
export interface InputPoiList {
  // 高德返回的 在他的data下面有一层tips包裹
  tips: InputPoiListTips[]
}
/** 省市区 */
export interface ITreeArea extends ILocation.ITreeArea {
  [key in string]: ''
}

interface AreasDataItem {
  id: string | number
  name: string
  pid: string
  children: []
  ad_name?: string
  letter?: string
}
/** 省市区 */
export interface IFetchTreeArea {
  /** 省份信息 */
  province: AreasDataItem | ''
  /** 城市信息 */
  city: AreasDataItem | ''
  /** 地区信息 */
  district: AreasDataItem | ''
  /** 当前地址信息 */
  current: AreasDataItem | ''
}

/** 方法的option：里边的值必须与高德的ad_name匹配 */
export interface IAdNamesOption {
  /** 省的ad_name -【必填】 */
  provinceName: string
  /** 城市的ad_name -【可填】 */
  cityName?: string
  /** 地区的ad_name -【可填】 */
  districtName?: string
}

/** 搜索周边地址: https://lbs.amap.com/api/webservice/guide/api/inputtips */
export type IParamsInputtips = {
  /** 搜索的关键字 */
  keywords: string
  /** 搜索城市的adcode */
  adcode?: string | number
  /** 仅返回指定城市数据 */
  citylimit?: boolean
  /** 经纬度坐标。设置该参数会在此 location 附近优先返回关键词信息。 */
  location?: string
  [key: string]: unknown
}

/** 获取地区信息 */
export type ILocationCity = {
  /** 城市adcode */
  adcode: string,
  /** 当前地址详情 */
  address: string,
  /** 城市全名（成都市） */
  cityCompleteName: string,
  /** 省全名（四川省） */
  provinceCompleteName: string,
  /** 当前定位的地址名 */
  name: string,
  /** 城市id */
  cityId: number,
  /** 省id: 26 */
  provinceId: number,
  /** 城市名: 成都 */
  cityName: string,
  /** 省份名 四川 */
  provinceName: string,
  /** 区域名: 武侯区 */
  district: string,
  /** 经度 */
  longitude: string,
  /** 纬度 */
  latitude: string,
  /** 经纬度 */
  location: string,
}

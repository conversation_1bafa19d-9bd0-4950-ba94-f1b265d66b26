/*
 * @Date: 2022-01-29 10:01:03
 * @Description: 充值 - 支付相关
 */

import miniConfig from '@/miniConfig/index'
import { store } from '@/store/index'
import dayjs from '@/lib/dayjs/index'
import { login } from '@/utils/helper/index'
import { isIos } from '@/utils/tools/validator/index'
import { tryPromise } from '../tools/common/index'
import { toJSON } from '../tools/formatter/index'

let timer = null
/**
 * 订单检测
 * @param {string} order_no 订单号
 * @param {Function} cb 成功回调
 * @param {Function} collectTimer 收集订单轮询timer, 轮询时点击页面返回清空
 */
const checkOrder = (order_no: string, cb: Function, collectTimer: Function) => {
  clearInterval(timer)
  timer = setInterval(() => {
    checkRun({ order_no }, cb)
  }, 3000)
  collectTimer(timer)
  checkRun({ order_no }, cb)
}

/** 检测订单状态 */
const checkRun = async (options, cb) => {
  const res = await wx.$.fetch['GET/job/integral/checkPaySuccess'](options, { hideErrCodes: true, hideMsg: true })
  if (res?.data?.errcode !== 'not_pay') {
    clearInterval(timer)
    cb()
  }
}

/** 清除timer, 抛出去使用 */
export const clearSwanCheckRun = () => {
  clearInterval(timer)
}

/** 生成支付的token */
const createToken = () => {
  return Number(`${dayjs().unix()}${randomNum()}`)
}

/** 生成置顶位数的随机数 */
const randomNum = (n = 4) => {
  let t = ''
  for (let i = 0; i < n; i += 1) {
    t += Math.floor(Math.random() * 10)
  }
  return t
}

/** 工行支付 pageType区分是不是中间页调接口,1不是中间页，2是中间页 */
export const icbcRecharge = (id: number, collectTimer: Function, logiticsVal?: any, type?: string, ext?: any, options?, pageType?: number | string) => {
  return new Promise(async (resolve, reject) => {
    if (ENV_IS_WEAPP) {
      let openid = store.getState().storage.loginAuthData?.openid
      let equityData = []
      let runtime = ''
      /** 没有openid就重新获取 */
      if (!openid) {
        const result: any = await login.getWechatAuthInfo()
        openid = result?.openid
      }
      const { bizType, payType, token, orderNo, orderinfo, version, goodsType, pay_device_id, pagesLength, price } = options || {}
      if (pageType == 1) {
        runtime = 'WX_MINI'
      } else if (pageType == 2) {
        if (options?.payUrl) {
          runtime = 'WX_MINI'
        } else {
          // eslint-disable-next-line no-lonely-if
          if (isIos()) {
            runtime = 'IOS'
          } else {
            runtime = 'ANDROID'
          }
        }
      }

      if (options.extInfo && options.extInfo.indexOf('object') != -1) {
        return wx.$.msg('支付路由参数错误')
      }
      let params: any = {
        payChannel: 'WEIXIN',
        payProductChannel: 'JSAPI',
        bizType: bizType || 'YUPAO_POINTS_CHARGE', // 业务类型
        payType: payType || 'ONLINE_PAY', // 支付类型
        goodsCount: '1',
        isGroupPurchase: false,
        goodsCode: id,
        extInfo: {
          ...(toJSON(decodeURIComponent(options.extInfo || '') || {})),
          openId: openid,
          appId: miniConfig.appid,
          runtime,
          ...ext,
        },
        /** 充值项的id */
        /** 支付类型： 微信 - wechat , 支付宝 - alipay , 小程序 - mini */
        // mch_type: 'mini',
        // /** 用户的OpenId */
        // openid,
        // /** 小程序的appid */
        // appid: miniConfig.appid,
      }
      // 工行支付优化新增-->有提前获取订单号的场景传入
      if (orderNo) {
        params.orderNo = orderNo
      }
      // * 如果是物流会员购买
      if (type == 'logitics') {
        params = {
          /** 支付类型： 微信 - wechat , 支付宝 - alipay , 小程序 - mini */
          mch_type: 'mini',
          /** 用户的OpenId */
          openid,
          ...logiticsVal,
        }
      }
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 1]
      let odata
      if (orderinfo && orderinfo.extInfo) {
        odata = { data: orderinfo }
      } else {
        const headers = {
          token,
          appVersion: version,
          deviceId: pay_device_id,
        }
        !token && delete headers.token
        !version && delete headers.appVersion
        !pay_device_id && delete headers.deviceId

        if (options && options.isPrePay) {
          // 去除多余参数
          delete params.goodsCount
          delete params.isGroupPurchase
          delete params.goodsCode
          odata = await wx.$.javafetch['POST/trade/v2/order/prePay'](params, { headers })
        } else if (goodsType == 'MARKETING') {
          odata = await wx.$.javafetch['POST/trade/v2/order/preCreateOrder'](params, { headers })
        } else {
          odata = await wx.$.javafetch['POST/trade/v1/goods/createOrder'](params, { headers })
        }


        if (params.goodsCode && headers.token && pagesLength == 1 && bizType != 'YUPAO_POINTS_CHARGE') {
          try {
            // if (params.goodsCode) {
            const querySkuAttr = await wx.$.javafetch['POST/marketGateway/v1/marketGoods/querySkuAttr']({ goodsCode: params.goodsCode }, { hideMsg: true, headers }).catch((err) => err)
            if (querySkuAttr?.code == 0) {
              equityData = getEquityData(querySkuAttr.data.skuAttrList)
            }
          } catch (error) {

          }
        }
        prevPage.setData({
          equityData,
          payData: odata.data
        })
        if (![200, 0].includes(odata.code)) {
          if (options && options.payProductChannel == 'APP' && odata && (odata.code == 19020068 || odata.code == 19020100)) {
            wx.$.msg('支付失败，请返回重新尝试')
          } else {
            wx.$.msg('支付失败,请5S后重新尝试')
          }
          wx.$.collectEvent.event('icbcPayReturn', { time: dayjs().valueOf(), status: 'fail', orderNo, err: odata, price })
          reject({ ...odata, payData: odata, equityData })
          return
        }
        if (options && options.createOrderCallBack) {
          options.createOrderCallBack(odata ? odata.data : {})
        }
        // app到小程序支付流程判断走流程1还是流程2（是否自动拉起支付）
        if (headers.token && pagesLength == 1) {
          // if (true) {
          const Pdata = await wx.$.javafetch['POST/trade/v1/order/queryPaymentProcess']({}, { headers })
          if (Pdata.code == 0 && !Pdata.data.result) {
            wx.hideLoading()
            resolve({ type: 2 })
            return
          }
        }
      }

      wx.hideLoading()

      wx.requestPayment({
        ...odata?.data?.extInfo,
        success(res) {
          wx.$.collectEvent.event('icbcPayReturn', { time: dayjs().valueOf(), status: 'success', orderNo, price })
          resolve({ ...res })
        },
        async fail(err) {
          wx.$.collectEvent.event('icbcPayReturn', { time: dayjs().valueOf(), status: 'fail', orderNo, err, price })
          if (pageType == 1 && err.errMsg == 'requestPayment:fail cancel') {
            wx.$.javafetch['POST/trade/v1/order/cancelOrder']({ orderNo: odata.data.orderNo }).then(() => {
              reject({ ...err })
            }).catch(() => {
              reject({ ...err })
            })
          } else {
            reject({ ...err })
          }
        },
      })
    } else if (ENV_IS_SWAN) { // 百度
      let params: any = {
        /** 充值项的id */
        id,
        /** 支付类型： 微信 - wechat , 支付宝 - alipay , 小程序 - mini */
        mch_type: 'baidu',
        /** 小程序的appid */
        // appid: miniConfig.appid,
        /** token */
        token: createToken(),
      }
      // * 如果是物流会员购买
      if (type == 'logitics') {
        params = {
          /** 支付类型： 微信 - wechat , 支付宝 - alipay , 小程序 - mini */
          mch_type: 'baidu',
          /** 小程序的appid */
          // appid: miniConfig.appid,
          /** token */
          token: createToken(),
          ...logiticsVal,
        }
      }
      // 优惠卷ID
      if (ext && ext.voucherId) {
        params.voucher_id = ext.voucherId
      }
      const { data, head } = await tryPromise(wx.$.fetch['POST/job/integral/pay'](params), { data: {}, head: {} })
      if (head.code != 200) {
        wx.$.msg('发起支付失败,请联系客服或稍后重试')
        reject()
        return
      }
      wx.requestPolymerPayment({
        orderInfo: {
          ...data.payData,
        },
        success(res) {
          // 检验百度支付是否成功，每3秒发起一次 直到成功
          checkOrder(data.order_no, resolve.bind(null, res), collectTimer)
        },
        fail(err) {
          reject(err)
        },
      })
    }
  })
}

const getEquityData = (data = []) => {
  const equityData = []
  data.map((item) => {
    let itemData = { name: '', value: 0, unit: '' }
    if (item.businessType == 'YUPAO_VIP') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.views, unit: '次' }
    } else if (item.businessType == 'YUPAO_SUB_CARD') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.views, unit: '次' }
    } else if (item.businessType == 'YUPAO_C_REFRESH_VIP') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.refreshTimes, unit: '次' }
    } else if (item.businessType == 'YUPAO_C_REFRESH_CARD') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.refreshTimes, unit: '次' }
    } else if (item.businessType == 'MASS_RMT') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.numberOfSenders, unit: '人' }
    } else if (item.businessType == 'YUPAO_URGENT_HIRING') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.numberOfTimes, unit: '人' }
    } else if (item.businessType == 'YUPAO_JOB_TOP') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.rightsDays, unit: '天' }
    } else if (item.businessType == 'JOB_VIE_CARD') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.numberOfTimes, unit: '天' }
    } else if (item.businessType == 'JOB_VIE_VIP') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.totalLimit, unit: '个' }
    } else if (item.businessType == 'YUPAO_B_VISIT_CARD') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.numberOfTimes, unit: '次' }
    } else if (item.businessType == 'YUPAO_B_PUBLISH_CARD') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.numberOfTimes, unit: '次' }
    } else if (item.businessType == 'YUPAO_B_REFRESH_CARD') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.numberOfTimes, unit: '次' }
    } else if (item.businessType == 'YUPAO_B_CHAT_CARD') {
      itemData = { name: item.skuTypeName, value: Number(item.skuAttrConfig.dailyLimit) * Number(item.skuAttrConfig.rightsDays), unit: '次' }
    } else if (item.businessType == 'JOB_TOP_PROVINCE_CARD') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.numberOfTimes, unit: '天' }
    } else if (item.businessType == 'JOB_TOP_CITY_CARD') {
      itemData = { name: item.skuTypeName, value: item.skuAttrConfig.numberOfTimes, unit: '天' }
    }

    itemData.name && equityData.push(itemData)
  })
  console.log('equityData', equityData)
  return equityData
}

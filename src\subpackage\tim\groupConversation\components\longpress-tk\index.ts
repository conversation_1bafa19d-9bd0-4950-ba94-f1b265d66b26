/*
 * @Date: 2022-02-09 11:02:56
 * @Description:
 */

import { store, dispatch, actions, MapStateToData, connect } from '@/store/index'

const mapStateToData: MapStateToData = (state) => {
  const { timmsg } = state
  return {
    longpressid: timmsg.longpressid,
    curConImages: timmsg.curConImages,
  }
}
Component(
  connect(mapStateToData)({
    properties: {
      msgInfo: {
        type: Object,
        value: {},
      },
      tkWith: {
        type: Number,
        value: 0,
      },
      isCopy: {
        type: Boolean,
        value: true,
      },
    },
    data: {
      isRevoke: true,
      left: -500,
      arrowLeft: 0,
      isShowTk: true,
    },
    observers: {
      longpressid(longpressid) {
        const { msgInfo } = this.data
        if (longpressid && `${msgInfo.id}_tk` != longpressid) {
          this.setData({ isShowTk: false })
        } else if (longpressid && `${msgInfo.id}_tk` == longpressid && !this.data.isShowTk) {
          this.setData({ isShowTk: true })
        }
      },
    },
    lifetimes: {
      ready() {
        const nowTime = new Date().getTime()
        const { msgInfo } = this.data
        const { time } = msgInfo || {}
        const msgTime = time * 1000
        const diff = (nowTime - msgTime) / 1000
        let isRevoke = true
        if (diff > 120 || !this.data.msgInfo.isSelf) {
          isRevoke = false
        }
        this.setData({ isRevoke })
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const that = this
        setTimeout(() => {
          that
            .createSelectorQuery()
            .select(`#${that.data.msgInfo.id}_tk_vv`)
            .boundingClientRect()
            .exec((rect) => {
              if (rect[0]) {
                const tkvWidth = rect[0].width
                const arrowLeft = tkvWidth / 2 - 6
                if (tkvWidth > that.data.tkWith) {
                  that.setData({ left: -tkvWidth / 2 + that.data.tkWith / 2, arrowLeft })
                } else {
                  that.setData({ left: that.data.tkWith / 2 - tkvWidth / 2, arrowLeft })
                }
              }
            })
        }, 100)
      },
    },
    methods: {
      onClick(e) {
        const { type } = e.target.dataset
        this.triggerEvent('click', { type })
        switch (type) {
          case 'copy':
            break
          case 'revoke':
            this.onRevoke()
            break
          case 'del':
            break
          default:
            break
        }
      },
      onRevoke() {
        const { msgInfo } = this.data
        const { ID } = msgInfo || {}
        const message = wx.$.tim.findMessage(ID)
        if (!wx.$.u.isEmptyObject(message)) {
          const promise = wx.$.tim.revokeMessage(message)
          promise
            .then(() => {
              this.saveRevokeMsgList()
            })
            .catch((iErr) => {
              // wx.$.msg('撤回失败,请稍后重试')
            })
        }
      },
      // 撤回消息
      saveRevokeMsgList() {
        const { msgInfo } = this.data
        wx.$.l.saveRevokeMsgList(msgInfo, {
          success: () => {
            this.removeImages(msgInfo)
          },
        })
      },
      // 删除预览图片
      removeImages(msg) {
        const { curConImages } = this.data
        const { payload } = msg || {}
        const { imageInfoArray } = payload || {}
        if (wx.$.u.isArrayVal(imageInfoArray)) {
          const nCurConImages = curConImages.filter((item) => item != (imageInfoArray[0].url || imageInfoArray[0].imageUrl))
          dispatch(actions.timmsgActions.setState({ curConImages: nCurConImages }))
        }
      },
    },
  }),
)

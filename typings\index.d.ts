/*
 * @Date: 2021-12-31 16:03:32
 * @Description: ts 类型定义
 */


/// <reference path="../src/lib/mini/core.d.ts" />
/// <reference path="../src/components/widget/modal/core/config.d.ts" />
/// <reference path="./ComponentOptions.d.ts" />
/// <reference path="./PageOptions.d.ts" />
/// <reference path="../src/services/request.base.d.ts" />
/// <reference path="./MiniConfig.d.ts" />
/// <reference path="./wx/index.d.ts" />
// declare namespace WechatMiniprogram {
//   interface Wx {
//     $: $Type
//   }
// }

/** 常用类型 */
declare type CurrentPages = ReturnType<WechatMiniprogram.Page.GetCurrentPages>

/** 全局方法 */
declare const App: (options: Record<string, any>) => void
declare let Page: (options: PageOptions | Class) => void
declare const getApp: () => any
declare let Component:(options: ComponentOptions) => void
declare const getCurrentPages: WechatMiniprogram.Page.GetCurrentPages
declare const Behavior: (options: Record<string, any>) => void

interface NodeRequire {
  async: Function
}

/**
 *  提取 T.properties 的类型
    type T = typeof this;
    提取 T.properties 的类型
    type PropertiesType = T['properties'];
    利用映射类型来生成 IProperties 类型
    type IProperties = {
      [K in keyof PropertiesType]: PropertiesType[K]['value'];
    };
 */
/** store */
type FactoryStore<T> = T extends { useStore }
  ? ReturnType<T['useStore']>
  : {};

/** 属性 */
type ToPrimitive<T> =
  T extends Array<any> ? any[] :
  T extends { valueOf(): infer U } ? U : T;
type FactoryProperties<T> = T extends { properties: infer P }
  ? {
      [K in keyof P]: P[K] extends { type: infer V } ? ToPrimitive<InstanceType<V>>: never;
    }
  : {};

/** data数据 */
type FactoryData<T> = T extends { data: infer D }
  ? {
    [K in keyof D]: D[K];
  }
  : {};

/** DataTypes: 页面和组件的对象类型提示 */
type DataTypes<T> = FactoryData<T>
  & FactoryProperties<T>
  & FactoryStore<T>
  & Record<string, any>

/** 请求超时时间-默认一分钟(60000)，微信官方也是一分钟，以毫秒为单位 */
declare const ENV_REQUEST_TIMEOUT: string

/** 微信小程序环境 */
declare const ENV_IS_WEAPP: boolean

/** 百度小程序环境 */
declare const ENV_IS_SWAN: boolean

/** 字节小程序环境 */
declare const ENV_IS_TT: boolean

/** 调试 | 打包 */
declare const ENV_MODE: 'dev' | 'build'

/** 开发站 ｜ 测试站 | 预发布 | 预发布正式站 | 正式站 */
declare const ENV_DEVELOPMENT: 'DEVELOP' | 'DEV' | 'DEV2' | 'PRE' | 'PRE2' | 'REL' | 'PRO'

/** 微信 | 百度 | 字节 */
declare const ENV_MINI_TYPE: 'weapp' | 'swan' | 'tt'

/** 运行的是哪个小程序包 */
declare const ENV_SUB: string

/** 将指定的属性改变为必选的属性 */
declare type RequiredProp<T, K extends keyof T> = T & {
  [P in K]-?: T[P]
}

/** 地址数据 */
declare namespace ILocation {
  /** @name 地址数据 */
  interface TAreaData {
    /** 地址名 */
    name: string,
    /** 地址唯一id */
    id: number | string,
    /** 地址编码 */
    ad_code: string,
    /** 地址父级id */
    pid: number | string,
    /** 地址等级 */
    level: number,
    /** 地址拼音 */
    letter: string,
    /** 地址全名 */
    ad_name: string
    /** 地址全名的首字母 */
    initials: string
    /** 子集id集合字符串，以逗号分隔 */
    subIdStr?: string
    /** 子集数据 */
    children?: TAreaData[]
  }

  /** 省市区 */
  declare interface ITreeArea {
    /** 省份信息 */
    province: TAreaData | ''
    /** 城市信息 */
    city: TAreaData | ''
    /** 地区信息 */
    district: TAreaData | ''
    /** 当前地址信息 */
    current: TAreaData | ''
    /** 地区是否属于 region 直辖市，hmt 港澳台，''其他
     * - `region`: 直辖市: 北京，上海，天津，重庆
     * - `hmt`: 特别行政区: 港澳台
     * - `''`: 其他 空字符串
     * */
    special: 'hmt' | 'region' | ''
  }
}

/**
 * 小程序绑定的点击事件
 * @param 第一个参数是 target 或者 currentTarget 的类型
 * @param mark 的类型
 */
declare type EventType<T extends Object = {}, M extends Object = {}> = WechatMiniprogram.BaseEvent<M, T, T>

/**
 * 自定义事件
 * @param 自定义参数 detail 里面的类型
 * @param 第二个参数是 target 或者 currentTarget 的类型
 * @param mark 的类型
 */
declare type CEvent<D extends Object = {} , T extends Object = {}, M extends Object = {}> = WechatMiniprogram.CustomEvent<D, M, T, T>

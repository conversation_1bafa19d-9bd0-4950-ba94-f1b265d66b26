type sources = WechatMiniprogram.PreviewMediaOption['sources']

/** 预览图片和视频 */
export default function previewMedia(sources: sources, currentIndex = 0) {
  if (!Array.isArray(sources) || sources.length < 1) {
    return
  }
  if (ENV_IS_WEAPP) {
    // 微信环境使用previewMedia查看
    wx.previewMedia({
      sources,
      current: currentIndex,
    })
    return
  }
  const item = sources[currentIndex]
  if (item.type === 'image') {
    const img = this.data.value.filter((item) => item.type === 'image').map((item) => item.path || item.tempFilePath)
    wx.previewImage({
      urls: img,
      current: item.url,
    })
  } else {
    /** 预览视频 */
    wx.$.r.push({
      path: '/subpackage/video/video_others/index',
      params: {
        pageSource: 'normal',
        item: {
          videoUrl: item.url,
          videoCoverImg: item.poster,
        },
      },
    })
  }
}

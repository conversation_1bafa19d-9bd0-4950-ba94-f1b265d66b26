/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-04-20 17:34:56
 * @FilePath: /yp-mini-two/start/cicd.js
 * @Description:
 */
const fs = require('fs')
const ci = require('miniprogram-ci')
const inquirer = require('inquirer')
const { spawn } = require('cross-spawn')
const chalk = require('./chalk')
const miniConfig = require('./miniConfig')
const { getFeishuDocContent } = require('./feishuNotify')
const { closeReachRequest, reportVersion } = require('./utils')

async function main() {
  const miniType = 'weapp'
  const nConfig = miniConfig[miniType]
  delete nConfig.ypjzzg
  /** 读取配置文件 */
  const configData = fs.readFileSync('src/config/app.ts', 'utf-8')
  /** 获取数据 */
  const match = configData.match(/export const REQUEST_VERSION = '(.+?)'/)
  /** 获取版本号 */
  const version = match && match[1]
  checkList(nConfig).then(async (op) => {
    const option = { miniType }
    const apps = op.apps
    const all = miniConfig[option.miniType]
    /** 打包的appid */
    const appids = op.apps.map((app) => all[app].appid)
    let miniName = []
    /** 总包 */
    let totalSize = '0KB'
    /** 主包 */
    let mainSize = '0KB'

    /** 备注处理 */
    op.desc = `${version}-正式站${op.desc ? ': ' + op.desc : ''}`
    console.log('op', op)
    try {
      const res = await closeReachRequest(appids)
      console.log(`触达关闭成功${JSON.stringify(res)}`)
      apps.forEach(async (ky) => {
        const appInfo = all[ky]
        option.sub = ky
        option.mode = 'build'
        // 强制设置为正式环境
        option.development = 'PRO'
        process.env.RUN_INFO = JSON.stringify(option)
        process.env.MINI_CONFIG = JSON.stringify(miniConfig)
        process.env.APPKY = `/${ky}`
        spawn.sync('node', ['./packages/mini-build-script/bin/mini-build-script', `${option.mode}:${option.miniType}`], {
          stdio: 'inherit',
          cwd: process.cwd(),
        })
        const project = new ci.Project({
          appid: appInfo.appid,
          type: 'miniProgram',
          projectPath: `./dist/${ky}/weapp`,
          privateKeyPath: `./start/my/private.${appInfo.appid}.key`,
          ignores: [],
        })
        ci.upload({
          project,
          version: version,
          desc: op.desc,
          threads: 50,
          setting: {
            es6: true,
            es7: true,
            minify: true,
            minifyJS: true,
            minifyWXML: true,
            minifyWXSS: true,
          },
        }).then((res) => {
          res.subPackageInfo.forEach((item) => {
            if (item.name === '__APP__') {
              mainSize = `${Math.round(item.size / 1024)}KB`
            } else if (item.name === '__FULL__') {
              totalSize = `${Math.round(item.size / 1024)}KB`
            }
          })
          miniName.push({ ...appInfo })
        }).catch((err)=>{
          miniName.push({ ...appInfo, name: `${appInfo.name}-上传失败` })
        }).finally((res) => {
            if (apps.length === miniName.length){
              getFeishuDocContent({
                ...option,
                desc: op.desc,
                miniName,
                version,
                mainSize,
                totalSize,
              })
              reportVersion(miniType)
            }
          })
      })
      // process.exit(1)
    } catch (error) {
      console.log(error)
      process.exit(1)
    }
  })
}

function checkList(config) {
  console.log('主要用途: ', chalk.green('打包发版到线上使用'))
  const promptList = [
    {
      type: 'input',
      message: '请输入备注:',
      name: 'desc',
    },
    {
      type: 'checkbox',
      message: '请选择编译标识：',
      name: 'apps',
      pageSize: 100,
      choices: () => {
        return Object.keys(config).map((item) => {
          return {
            name: config[item].name,
            value: item,
          }
        })
      },
    },
  ]

  return inquirer.prompt(promptList)
}

main().catch((error) => {
  console.error(error)
  process.exit(1)
})

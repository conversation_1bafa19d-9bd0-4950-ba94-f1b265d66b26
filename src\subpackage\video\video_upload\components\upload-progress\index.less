@keyframes loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.upload-progress-component {
  box-sizing: border-box;
  width: 606rpx;
  height: 720rpx;
  background: #FFF;
  border-radius: 8rpx;
  padding: 24rpx;
  text-align: center;
}

.canvas-wrap {
  width: 164rpx;
  height: 164rpx;
  margin: 0 auto;
  background-color: #0092FF;
}

.upload-progress-canvas {
  width: 164rpx;
  height: 164rpx;
  margin: 0 auto;
}

.upload-load {
  width: 164rpx;
  height: 164rpx;
  display: inline-block;
  animation: rotate 1s linear infinite;
}

.upload-error {
  width: 164rpx;
  height: 164rpx;
  margin: 0 auto;
  background-color: #F74742;
  display: inline-block;
}

.success-icon {
  margin: 0 auto;
  width: 128rpx;
  height: 128rpx;
}

.upload-state-text {
  margin-top: 28rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
  line-height: 48rpx;
}

.upload-tips-wrap {
  box-sizing: border-box;
  width: 100%;
  text-align: left;
  background: #F5F6FA;
  border-radius: 16rpx;
  padding: 24rpx;
}

.upload-tips-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #0092FF;
  line-height: 48rpx;
}

.upload-tips-content {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.65);
  line-height: 44rpx;
}

.content-line {
  line-height: 44rpx;
  white-space: normal;
}

.error {
  color: #F74742;
}

.success {
  color: #0092FF;
}

.action-btn {
  width: 100%;
  margin-top: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.button-ghost {
  width: 270rpx;
  height: 80rpx;
  border: 2rpx solid #0092FF;
  border-radius: 6rpx;
  color: #0092FF;
  text-align: center;
  line-height: 80rpx;
  font-size: 28rpx;
}

.button-primary {
  width: 270rpx;
  height: 80rpx;
  border: 2rpx solid #0092FF;
  background: #0092FF;
  border-radius: 6rpx;
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  font-size: 28rpx;
}

.button-primary-large {
  width: 554rpx;
  height: 80rpx;
  border: 2rpx solid #0092FF;
  background: #0092FF;
  border-radius: 6rpx;
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  font-size: 28rpx;
  margin-top: 46rpx;
}

/*
 * @Date: 2022-03-08 10:06:29
 * @Description: 搜索列表
 * @Info: 数据列表内部增加aliasName字段用于搜索结果的展示文案
 */

import { MapStateToData, connect, dispatch, actions } from '@/store/index'

const mapStateToData: MapStateToData = (state) => {
  return {
    topCityHistoryInfo: state.storage.topCityHistoryInfo,
  }
}

/** 预处理 pattern 字符串，防止构造器报错 */
const escapeRegexp = function (str: string) {
  if (!str) return ''
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

Component(connect(mapStateToData)({
  // 组件的属性列表
  properties: {
    /** 搜索列表高度 */
    height: {
      type: String,
      value: '',
    },
    /** 是否是否显示搜索列表 */
    show: {
      type: Boolean,
      value: false,
    },
    /** 搜索关键字 */
    keywords: {
      type: String,
      value: '',
    },
    /** 热门城市 */
    hotAreas: {
      type: Array,
      value: [],
    },
    /** 城市列表总数据 */
    areaTreeAreas: {
      type: Array,
      value: [],
    },
  },
  observers: {
    // 监听keywords,show两个值的变化
    'keywords,show': function (val) {
      this.data.show && this.handleSearch(val)
    },
  },

  // 组件的初始数据
  data: {
    // 搜索的数据列表
    searchList: [],
  },
  methods: {
    onClick(e) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { aliasName, children, ...city } = e.currentTarget.dataset.item
      const topCityHistoryInfo = [...this.data.topCityHistoryInfo]
      // 判断历史记录是否有当前城市的记录
      const index = topCityHistoryInfo.findIndex(info => info.id === city.id)
      if (index != -1) {
        topCityHistoryInfo.splice(index, 1)
      }
      topCityHistoryInfo.unshift(city)
      setTimeout(() => {
        // 本地存储history值
        dispatch(actions.storageActions.setItem({ key: 'topCityHistoryInfo', value: topCityHistoryInfo }))
      }, 200)
      // 不抛出children和aliasName字段
      this.triggerEvent('click', city)
    },
    /** 清空历史搜索数据 */
    onClearHistory() {
      dispatch(actions.storageActions.setItem({ key: 'topCityHistoryInfo', value: [] }))
    },
    /** 处理搜索结果 */
    handleSearch(keywords) {
      const { areaTreeAreas } = this.data
      const searchList = []
      // 正则
      const regKeywords = new RegExp(escapeRegexp(keywords), 'gi')
      //  替换的字符
      const repKeywords = "<span style='color:#0092ff'>$&</span>"
      let aliasName = ''// 显示的文字

      // 处理地址总数据
      areaTreeAreas.forEach(item => {
        const { children } = item
        if (`${item.name}`.includes(keywords) || `${item.letter}`.includes(keywords)) {
          // 省
          if (item.id == '33' || item.id == '34' || item.id == '35') {
            // 处理港澳台
            item.aliasName = `<div>${item.name.replace(regKeywords, repKeywords)}</div>`
            searchList.push(item)
          } else if (children.length > 0) {
            children.forEach((city) => {
              aliasName = `${item.name}-${city.name}`
              // eslint-disable-next-line no-param-reassign
              city.aliasName = `<div>${aliasName.replace(regKeywords, repKeywords)}</div>`
            })
            searchList.push(...children)
          }
        } else if (children.length > 0) {
          // 市
          children.forEach(city => {
            if (`${city.name}`.includes(keywords) || `${city.letter}`.includes(keywords)) {
              aliasName = `${item.name}-${city.name}`
              // eslint-disable-next-line no-param-reassign
              city.aliasName = `<div>${aliasName.replace(regKeywords, repKeywords)}</div>`
              searchList.push(city)
            }
          })
        }
      })

      // 处理热门城市
      this.filterHotAreas(searchList).forEach(city => {
        if ((`${city.name}`.includes(keywords) || `${city.letter}`.includes(keywords))) {
          // eslint-disable-next-line no-param-reassign
          city.aliasName = `<div>${city.name.replace(regKeywords, repKeywords)}</div>`
          searchList.push(city)
        }
      })
      this.setData({ searchList })
    },

    /** 从热门城市中过滤掉已筛选的地址 */
    filterHotAreas(searchList) {
      const { hotAreas } = this.data
      const searchListIds = searchList.map(item => item.id)
      return hotAreas.filter(item => {
        return !searchListIds.includes(item.id)
      })
    },
  },
}))

import { actions, dispatch, store } from '@/store/index'

/**
 * @name 优质工人名片埋点
 * @param eventName 事件名称
 * @param workerInfo 工人信息
 * @param otherData 埋点其它事件数据
 * @param type '' | phone 主要是 phone state 进行判断
 */
export const BPOfQualityWorker = async (eventName, workerInfo, otherData, type = '') => {
  try {
    const { workerRecommendHistory } = store.getState().storage
    // 如果工人推荐浏览记录存在当前id就不上报
    const isReturn = !workerInfo || workerInfo.hide || !workerInfo.id || (type != 'phone' && workerRecommendHistory.includes(workerInfo.id))
    if (isReturn) {
      return
    }
    dispatch(actions.storageActions.setItem({ key: 'workerRecommendHistory', value: [...workerRecommendHistory, workerInfo.id] }))
    const { longitude, latitude } = workerInfo
    const distance = await wx.$.l.getProjectAddressDistanceTxt(`${longitude},${latitude}`)
    wx.$.collectEvent.event(eventName, {
      // 进入来源 1、2、3、4（1-策略进入、2-消息页面按钮、3-找活大列表、4-新手使用指南、12-push、13-站内信）
      enter_id: '1',
      // 指当前页面
      source: '优质牛人工作',
      // 找活名片 ID
      info_id: workerInfo.id,
      // 找活子名片 ID
      resume_uuid: workerInfo.uuid,
      // 位置 id (当前牛人卡片所在的下标+1)
      location_id: workerInfo.item_idx || '',
      // 距离(km)
      post_distance: distance,
      // state 获取状态(优质工人=点击联系埋点才有)
      ...otherData,
    })
  } catch (error) {
    console.error(error)
  }
}

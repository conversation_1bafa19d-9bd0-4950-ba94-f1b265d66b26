/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 消息用户卡片
 */
// import { store } from '@/store/index'

Component({
  properties: {
    /** 消息内容 */
    msgInfo: {
      type: Object,
      value: {},
    },
  },
  data: {
    info: {},
    classNames: '',
  },
  observers: {},
  lifetimes: {
    ready() {
      const { msgInfo } = this.data

      const content = wx.$.u.getObjVal(msgInfo, 'payload.data.content') || {}
      const { tags } = content || {}
      if (wx.$.u.isArrayVal(tags)) {
        this.setData({
          classNames: tags.split(','),
          info: content,
        })
      }
    },
  },

  methods: {
    onClick() {
      const { info, msgInfo } = this.data
      if (info.resume_id) {
        this.triggerEvent('click', { id: info.resume_uuid || info.resume_id, isSelf: msgInfo.isSelf })
      }
    },
  },
})

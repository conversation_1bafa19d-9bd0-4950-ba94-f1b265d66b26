import { actions, dispatch } from '@/store/index'
import { getShareReq } from '@/utils/helper/share/index'

type LoginParams = YModels['POST/account/v1/login/codeLogin']['Req']

/** 验证码登录 */
export function fetchCodeLogin(headers, params: LoginParams) {
  /** 分享唯一标识 */
  const shareReq = getShareReq()
  return wx.$.javafetch['POST/account/v1/login/codeLogin']({
    ...params,
    shareReq,
  }, { headers })
}

/** 获取登录用户信息 */
export function getLoginUserInfo(headers, params = {}) {
  return wx.$.javafetch['POST/account/v1/userBase/getLoginUserInfo']({
    ...params,
  }, {
    headers,
  }).then((res) => {
    const { data } = res || {}
    const { entangledUserInfo } = data || {} as any
    dispatch(actions.storageActions.setItem({ key: 'entangledUserInfo', value: entangledUserInfo || {} }))
    return res
  }).catch((err) => {
    return err
  })
}

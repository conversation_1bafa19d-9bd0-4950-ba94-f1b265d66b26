/*
 * @Date: 2022-03-09 14:58:54
 * @Description: 示例视频组件
 */

import { storage } from '@/store/index'

let videoDuration = '00:00'

Component({
  properties: {
    /** 视频地址 */
    videoUrl: {
      type: String,
      value: '',
    },
    /** 封面图 */
    poster: {
      type: String,
      value: '',
    },
  },
  data: {
    playState: false,
    visiblePlayBtn: false,
    videoDuration: '00:00',
    progress: 0,
    openExample: false,
  },
  lifetimes: {
    ready() {
      /** 首次进入拍摄页默认展示示例视频 */
      const firstResumeShoot = storage.getItemSync('firstResumeShoot')
      !firstResumeShoot && storage.setItem('firstResumeShoot', true)
      this.setData({
        openExample: !firstResumeShoot,
      })
    },
  },
  methods: {
    /** 当视频播放时 */
    onTimeUpdate(event) {
      videoDuration = wx.$.u.transformationTime(event.detail.duration - event.detail.currentTime)
      this.setData({
        videoDuration,
        progress: (event.detail.currentTime / event.detail.duration) * 100,
      })
    },
    /** 视频初始化 */
    onInitVideo(event) {
      videoDuration = wx.$.u.transformationTime(event.detail.duration)
      this.setData({
        videoDuration,
      })
    },
    /** 视频播放完毕hook */
    onEnded() {
      this.setData({
        playState: false,
        visiblePlayBtn: false,
        videoDuration,
        progress: 0,
      })
    },
    /** 点击播放/暂停视频 */
    onClickPlay() {
      const videoContext = wx.createVideoContext('example-video', this)
      videoContext[this.data.playState ? 'pause' : 'play']()
      this.setData({
        visiblePlayBtn: true,
        playState: !this.data.playState,
      })
    },
    /** 切换视频显示隐藏 */
    onOpenExamples() {
      this.data.playState && this.onClickPlay()
      this.setData({
        openExample: !this.data.openExample,
      })
    },
  },
})

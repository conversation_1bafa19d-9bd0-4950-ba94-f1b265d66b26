/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-02-09 08:55:29
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-09-25 15:16:52
 * @FilePath: /yupao_mini_taro_recode-1.2.1-yh/src/swan-sitemap/recruit/index.js
 * @Description:
 */

import { app } from '@/config/index'

// 引入转换时间格式
Page({
  data: {
    listData: [],
    totalPage: 1,
    currentPage: 1,
    path: 'swan-sitemap/index',
  },

  onLoad(e) {
    // 初始页面打开时，需要读取页面的 currentPage 参数（即翻页页码），并根据参数值请求数据
    let { currentPage } = e
    // 起始页码为 1，如读取到的值为空，默认赋值起始页码
    currentPage = +currentPage || 1
    // 百度小程序sitemap找活接口请求参数ID
    const id = currentPage == 1 ? 0 : wx.getStorageSync('recruit_next_page_id') || 0
    // 根据当前页码获取该页数据资源
    this.requestData(currentPage, id)
  },

  requestData(currentPage, id) {
    // 发起数据资源请求。
    wx.request({
      // 数据接口，需改为开发者实际的请求接口
      url: `${app.REQUEST_URL}jlist/job/baiduSiteMap`,
      header: {
        'content-type': 'application/json',
        business: '1',
        system_type: 'baidu',
        version: '1.2.8',
        versionmini: '1',
        // wechat_token: 'baidu',
      },
      data: {
        min_id: id,
        // 参数中需携带页码参数，此为示例，可根据实际情况传入其他所需参数
        page: currentPage,
        page_size: 200,
      },
      success: (res) => {
        if (res.statusCode === 200) {
          const resData = res.data
          wx.setStorageSync('recruit_next_page_id', resData?.data?.minId)
          // 根据返回数据更新列表。如请求返回格式不符合模板数据 listData 的要求格式，需调整格式后再赋值给 listData。
          const { list } = resData.data
          // 输出转换时间格式，时间格式为 2021-08-23 16:32:22
          // for (var i = 0; i < list.length; i++) {
          //   list[i]['sort_time'] = dateTimeStampFormat(Number(list[i]['sort_time']) * 1000, 'YYYY-mm-dd HH:MM:SS')
          // }
          // listData 的格式要求为：Array<{title:string, path:string, releaseDate:DateString}>
          // 输出新的数组 Array<{title:string, path:string, releaseDate:DateString}>
          const newTecherList = list.map((item) => ({
            // 文章路径标题
            title: item.title,
            // 小程序文章路径
            path: `/subpackage/recruit/details/index?id=${item.id}`,
            // 发布时间
            releaseDate: item.add_time_str,
          }))
          this.setData({
            // 给listData 输出新的数组 newTecherList
            listData: newTecherList,
            // 总页数
            totalPage: resData.data.page_count,
            // 页码参数
            currentPage,
          })
        }
      },
    })
  },
})

.skeleton-box {
  position: relative;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background-size: 100% 600rpx;
  overflow: hidden;

  &::after {
    content: '';
    display: block;
    width: 150rpx;
    height: 200vh;
    background-color: #fff;
    position: absolute;
    z-index: 21;
    top: 0;
    left: 0;
    opacity: 0.6;
    box-shadow: 1px -1px 20px 20px #fff;
    animation: skeleton-loading1 1.2s infinite;
  }
}

@keyframes skeleton-loading1 {
  0% {
    transform: rotate(45deg) translateY(-50vw) translateX(-200vw);
  }

  to {
    transform: rotate(45deg) translateY(-50vw) translateX(100vw);
  }
}

.skeleton {
  width: 100%;
  height: 100%;
  background-color: #ebecf1;
  border-radius: 6rpx;
}

.all-v {
  position: fixed;
  z-index: 10000;
  background: #f5f6fa;
}

.sv-v-w-height {
  height: 24rpx;
  width: 1rpx;
}

.recuit-content {
  position: relative;
  background: #fff;
  border-radius: 6rpx;
  margin: 0 32rpx;
  padding: 26rpx;
  width: 686rpx;
}

.recruit-title {
  font-size: 32rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
  width: 432rpx;
  height: 48rpx;
}

.classly-p {
  display: flex;
}

.classly-content {
  margin: 20rpx 0;
  flex: 1;
  display: flex;
}

.classly-txt {
  flex: 1;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.65);
  background: #ebecf1;
  border-radius: 6rpx;
  padding: 4rpx 16rpx;
  height: 48rpx;
}

.classly-txt-r {
  margin-right: 8rpx;
}

.address-txt {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.65);
  height: 36rpx;
}

.detail-txt {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.25);
  height: 36rpx;
  margin-top: 20rpx;
}

.btmbtn-body {
  position: fixed;
  bottom: 0;
  min-height: 196rpx;
  background: #fff;
  width: 100vw;
}

.shortcut-fun {
  height: 96rpx;
  width: 100vw;
  padding: 0 32rpx;
  display: flex;
  align-items: center;
}

.shortcut-btn {
  width: 204rpx;
  height: 64rpx;
  background: #0092ff;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.shortcut-btn-txt {
  font-size: 28rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.95);
}

.chat-fun {
  min-height: 100rpx;
  width: 100vw;
  padding: 18rpx 26rpx;
  display: flex;
  align-items: center;
}

.face-icon {
  margin-right: 28rpx;
}

.face-icon-r-12 {
  margin-right: 12rpx;
}

.chat-input-v {
  width: 450rpx;
  background: #f5f6fa;
  border-radius: 6rpx;
  margin: 0 26rpx;
  padding: 10rpx 22rpx;
  min-height: 64rpx;
  display: flex;
  align-items: center;
}

.yuyin-btn-click {
  background: #d1d5e0;
}

.chat-input {
  width: 100%;
  max-height: 200rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
}

.send-btn {
  font-size: 36rpx;
  font-weight: bold;
  color: #0092ff;
}

.chat-yuyin-btn {
  font-size: 28rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
  width: 100%;
}

.msg-main {
  display: flex;
  justify-content: flex-end;
  margin: 74rpx 24rpx 0;
}

.msg-txt-v {
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;
}

.msg-txt {
  position: relative;
  width: 456rpx;
  padding: 14rpx 20rpx;
  background: #0092ff;
  border-radius: 6rpx;
  height: 100rpx;
}

.main-start {
  justify-content: flex-start;
}

.msg-other {
  background: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.45);
}

.arrow {
  position: absolute;
  top: 19px;
  width: 0;
  height: 0;
  font-size: 0;
  border: solid 8rpx;
}

.arrow-r {
  right: -8px;
  border-color: transparent transparent transparent #0092ff;
}

.arrow-l {
  left: -8px;
  border-color: transparent rgba(0, 0, 0, 0.05) transparent transparent;
}

.emoji {
  width: 36rpx;
  height: 36rpx;
  margin: 0 4rpx;
}

.dom_em {
  display: inline;
  word-wrap: break-word;
  word-break: break-all;
}

.dom_em_v {
  height: 100%;
}

.avater {
  width: 96rpx;
  height: 96rpx;
  border-radius: 6rpx;
}

.avater-l {
  margin-left: 20rpx;
}

.avater-r {
  margin-right: 20rpx;
}

.flex {
  display: flex;
}

.header {
  display: flex;
  background-color: #fff;
  padding: 0 24rpx;
}

.bg {

}

.item-block {
  left: 200rpx;
  width: 176rpx;
  height: 112rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.block {
  width: 40rpx;
  height: 40rpx;
  border-radius: 8rpx;
  background-color: rgba(245, 246, 250, 1);
  margin-bottom: 8rpx;
}

.block-line {
  width: 100rpx;
  height: 36rpx;
  border-radius: 8px;
  background-color: rgba(245, 246, 250, 1)
}

.content {
  padding: 24rpx;
}

.line {
  width: 702rpx;
  height: 96rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
}

.msg {
  height: 92rpx;
  align-items: flex-end;
  margin-bottom: 40rpx;
}

.circle {
  width: 64rpx;
  height: 64rpx;
  background-color: #fff;
  margin-right: 16rpx;
  border-radius: 50%;
}
.right-circle {
  background-color: rgba(224, 243, 255, 1);
}


.msg-box {
  width: 336rpx;
  height: 92rpx;
  background-color: #fff;
  border-radius: 24rpx 24rpx 24rpx 4rpx;
}

.r-msg-box {
  border-radius: 24rpx 24rpx 4rpx 24rpx;
  background-color: rgba(224, 243, 255, 1);
  margin-right: 16rpx;
}
.msg-right {
  justify-content: flex-end;
}

.avatar {
  width: 64rpx;
  height: 64rpx;
  background-color: #fff;
  border-radius: 50%;
}

.b {
  
}

.text-pre {
  white-space: pre-wrap!important;
  word-break: break-all;
}

.card {
  padding: 0 32rpx;
  background-color: white;

  &:last-child {
    .card-container {
      border-bottom: none;
    }
  }
  .head-tag-box {
    flex: 1;
    margin: -4rpx;
    padding: 0 8rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-wrap: wrap;
    height: 46rpx;
    overflow: hidden;
  }

  .head-tag {
    margin: 4rpx;
    height: 36rpx;
    min-width: 36rpx;
    display: inline-flex;
    align-items: center;
    padding: 0 8rpx;
    border-radius: 8rpx;
    color: @primary-color;
    border: 2rpx solid @primary-color;
    background: #e0f3ff;
    font-size: 20rpx;
    line-height: 28rpx;
    font-weight: bold;
    .tag-dot {
      white-space: nowrap;
      width: 8rpx;
      height: 8rpx;
      background: #06B578;
      border-radius: 100%;
      margin-right: 4rpx;
    }
    &.tag-red {
      color: @error-color;
      border: 2rpx solid @error-color;
      background: #FFEBEC;
    }
    &.tag-gray {
      color: rgba(0, 0, 0, 0.25);
      border: 2rpx solid rgba(0, 0, 0, 0.25);
      background: #F5F6FA;
    }
    &.tag-green {
      color: #06B578;
      border: 2rpx solid #06B578;
      background: #DFF2EC;
    }
  }

  .card-container {
    width: 100%;
    border-bottom: 1rpx solid #E9EDF3;
    padding: 40rpx 0;


    .card-subtitle {
      width: 100%;
      font-size: 34rpx;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.85);
      position: relative;

      &:not(.no-dot)::before {
        position: absolute;
        left: -20rpx;
        top: 50%;
        width: 12rpx;
        height: 12rpx;
        border-radius: 6rpx;
        transform: translateY(-50%);
        background-color: #0092FF;
        content: "";
      }
    }
  }

  .resume-header {
    width: 100%;
    display: flex;


    .header-left {
      display: flex;
      flex-direction: column;
      width: calc(100% - 120rpx);

      .header-left-top {
        display: flex;
        align-items: center;

        >.name {
          color: rgba(0, 0, 0, 0.85);
          font-weight: 500;
          font-size: 34rpx;
          padding-right: 8rpx;
        }

        >.real-name-tag {
          color: rgba(0, 146, 255, 1);
          font-weight: 500;
          font-size: 26rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 1);
          padding: 0 8rpx;
          border-radius: 8rpx;
          height: 44rpx;
          border: 2rpx solid rgba(224, 243, 255, 1);
          margin-right: 8rpx;
        }

        >.text {
          color: #06b578;
          font-size: 26rpx;
          padding-left: 16rpx;
        }
      }

      .header-left-bottom {
        width: 100%;
        color: rgba(0, 0, 0, 0.65);
        font-size: 26rpx;
        padding-top: 16rpx;
      }
    }

    .header-right {
      margin-left: 24rpx;
      flex-shrink: 0;
      height: 96rpx;
      border-radius: 48rpx;
      overflow: hidden;

      >.image {
        width: 96rpx;
        height: 100%;
        border-radius: 48rpx;
        overflow: hidden;
        flex-shrink: 0;
      }
    }
  }


  .status-block {
    width: 100%;

    .status-block-header {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      >.subtitle {
        color: rgba(0, 0, 0, 0.85);
        font-weight: bold;
        font-size: 30rpx;
      }

      >.tag {
        padding: 6rpx 12rpx;
        border-radius: 8rpx;
        background: #ffebec;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 26rpx;
        color: #e8362e;

        &.grey {
          color: rgba(0, 0, 0, 0.25);
          background-color: #f5f6fa;
        }

        >.icon-img {
          width: 24rpx;
          height: 24rpx;
          margin-right: 6rpx !important;
        }
      }
    }

    .status-block-tags {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 24rpx;

      >.tag {
        height: 56rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 16rpx;
        border-radius: 8rpx;
        background-color: #f5f6fa;
        font-size: 26rpx;
        color: rgba(0, 0, 0, 0.85);
        margin-right: 16rpx;
      }
    }
  }


  .job-expect-block {
    width: 100%;

    >.list {
      width: 100%;
      display: flex;
      flex-direction: column;

      >.list-item {
        width: 100%;
        padding: 40rpx 0;
        border-bottom: 1rpx solid #e9edf3;
        &.list-item-hide {
          display: none;
        }
        &:last-child {
          border-bottom: none;
          padding-bottom: 0;
        }

        >.list-item-header {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;

          >.text {
            font-size: 30rpx;
            font-weight: bold;
            color: rgba(0, 0, 0, 0.85);
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          >.highlight-text {
            font-size: 34rpx;
            font-weight: bold;
            flex-shrink: 0;
            padding-left: 32rpx;
            color: #0092FF;
          }
        }

        >.list-item-tags {
          margin-top: 8rpx;

          >.tag-box {
            height: 154rpx;
            overflow: hidden;

            >.tag-cover {
              display: flex;
              align-items: center;
              flex-wrap: wrap;

              >.tag {
                height: 56rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0 16rpx;
                border-radius: 8rpx;
                background-color: #f5f6fa;
                font-size: 26rpx;
                color: rgba(0, 0, 0, 0.85);
                margin-right: 16rpx;
                margin-top: 16rpx;
              }
            }
          }

          .moreheight {
            height: auto;
            overflow: visible;
          }

          >.more-box{
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            align-items: center;
            height: 36rpx;
            margin-top: 8rpx;
            .text {
              color: rgba(0, 0, 0, 0.45);
              font-size: 26rpx;
              margin-right: 4rpx;
            }
          }
        }
      }
    }


  }

  .hope-area-text {
    padding-top: 40rpx;
    font-size: 30rpx;
    color: rgba(0, 0, 0, 0.65)
  }

  .introduce {
    width: 100%;
    font-size: 30rpx;
    margin-top: 40rpx;
    color: rgba(0, 0, 0, 0.65);
    max-height: 208rpx;
    overflow: hidden;
    position: relative;
    line-height: 52rpx;

    &.show-all {
      max-height: none;
    }

    >.expand {
      background-color: #fff;
      padding-left: 12rpx;
      font-size: 30rpx;
      color: #0092FF;
      line-height: 52rpx;
      position: absolute;
      bottom: 0;
      right: 0;
    }
  }

  .work-experience,
  .edu-experience {
    width: 100%;

    >.list {
      width: 100%;
      display: flex;
      flex-direction: column;

      >.list-item {
        width: 100%;
        padding: 40rpx 0;
        border-bottom: 1rpx solid #e9edf3;

        &.list-item-hide {
          display: none;
        }

        &:last-child {
          border-bottom: none;
          padding-bottom: 0;
        }

        >.list-item-header {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;

          >.text {
            font-size: 30rpx;
            font-weight: bold;
            color: rgba(0, 0, 0, 0.85);
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          >.secondary-text {
            font-size: 30rpx;
            padding-left: 32rpx;
            flex-shrink: 0;
            color: rgba(0, 0, 0, 0.45);
          }
        }

        >.list-item-text {
          padding-top: 16rpx;
          font-size: 30rpx;
          color: rgba(0, 0, 0, 0.65);
        }

        >.list-item-desc {
          white-space: pre-wrap;
          word-break: break-all;
          padding-top: 16rpx;
          font-size: 30rpx;
          line-height: 52rpx;
          color: rgba(0, 0, 0, 0.65);
        }
      }
    }

    >.bottom-btn {
      padding-top: 40rpx;
      color: #0092FF;
      font-size: 30rpx;
    }

  }

  .project-exp-card {
    padding: 40rpx 0;
    border-bottom: 1rpx solid #e9edf3;

    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
  }

  .video {
    >.content {
      display: flex;
      align-items: center;
      margin-top: 32rpx;
      width: 100%;
      position: relative;



      /* 面试视频 */
      .interview-video {
        position: relative;
        width: 100%;
        height: 360rpx;
        border-radius: 16rpx;
        background: #f5f6fa;
        overflow: hidden;
      }

      .play-btn {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 3;
        width: 80rpx;
        height: 80rpx;
      }

      .cover-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 622rpx;
        height: 360rpx;
        filter: blur(20rpx);
        transform: scale(1.3);
      }

      .video-poster {
        position: absolute;
        top: 0;
        left: 50%;
        z-index: 2;
        transform: translateX(-50%);
        width: 204rpx;
        height: 360rpx;
        background: #f5f6fa;
      }

      .video-duration {
        padding: 10rpx 12rpx;
        line-height: 28rpx;
        position: absolute;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 8rpx;
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
        bottom: 10rpx;
        right: 10rpx;
        z-index: 3;
        text-align: center;
      }


      .video-zz-ios {
        background-image: url("https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_video_z.png");
      }
    }
  }
}

.full-width {
  width: 100%;
}


.list-item-cates-tags {
  margin-top: 40rpx;

  .tag-box {
    height: 272rpx;
    overflow: hidden;

    .tag-cover {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .tag {
        height: 56rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 16rpx;
        border-radius: 8rpx;
        background-color: #f5f6fa;
        font-size: 26rpx;
        color: rgba(0, 0, 0, 0.85);
        margin-right: 16rpx;
        margin-bottom: 16rpx;
      }
    }
  }

  .moreheight {
    height: auto;
    overflow: visible;
  }

  .more-box{
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    height: 44rpx;
    margin-top: 24rpx;
    .text {
      color: rgb(0, 146, 255);
      font-size: 28rpx;
    }
  }
}


.hidden-box {
  position: fixed;
  bottom: -280rpx;
  height: auto !important;

  .tag {
    height: 56rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16rpx;
    border-radius: 8rpx;
    background-color: #f5f6fa;
    font-size: 26rpx;
    color: rgba(0, 0, 0, 0.85);
    margin-right: 16rpx;
    margin-bottom: 16rpx;
  }
}

.job-exp-mask {
  width: 706rpx;
  height: 108rpx;
  // 兼容滤镜效果导致图片扩散
  margin-left: -10rpx;
}
.school-name-mask {
  width: 520rpx;
  height: 52rpx;
}
.cates-list-mask {
  width: 690rpx;
  height: 56rpx;
  margin-top: 40rpx;
}

.filter {
  filter: blur(12rpx);
  -webkit-filter: blur(12rpx);
}

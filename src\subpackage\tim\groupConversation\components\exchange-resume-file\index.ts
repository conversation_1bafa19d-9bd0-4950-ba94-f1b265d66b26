import { store } from '@/store/index'

Component(class extends wx.$.Component {
  properties = {
    visible: { type: Boolean, value: false },
    conversation: { type: Object, value: {} },
    fileList: { type: Array, value: [] },
    // 消息ID
    exchangeMsgId: { type: String, value: '' },
    exchangeType: { type: String, value: '' },
  }

  observers = {
    visible(v) {
      if (v) {
        const { fileList } = this.data
        const { userChooseRole } = store.getState().storage
        if (wx.$.u.isArrayVal(fileList) && userChooseRole == 2) {
          const len = fileList.length
          if (len == 1) {
            this.setData({ workerSendFilePop: true })
          } else {
            this.setData({ resuemeFilePop: true })
          }
        } else if (userChooseRole == 1) {
          this.setData({ bossReqFilePop: true })
        }
      }
    },
  }

  data = {
    // 牛人发送简历弹框
    workerSendFilePop: false,
    // 简历附件列表弹框
    resuemeFilePop: false,
    // 老板求简历确认弹框
    bossReqFilePop: false,
  }

  onClose() {
    this.setData({ workerSendFilePop: false, resuemeFilePop: false, bossReqFilePop: false })
    this.triggerEvent('close')
  }
})

/*
 * @Date: 2022-03-01 13:56:55
 * @Description: 视频查看页
 * @path-params: {
 *    pageSource: 'midphone', 中间号视频宣导进入的, 'resumeList'从列表页进入
 *    title: '', 视频标题
 *    pageSource: 'normal', 普通视频进入的
 *    item: {videoUrl: '', videoCoverImg: ''}
 * }
 */

import { helper } from '@/utils/index'
import { getMenuButtonBoundingClientRect, getSystemInfoSync, rpxToPx } from '@/utils/tools/common/index'
import { dispatch, actions, store, storage } from '@/store/index'

import { getBaseInfo, refreshDetail } from './utils'
import { toLogin } from '@/utils/helper/common/toLogin'

/** 视频的总时长 */
let videoDuration = 0
const { windowWidth, windowHeight, statusBarHeight } = getSystemInfoSync()
const { top: menuButtonTop, left: rectLeft } = getMenuButtonBoundingClientRect()
Page({
  /** 如果pageSource为midphone则记录播放的秒钟 */
  midphoneSecond: 0,
  /** 视频播放的秒钟 */
  playSeconds: 0,
  /** 是否需要设置中间号视频宣导缓存 */
  isSettingMidphone: true,
  /** 视频标题 */
  title: '',
  data: {
    /** 从哪个页面跳转过来的： normal - 常规页面，只预览 ，midphone - 中间号视频宣导进入的 ，resumeList - 找活大列表 ， resumeDetail - 找活名片 */
    pageSource: 'normal',
    /** 是否显示拨打电话按钮 */
    showPhoneBtn: false,
    statusBarHeight,
    menuButtonTop,
    rectLeft,
    /** video实例 */
    videoCtx: null,
    /** 视频地址 */
    videoUrl: '',
    /** 视频封面图地址 */
    posterUrl: '',
    /** video外层view的高度 */
    videoHeight: windowHeight,
    /** video的真实高度 */
    videoRealHeight: windowHeight,
    /** video的展示方式 */
    videoObjectFit: 'cover',
    /** 播放状态 */
    playState: false,
    videoTime: '00:00',
    /** 播放进度 */
    progress: 0,
    /** 页面来源 */
    origin: '',
    /** 找活详情 */
    detail: {},
    /** 找活详情评价内容控制器 */
    evaluateContentControl: {
      show: false,
    },
    /** 评价弹窗是否加入中间号获取真实号码 */
    isMidGetTel: {
      isShow: false,
      tel: '',
      action: 2,
    },
    /** 用户信息数据 */
    baseInfo: {
      /** 姓名 年龄 名族 */
      userStr: '',
      /** 找活状态 */
      resumeStatus: '',
      /** 工种信息 */
      classifyStr: '',
    },
    isSelf: false,
  },
  onLoad(options) {
    // 每次进页面都要重置该值，防止缓存
    videoDuration = 0
    const { pageSource, item = { }, detail = {}, title = '', showPhoneBtn = false } = wx.$.r.getParams()
    this.initLayout()
    if (item.videoUrl) {
      wx.$.loading('加载中...')
      // wx.showLoading({ title: '加载中', mask: true })
    }
    const { userId } = storage.getItemSync('userState')
    const baseInfo = getBaseInfo(detail)
    this.setData({
      baseInfo,
      title,
      showPhoneBtn,
      pageSource,
      videoUrl: item.videoUrl,
      videoCoverImg: item.videoCoverImg,
      // videoUrl: videoResp.attach.filePath,
      // videoCoverImg: videoResp.attach.cover,
      origin: options.origin || '',
      detail,
      isSelf: detail.userId && detail.userId == userId,
    })
  },
  onShow() {},
  onBack() {
    wx.$.r.back()
  },
  /** 跳转到找活名片 */
  onNavigateToDetail() {
    const { detail } = this.data

    dispatch(actions.resumeActions.setInfo({ uuid: detail.resumeSubUuid, id: detail.resumeId }))
    dispatch(actions.resumeActions.fetchGetDetailOther({ uuid: detail.resumeSubUuid }))
    wx.$.r.replace({
      path: '/subpackage/resume/detail/index',
      query: {
        uuid: detail.resumeSubUuid,
        id: detail.resumeId,
        origin: this.data.origin,
      },
    })
  },
  onClickVideo() {
    this.data.videoCtx?.[this.data.playState ? 'pause' : 'play']?.()
    this.setData({
      playState: !this.data.playState,
    })
  },
  /** 当视频播放中会触发 */
  onTimeUpdate(e) {
    handlerVisibleMidphone.call(this, e)
    this.setData({
      videoTime: wx.$.u.transformationTime(e.detail.duration - e.detail.currentTime),
      progress: (e.detail.currentTime / e.detail.duration) * 100,
    })
  },
  /** 当视频播放完毕时 */
  onEnded() {
    this.setData({
      playState: false,
      videoTime: wx.$.u.transformationTime(videoDuration),
      progress: 0,
    })
  },
  /** 视频数据加载完成时触发 */
  onLoadVideoData(e) {
    wx.hideLoading()
    videoDuration = e.detail.duration
    // eslint-disable-next-line prefer-const
    let { videoObjectFit, videoHeight, videoRealHeight } = this.data
    if (e.detail.width > e.detail.height) {
      // 说明是横屏
      videoObjectFit = 'contain'
      videoRealHeight = videoHeight
    } else {
      videoRealHeight = windowWidth * (e.detail.height / e.detail.width)
    }
    this.setData({
      videoTime: wx.$.u.transformationTime(e.detail.duration),
      videoObjectFit,
      videoRealHeight,
    })

    if (!this.data.playState) {
      this.onClickVideo()
    }
  },
  /** 点击进度条触发 */
  onChangeProgress(e) {
    const { value } = e.detail
    const second = videoDuration * (value / 100)
    this.setData({
      videoTime: wx.$.u.transformationTime(second),
      progress: value,
    })
    this.data.videoCtx.seek(second)
  },
  /** 获取布局信息，设置包裹video标签的view要展示的高度 */
  initLayout() {
    /** 获取cameraCircle宽度 */
    const videoCtx = wx.createVideoContext('video')
    wx.createSelectorQuery()
      .select('#video-footer')
      .boundingClientRect((rect) => {
        if (rect) {
          this.setData({
            videoCtx,
            videoHeight: windowHeight - rect.height - (statusBarHeight || 0),
          })
        } else {
          this.setData({
            videoCtx,
            videoHeight: windowHeight - rpxToPx(120) - (statusBarHeight || 0),
          })
        }
      })
      .exec()
  },
  onError(e) {
    wx.hideLoading()
    wx.$.msg('视频加载失败')
  },
  /** 生命周期回调—监听页面卸载 */
  onUnload() {
    // 刷新找活详情
    refreshDetail()
  },
  async onPhoneClick() {
    await wx.$.u.waitAsync(this, this.onPhoneClick, [], 2000)
    const { userState } = store.getState().storage
    // 判断是否登录
    if (!userState.login) {
      toLogin(true)
      return
    }
    const { detail = {} } = this.data
    const { resumeSubUuid } = detail || {}
    // 拨打电话前需要进行实名判断
    wx.showLoading({ title: '正在联系...', mask: true })
    this.setData({ callPhoneType: 'call', isCallPhone: true })
    wx.$.l.resumeTelV3(
      { uuid: resumeSubUuid },
      { pageName: '查看面试视频页面', hasShowPhone: detail.viewed },
    ).then(async (res) => {
      wx.hideLoading()
      const { data } = res || {}
      if (!data) {
        return
      }
      const { tel, dialogIdentifyDefault, dialogData } = data
      const { dialogIdentify = '' } = dialogData || {}
      if (tel || dialogIdentifyDefault || (await wx.$.l.newResumeMidPops()).includes(dialogIdentify)) {
        wx.$.l.operationMidCallV3(
          { ...res.data },
          { hasShowPhone: detail.viewed },
          {
            callPopBack: (pop) => {
              wx.$.resumeMidModel({
                ...pop,
                zIndex: 10011,
                source: '2',
                infoId: resumeSubUuid,
                pageName: '查看面试视频页面',
                call: (callresp) => {
                  const { detail } = callresp || {}
                  if (detail == 2) {
                    const showRealTelState = storage.getItemSync('showRealTelState')
                    if (showRealTelState) {
                      const currentPage = wx.$.r.getCurrentPage()
                      storage.setItemSync('showRealTel', currentPage.route)
                    }
                  }
                  this.onCallMidPhone(callresp)
                },
              })
              this.callPhoneOk(detail)
            },
            callRealPhone: (isPrivacy = 0) => {
              this.callRealOrMidPhone(isPrivacy)
            },

            callPhone: () => {
              this.onPhoneClick()
            },
          },
        )
      } else {
        this.callRealOrMidPhone(0)
      }
    }).catch((res) => {
      wx.hideLoading()
    })
  },

  /** 拨打电话(真实拨打或者中间号拨打) */
  callRealOrMidPhone(isPrivacy = 1) {
    const { detail = {} } = this.data
    const { resumeSubUuid } = detail || {}
    wx.$.l.resumeTelV3({ uuid: resumeSubUuid, isPopup: 0, isPrivacy }).then(async resTel => {
      if (resTel.code == 0) {
        await wx.$.l.operationMidCallV3({ ...resTel.data }, {}, {
          callPopBack: (pop) => {
            wx.$.resumeMidModel({
              ...pop,
              zIndex: 10011,
              source: '2',
              infoId: resumeSubUuid,
              pageName: '查看面试视频页面',
              call: (callresp) => {
                const { detail } = callresp || {}
                if (detail == 2) {
                  const showRealTelState = storage.getItemSync('showRealTelState')
                  if (showRealTelState) {
                    const currentPage = wx.$.r.getCurrentPage()
                    storage.setItemSync('showRealTel', currentPage.route)
                  }
                }
                this.onCallMidPhone(callresp)
              },
            })
          },
        })
        this.callPhoneOk(detail, resTel.data)
      }
    })
  },
  /** 拨打电话返回结果处理逻辑 */
  callPhoneOk(detail) {
    helper.list.cardViewHistory.setHistory('resume', detail.resumeSubUuid, true, true)
    this.setData({
      'detail.viewed': true,
    })
  },
  /** 组件回调拨打中间号 */
  async onCallMidPhone(e) {
    // val.detail 为 2 代表拨打虚拟号
    const val = e ? e.detail : 2
    const { detail } = this.data
    wx.$.l.resumeTelV3({ uuid: detail.resumeSubUuid, isPopup: 0, isPrivacy: val == 2 ? 1 : 0 }).then(async resTel => {
      const { code, data } = resTel
      if (code == 0) {
        if (val == 2 && data.timeRemaining) {
          await wx.$.l.midPhoneTimeMsg(data.timeRemaining)
        }
        setTimeout(() => {
          wx.$.u.callPhone(data.tel)
          this.callPhoneOk(detail, data)
        }, 200)
      }
    })
  },
})

/** 判断并存储是否隐藏中间号宣导组件 */
function handlerVisibleMidphone(e) {
  if (this.data.pageSource === 'midphone' && this.isSettingMidphone) {
    const playSeconds = parseInt(e.detail.currentTime, 10)
    if (this.midphoneSecond > 29) {
      this.isSettingMidphone = false
      return
    }
    if (playSeconds != this.playSeconds) {
      this.playSeconds = playSeconds
      this.midphoneSecond += 1
    }
  }
}

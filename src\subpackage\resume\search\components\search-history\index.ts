import { actions, dispatch, store } from '@/store/index'

Component(class extends wx.$.Component {
  useStore(state: StoreRootState) {
    const { searchHistory } = state.storage

    return {
      list: searchHistory.resumeobj,
    }
  }

  observers = {
    list(v) {
      if (wx.$.u.isArrayVal(v)) {
        this.initHistoryList(v)
      } else {
        this.setData({ historyList: [] })
      }
    },
  }

  data = {
    historyList: [],
  }

  initHistoryList(list) {
    const historyList = list.map(item => {
      const { keywords, title, cityName, filterscreen, city } = item
      const showLabels = []

      // 处理基础信息
      if (keywords) showLabels.push(keywords)
      if (title) showLabels.push(keywords ? ' - ' : '', title)

      // 处理城市名称
      if (cityName) {
        const { resumeSearchCityObj } = city || {}
        const { cityLen } = resumeSearchCityObj || {}
        // eslint-disable-next-line no-nested-ternary
        const separator = (!keywords && !title) ? '' : (keywords && !title ? ' - ' : ' · ')
        let sub = ''
        if (cityLen && cityLen > 1) {
          sub = ` · ${cityLen}`
        }
        showLabels.push(separator, `${cityName}${sub}`)
      }

      // 处理筛选条件
      if (!wx.$.u.isEmptyObject(filterscreen)) {
        const { age, sex, salary, activeStatus, workStatus } = filterscreen
        const addFilter = (value, formatter) => {
          if (!wx.$.u.isEmptyObject(value)) {
            showLabels.push(' · ', formatter(value))
          }
        }

        // eslint-disable-next-line sonarjs/no-nested-template-literals
        addFilter(age, a => `${a.map(c => `${c}岁`).join('-')}`)
        addFilter(sex, s => Object.values(s).map((s:any) => s.name).join(' · '))
        addFilter(salary, s => Object.values(s).map((s:any) => s.name).join(' · '))
        addFilter(activeStatus, s => Object.values(s).map((s:any) => s.name).join(' · '))
        addFilter(workStatus, s => Object.values(s).map((s:any) => s.name).join(' · '))
      }

      return { ...item, showLabel: showLabels.join('') }
    })

    this.setData({ historyList })
  }

  async onClick(e) {
    await wx.$.u.waitAsync(this, this.onClick, [e], 1000)
    const { item } = e.currentTarget.dataset
    this.triggerEvent('click', { item })
  }

  onClearHistory() {
    const { searchHistory } = store.getState().storage
    const value = wx.$.u.deepClone(searchHistory)
    value.resumeobj = []
    dispatch(actions.storageActions.setItem({ key: 'searchHistory', value }))
  }
})

export interface ITopConfigRes {
  /** 最大可选城市 */
  maxCityNum: number
  /** 最大可选省份 */
  maxProvinceNum: number
  /** 可选置顶时间列表 */
  topConfigList: ITopTimeListItem[]
}

/**
 * @name 置顶时间可选项
 */
export interface ITopTimeListItem {
  /** 标题 */
  title: string
  /** 置顶天数 */
  days: number
  /** 是否默认展示项 */
  defaultShow: boolean

  /** 曝光读 */
  exposureNum: number
  /** 推广次数 */
  promotionNum: number
  /** 免费自动刷新次数 */
  refreshNum: number
}

/**
 * @name 当前招工置顶信息
 */
export interface IJobTopInfo {
  /** 招工 ID */
  jobId: number
  /** 置顶状态类型：-1-未置顶 0-预约置顶中 1-置顶中 2-暂停置顶中 3-置顶已结束 */
  jobTopStatus: { code: number; desc: string }
  /** 置顶成功天数 */
  pinDays: number

  /** 置顶操作类型：1=预约置顶；2=去置顶；3=修改置顶；4=继续置顶； */
  topType: { code: number; desc: string }
  /** 置顶省份ID集 */
  topProvinceIds: any
  /** 置顶城市ID集 */
  topCityIds: any
  /** 置顶的招工 ids */
  occV2: Array<{ industry: number; occIds: number[] }>

  /** 计划结束日期时间 */
  planEndDateTime: any
  /** 计划结束日期时间-自定义 */
  planEndDateTimeStr: any
  /** 默认地区 ID */
  defaultAreaId: number
}

/**
 * @name 置顶招工计算请求参数
 */
export interface ITopJobCalcReq {
  /** 招工 ID */
  jobId: string

  /** 省份 */
  provinces: any[]
  /** 城市 */
  cities: any[]

  /** 置顶类型 1-预约置顶 2-去置顶 3-修改置顶 */
  topType: number
  /** 置顶天数 */
  topDays: number
}

/**
 * 置顶天数选择器数据项结构
 */
export interface IDayPickerListItem {
  /** 展示标题 */
  name: string
  /** 展示内容 */
  value: number
}

/**
 * 置顶城市选中列表
 */
export interface ITopCityListItem {
  /** 省市父级 ID */
  pid: number
  /** 省市ID */
  id: number
  /** 省市名称 */
  name: string
}

export interface IJobTopDoReq {
  jobId: number
  provinces: number[]
  cities: number[]

  topType: number
  topDays: number
  topCheckId: string
}

/** 招工置顶价格信息 */
export interface IJobTopPriceInfo {
  jobId: number
  /** 总积分消耗 */
  totalConsume: number
  /** 总原价 */
  originPrice: number
  /** 折扣描述 */
  discountDesc: number
  /** 定价 ID */
  pricingId: number
  /** 置顶校验 ID */
  topCheckId: string
}

/** 招工置顶配置查询请求参数 */
export interface ITopJobConfigReq {
  bizType: number
  targetId: string
  provinces: number[]
  cities: number[]
}

/** 招工置顶配置查询返回结果 */
export interface ITopJobConfigRes {
  configList: {
    configId: number
    title: string
    planEndTime: string
    days: number
    defaultShow: boolean
    extInfo: {
      exposureNum: number
      promotionNum: number
      refreshNum: number
    }
    originPrice: number
    discountPrice: number
    originCashPrice: number
    discountCashPrice: number
    label: string
  }[]
  maxProvinceNum: number
  maxCityNum: number
  defaultAreaId: number
}

/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-01-20 17:04:14
 * @Description: 微信授权登录
 */
import { SERVER_PHONE, getAgreementUrl } from '@/config/app'
import { helper, tools } from '@/utils/index'
import { MapStateToData, connect, dispatch, actions, store } from '@/store/index'
import { LOGIN_LOGO } from '../common/data'
import { toLogin } from '@/utils/helper/common/toLogin'

const { login } = helper
const { validator } = tools

const { isPhone } = validator
const { jumpTo, logOutModel, afterLogin } = login

const mapStateToData: MapStateToData = (state) => {
  const { storage } = state
  return {
    loginAuthData: storage.loginAuthData,
    track_seed_share: storage.track_seed_share, // track_seed_share
    sourceCode: storage.sourceCode, // refId
    sourceShare: storage.source_share, // 分享小程序的source
  }
}

Component(
  connect(mapStateToData)({
    // 组件的属性列表
    properties: {
      // 微信登录认证类型 默认为1为快捷登录，2为手机号登录
      authType: {
        type: Number,
        value: 1,
      },
      // 判断是否为页面
      isPage: {
        type: Boolean,
        value: false,
      },
    },

    // 组件的初始数据
    data: {
      // 快捷登录加载状态
      kjpageLoading: false,
      // 登录页面加载状态
      pageLoading: false,
      // 登录code
      loginCode: '',
      // 电话号码
      phone: '',
      // 验证码
      code: '',
      // 错误提示
      errorTip: '',
      // 是否点击协议
      isCheckAgreement: false,
      // 点击的输入框
      active: '',
      // logo
      LOGIN_LOGO,
    },

    lifetimes: {
      // 在组件实例进入页面节点树时执行
      attached() {
        this.getUserLoginCode()
      },
      // 销毁的时候通知其他地方登录成功还是失败
      detached() {
        // 登录失败
        if (!store.getState().storage.userState.login) {
          toLogin.callReject()
        }
        this.timer && clearTimeout(this.timer)
      },
    },

    // 组件的方法列表
    methods: {
      // 登录之后用户数据处理
      async newInterfaceDealInfo(resp, newMember) {
        const user = {
          userId: Number(resp.data.user_id),
          token: resp?.data?.sign?.token,
          tokenTime: resp?.data?.sign?.time,
          uuid: resp.data.uuid,
          login: true,
          tel: resp.data.tel,
          role: resp.data.role,
          newMember,
        }
        afterLogin(user)
        if (this.data.isPage) {
          jumpTo()
        } else {
          this.triggerEvent('hideLogin')
        }
      },
      // 获取wx登录的临时凭证，每一个只能用一次
      getUserLoginCode() {
        return new Promise((resolve, reject) => {
          wx.login({
            success: (res) => {
              resolve(res)
              this.setData({ loginCode: res?.code })
            },
            fail: (err) => {
              reject(err)
              wx.$.alert({ content: '抖音账号登录失败，请重新登录' })
            },
          })
        })
      },
      // 切换快捷登录
      onFastLogin() {
        this.setData({ kjpageLoading: true })
      },
      // 登录之后返回
      onBack() {
        wx.$.r.back()
      },
      // 获取用户的已经绑定微信的手机号码
      onGetPhoneNumber(e) {
        if (e.detail.errMsg !== 'getPhoneNumber:ok') {
          this.setData({
            authType: 2,
            kjpageLoading: false,
          })
        } else {
          const { iv, encryptedData } = e.detail
          this.setData({
            kjpageLoading: false,
          })
          wx.login({
            success: (res) => {
              if (res?.code) {
                this.userAuthRequest({ iv, ciphertext: encryptedData, code: res?.code })
              }
            },
            fail(err) {
              wx.$.alert({ content: JSON.stringify(err) })
            },
          })
        }
      },
      // 字节授权登录
      async userAuthRequest(params) {
        wx.showLoading({ title: '登录中...' })
        const data = { ...params, refid: this.data.sourceCode, share_source: this.data.sourceShare }
        wx.$.fetch['POST/login/mobile/douyinLogin'](data).then(res => {
          wx.hideLoading()
          if (res.data.token) {
            this.detailUserLoginInfo(res)
          } else {
            wx.$.msg(res.head.msg)
          }
        }).catch(res => {
          wx.hideLoading()
          wx.$.msg(res.head.msg)
        })
      },
      // 验证手机号及验证码
      validataPhone() {
        let errorTip = ''
        if (!isPhone(this.data.phone)) {
          errorTip = '请输入正确的手机号'
        } else if (!this.data.code) {
          errorTip = '请输入验证码'
        } else if (this.data.code.length > 0 && this.data.code.length < 4) {
          errorTip = '验证码错误!'
        } else {
          errorTip = ''
        }
        return errorTip
      },
      // 电话号码失去焦点
      onBlurPhone(e) {
        if (!isPhone(e.detail.value)) {
          this.setData({
            errorTip: '请输入正确的手机号',
          })
        }
        // 判断若从手机号失去焦点时，active才置为空，兼容click和blur执行速度不一致问题
        if (this.data.active == 'phone') {
          this.setData({
            active: '',
          })
        }
      },
      // 验证码失去焦点
      onBlurCode() {
        const err = this.validateAll()
        if (err) {
          this.setData({
            errorTip: err,
          })
        }
        // 判断若从验证码失去焦点时，active才置为空，兼容click和blur执行速度不一致问题
        if (this.data.active == 'code') {
          this.setData({
            active: '',
          })
        }
      },
      validateAll() {
        let err = this.validataPhone()
        if (!err) {
          const { code } = this.data
          if (!code) {
            err = '请输入验证码'
          } else if (code.length > 0 && code.length < 4) {
            err = '验证码错误！'
          }
        }
        return err
      },
      // 点击输入框
      onClick(e) {
        const { type } = e.target.dataset
        if (type === this.data.active) {
          return
        }
        this.setData({
          active: type,
        })
      },
      // 输入手机号验证码
      onChange(e) {
        const { type } = e.target.dataset
        const { value } = e.detail
        this.setData({
          [type]: value,
          errorTip: '',
        })
      },
      // 清除电话号码及验证码
      onClear(e) {
        const { type } = e.target.dataset
        this.setData({
          [type]: '',
          errorTip: '',
        })
      },
      // 展示验证码错误信息
      onShowErrorTip(val) {
        this.setData({
          errorTip: val.detail,
        })
      },
      // 登录按钮
      async onLogin() {
        const errorTip = this.validateAll()
        if (errorTip) {
          this.setData({
            errorTip,
          })
          return
        }
        if (!this.data.isCheckAgreement) {
          wx.$.msg('请阅读并勾选下方协议')
          return
        }
        const data = {
          tel: this.data.phone,
          code: this.data.code,
          refid: this.data.sourceCode,
          share_source: this.data.sourceShare,
        }
        const authObj = this.data.loginAuthData
        wx.$.fetch['POST/login/mobile/codeLogin'](data, { headers: { ...authObj }, hideErrCodes: ['toCodeLogin'] }).then(res => {
          wx.hideLoading()
          this.detailUserLoginInfo(res)
        }).catch(res => {
          this.detailErrCode(res)
        })
      },
      /**
       * 处理授权登录异常情况
       * @param res 异常值
       */
      async detailErrCode(res) {
        wx.hideLoading()
        if (res?.head?.code == 500 && res.data?.errcode === 'toCodeLogin') {
          await this.getUserLoginCode()
          // 理论上errorcode为toCodeLogin时出现，手机号已绑定其他鱼泡账号，请使用手机号验证码登录
          wx.showModal({
            title: '快捷登录失败',
            content: res.head.msg,
            confirmText: '前往登录',
            showCancel: false,
            success: (res) => {
              if (res.confirm == true) {
                this.setData({
                  authType: 2,
                })
              }
            },
          })
        } else {
          wx.$.msg(res.head.msg)
          await this.getUserLoginCode()
          this.setData({
            kjpageLoading: false,
          })
        }
      },
      // 鱼泡登录验证账号
      async detailUserLoginInfo(res) {
        if (res?.head?.code == 200) {
          wx.$.fetch['GET/job/member/init'](
            {
              new_member: res?.data?.new_member,
              origin: res?.data?.origin,
              refid: this.data.sourceCode,
              share_source: this.data.sourceShare,
            },
            {
              headers: { singletoken: res?.data?.token },
              hideErrCodes: ['member_shielding', 'freeze'],
              hideMsg: true,
            },
          ).then(resp => {
            if (resp?.head?.code == 200) {
              dispatch(actions.userActions.setState({ isRefeshData: { status: true, id: 0 } }))
              this.newInterfaceDealInfo(resp, res?.data?.new_member)
            }
          }).catch(async (resp) => {
            if (resp?.head?.code == 500 && resp.data?.errcode == 'member_shielding') {
              await this.getUserLoginCode()
              this.setData({
                kjpageLoading: false,
              })
              // 拉黑用户弹窗
              wx.$.confirm({
                content: resp.head.msg,
                cancelText: '知道了',
                confirmText: '联系客服',
              }).then(() => {
                wx.$.u.callPhone(SERVER_PHONE)
              })
            } else if (resp?.head?.code === 500 && resp.data?.errcode === 'freeze') {
              // 注销账号弹窗
              logOutModel(resp, (result) => this.newInterfaceDealInfo(result))
            } else {
              wx.$.msg(res.head.msg)
            }
          })
        }
      },
      // 自定义导航返回事件
      onNavBack() {
        wx.hideToast()
        this.setData({
          authType: 1,
        })
      },
      // 使用一键快速登录
      onToggleLogin() {
        wx.hideToast()
        this.setData({ authType: 1 })
      },
      // 跳转隐私
      onToGreement(e) {
        const { type } = e.target.dataset
        wx.$.r.push({ path: `/subpackage/web-view/index?url=${getAgreementUrl(type)}` })
      },
      // 点击协议
      onCheckAgreement() {
        this.setData({
          isCheckAgreement: !this.data.isCheckAgreement,
        })
      },
    },
  }),
)

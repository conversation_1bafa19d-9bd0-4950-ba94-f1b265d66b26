/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 当前选择置顶范围组件
 */
import { dispatch, actions } from '@/store/index'

Component({
  properties: {
    /** 路由参数 */
    query: { type: Object, value: {} },
    /** 收集用户数据的集合 */
    form: {
      type: Object,
      value: {
        city: [],
      },
    },
    /** 招工置顶全局配置信息 */
    topConfig: {
      type: Object,
      value: {},
    },
    /** 来源，参数：recruit/resume */
    origin: {
      type: String,
      value: 'recruit',
    },
    /** 禁止选择的城市，传入数组id */
    disabledCities: {
      type: Array,
      value: ['33', '34', '35'],
    },
  },
  data: {

  },
  methods: {
    /** 跳转至城市选择页 */
    onHandleJump() {
      /** 选择城市页面需要的Model参数 */
      const config = {
        selectCities: this.data.form.city,
        maxCity: this.data.topConfig.max_city,
        maxProvince: this.data.topConfig.max_province,
        maxNumberTips: this.data.topConfig.max_number_tips,
        disabledCities: this.data.disabledCities,
        origin: this.data.origin,
      }
      dispatch(actions.topCityActions.setState(config))
      /** 跳转城市选择页面 */
      wx.$.r.push({
        path: '/subpackage/topset/topcity/index',
      })
    },
    /** 监听删除操作 */
    onHandleDel({ currentTarget: { dataset: { index } } }) {
      const city = [...this.data.form.city]
      city.splice(index, 1)
      this.triggerEvent('setFormData', {
        form: {
          ...this.data.form,
          city,
        },
      })
    },
    // 选择其他置顶信息
    onClickOtherInfo() {
      // 埋点上报
      const reportParams: any = {
        page_name: '招工置顶',
        click_button: '选择其他信息',
      }
      wx.$.collectEvent.event('miniPageClick', reportParams)
      this.triggerEvent('onOpenOtherToTopInfo', { })
    },
  },
})

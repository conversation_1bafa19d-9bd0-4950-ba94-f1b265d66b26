/*
 * @Author: gyf
 * @Date: 2022-02-09 08:55:29
 * @LastEditors: gyf
 * @LastEditTime: 2022-03-15 16:29:42
 * @FilePath: /yupao_mini_taro_recode-1.2.1-yh/src/swan-sitemap/resume/index.js
 * @Description:
 */
import { app } from '@/config/index'

Page({
  data: {
    detailData: {},
    footerTitle: '',
  },

  async onLoad(option) {
    const { id } = option
    const { data } = await wx.$.fetch['GET/jlist/pc/seo/secondDetail']({ id })
    this.setData({
      detailData: data,
      cityList: data?.cityList?.flat(),
      footerTitle: `查看更多${data.city.name}职位信息>>`,
    })
    const { data: tdk } = await wx.$.fetch['GET/jlist/seo/getTdkForRecruitmentList']({ id })

    wx.$.setTDK({
      title: tdk.title,
      description: tdk.desc,
      keywords: tdk.keywords,
    })
  },

  /** 点击底部查看更多 */
  onFooterBtn() {
    wx.$.r.push({ path: '/pages/index/index' })
  },

  /** 跳转招工大全详情 */
  clickItem({ target: { dataset: { item } } }) {
    if (item) {
      wx.$.r.replace({ path: '/swanrecruitseo/recruitListDetailSeo/index', query: { id: item.href.replace(/\D/g, '') } })
    }
  },
})

/*
 * @Date: 2022-2-10 14:00:00
 * @Description: 联系记录
 * @path-query: tab_index: 0, 1(默认选择的tab)
 * request:谁联系过我   /jlist/focusMe/getContactMeList
 * request:我联系的人   /jlist/focusMe/getMeContactList
 */

import { storage, MapStateToData, connectPage, actions, dispatch } from '@/store/index'
import { publishPage } from '@/lib/mini-component-page/index'
import { PLTools } from '@/utils/index'
import resource from '@/components/behaviors/resource'

// eslint-disable-next-line max-len
import { callMyContactCardPhone, callMyContactCardPhoneReport, callMyContactPeopleMidPhone } from './utils-call'
import { getContactConfigApi, getContactRecordService, getMyContactDefaultActiveTabIdxApi } from './api-server'
import { updateDeduceViewRealPhoneCardInfo } from './util-card'
import { getFmtOnLoadQuery, publishFetchCall, publishFetchMyContact, showGetIntegralModal } from './utils'
import { callMidPhoneResume, operationMidCall } from './utils-popup'

import miniConfig from '@/miniConfig/index'
import { fetchResumeExist } from '@/utils/helper/resume/index'
import { initIsShowToppingBtnStatus } from '../who_see/components/brows-list/api-server'
import { isReportEvent, listExposure, reportFindWorkList, reportRecruitList4 } from '@/utils/helper/list/index'
import { getDom } from '@/utils/tools/common/index'

const mapStateToData: MapStateToData = (state) => {
  const { user, storage } = state
  const { myContactHistory } = storage.communicateTip
  const { login: isLogin, userId } = storage.userState
  const { to_auth: toAuth } = user.userInfo
  /** header-tip组件的显示状态 */
  return {
    isHeaderTip: myContactHistory,
    userId,
    isLogin,
    toAuth,
    role: state.storage.userChooseRole,
  }
}

/** @name 拨打记录 */
Page(
  connectPage(mapStateToData)(
    publishPage(['onShow', 'onReachBottom'])({
      ...(!ENV_IS_SWAN ? { behaviors: [resource] } : {}),
      isFirstRender: true,
      data: {
        ...(ENV_IS_SWAN ? resource.data : {}),
        query: {},
        // -------- 页面导航栏部分
        tabList: [
          { name: '我沟通过的', id: 'getMeContactList' },
          { name: '谁沟通过我', id: 'getContactMeList' },
        ],
        // 0-我联系的人 1-谁联系过我
        activeTabIdx: 0,
        showHeaderTip: false,
        ENV_SUB,
        contactConfig: {},
        // -------- 页面列表数据部分
        myContactInfo: PLTools.getDefaultPLData({}),
        contactMeInfo: PLTools.getDefaultPLData({}),
        // 是否加载失败-是否需要重新加载数据
        loadFail: false,

        // -------- 联系卡片弹窗相关数据
        // 点击卡片的临时保存数据
        tempCardItem: {},
        tempCardItemIdx: -1,
        // 中间号弹窗显示组合,call拨打电话,revise修改电话,write填写电话,real-copy获取真实手机号,evaluation评论,none弹窗隐藏
        showMiddleVisible: '',
        // ---- 评价弹窗所需数据
        // 是否展示底部 免费拨打 or 拨打, 聊一聊, 取消 弹窗
        isShowContactChooseDrawer: false,
        // ---- 中间号相关弹窗数据
        /** 中间号弹窗类型, 1=可选直接拨号，2=必须使用安全号 0=安全号不弹窗 */
        popType: 1,
        // 中间号弹窗文案
        popContent: [],
        /** 中间号弹窗文案及样式配置 */
        popNewContent: {},
        popContentData: {
          /** 是否消耗积分查看该信息：0-未消耗，1-已消耗。备注：只用于中间号交流弹窗。 */
          is_expense_integral: 0,
          /* 标识本次查看电话动作，是否真实消耗了积分。0-未消耗，1-已消耗。 */
          has_expense_integral: 0,
          /** 招工本次弹窗新样式 */
          is_new_popup: 0,
          /** 是否展示气泡 */
          show_bubble: 0,
        },
        // ---- 针对工厂 扣费弹窗
        // 是否显示扣费弹窗--- 只针对工厂专区
        isShowDeductionPop: false,
        // 扣费弹窗的内容
        deductionContent: {},
        // 扣费弹窗的积分数
        deductionNum: 0,
        // 扣费弹窗的类型
        factoryDeductionType: 0,
        // ---- 充值弹窗
        // 是否弹出充值弹窗
        isShowRecharge: false,
        // 充值弹窗的积分差值描述
        textList: [],

        /** 中间号拨打的那条信息 */
        item: {},
        // -----------------  提取的 wrapper-ward data
        /** 1 为招工， 2为找活  */
        source: 1,

        // -------- 真实号码评价弹窗
        isMidGetTel: { isShow: false, tel: '' },

        // -------- 站内信互评
        // 站内信互评进入
        ext: {},
        // 是否是百度打完电话回到当前页面
        isSwanPhoneBack: false,

        /** 资源位标识对应的数据key */
        resourceKeys: {
          float_contactlist_under: 'buoyUnder',
          float_contactlist_top: 'buoyTop',
        },
        /** 资源位——浮标上 */
        buoyTop: null,
        /** 资源位——浮标下 */
        buoyUnder: null,
        /** 广告的unitId */
        advertUnitId: '',
        isShowTopping: false, // 名片是否在置顶
        hasResume: false, // 是否有名片
        /** 是否已发布职位-- 谁沟通过我的 */
        isPublished: true,
        /** 是否有职位-- 我沟通过谁的 */
        isExistRecruit: true,
      },
      callTel: '',
      middleData: {},
      async onLoad(query) {
        const fmtQuery = getFmtOnLoadQuery(query)
        // 如果是百度从打电话回来
        if (ENV_IS_SWAN && fmtQuery.isSwanGo == 'yes') {
          this.commonShowCopyPop(fmtQuery.item, fmtQuery.currentIndex)
        }
        this.initContactPageInfo(fmtQuery)
        ENV_IS_SWAN && resource.lifetimes.attached.call(this)
        const abOk = await wx.$.u.getAbUi('WX_xiaochengxu', 'xiaochengxuNB')
        const { exist } = await fetchResumeExist()
        const isShowTopping = exist ? await initIsShowToppingBtnStatus() : false
        this.setData({
          advertUnitId: abOk ? miniConfig.advert.callAndBrowseUnitId : '',
          isShowTopping,
          hasResume: exist,
        })
        /** 检测是否已发布职位 -- 谁沟通过我的 */
        publishFetchCall.call(this)
        /** 检测是否已发布职位 -- 谁沟通过我的 */
        publishFetchMyContact.call(this)
      },
      onShow() {
        ENV_IS_SWAN && resource.pageLifetimes.show.call(this)
        // 如果是百度从打电话回来
        const { activeTabIdx, isSwanPhoneBack } = this.data
        const item = this.data.tempCardItem
        if (ENV_IS_SWAN && isSwanPhoneBack) {
          const query = { isSwanGo: 'yes', item, currentIndex: activeTabIdx }
          wx.$.router.replace({ path: '/subpackage/member/myContactHistory/index', query })
        }
        const { infoId, subUuid, expenseId: expense_id, userId: user_id, identityType } = item
        const fmtItemData = { infoId, subUuid, identityTypeCode: identityType?.code, expense_id, user_id }
        this.commonShowCopyPop(fmtItemData, activeTabIdx)
        // 首次渲染退出并更改状态
        if (this.isFirstRender) {
          this.isFirstRender = false
          return
        }
        // 滚动到的位置（距离顶部 px）// 滚动所需时间 如果不需要滚动过渡动画，设为0（ms）
        wx.pageScrollTo({ scrollTop: 0, duration: 0 })
        this.onService(true)
      },

      onHide() {
        this.onPageHideReport()
      },

      /** 退出页面时曝光 */
      onUnload() {
        this.onPageHideReport()
      },

      ...(ENV_IS_SWAN ? resource.methods : {}),
      onPullDownRefresh() {
        const { activeTabIdx, myContactInfo, contactMeInfo } = this.data
        if (PLTools.isLoadingState(activeTabIdx == 0 ? myContactInfo : contactMeInfo)) {
          wx.stopPullDownRefresh()
          return
        }
        this.onService(true).finally(() => wx.stopPullDownRefresh())
      },
      onReachBottom() {
        const { activeTabIdx, myContactInfo, contactMeInfo } = this.data
        if (PLTools.isLoadingOrFinishState(activeTabIdx == 0 ? myContactInfo : contactMeInfo)) {
          return
        }
        this.onService(false)
      },

      // ======== 页面进入有可能执行的函数
      /**
       * @name 注意路径参数兼容问题
       * @param item
       * @param activeTabIdx
       */
      async commonShowCopyPop(item?, activeTabIdx?) {
        const isShowPopup = storage.getItemSync('isShowPopup')
        const { infoId, subUuid } = item
        // 中间号通话结束弹窗 -- 只有我联系的人 才会弹该弹窗
        const currentPage = wx.$.r.getCurrentPage()
        if (isShowPopup == currentPage.route && activeTabIdx === 0) {
          const isBoss = item.identityTypeCode == 1
          // todo 中间号拨打逻辑自测
          const infoType = isBoss ? 'job' : 'resume'
          const nItem = wx.$.u.deepClone(item)
          await wx.$.l.showMidPopup(0, infoType, isBoss ? infoId : subUuid, nItem)
        }
        this.setData({ item: {} })
      },

      // ======== 页面列表相关
      async initContactPageInfo(fmtQuery) {
        const contactConfig = await getContactConfigApi()
        const defaultActiveTabIdx = await getMyContactDefaultActiveTabIdxApi(fmtQuery)
        this.setData({ contactConfig, activeTabIdx: defaultActiveTabIdx, query: fmtQuery, ext: fmtQuery.ext })
        this.onService(true)
      },
      // 获取招工找活记录数据
      onService(isRefresh = false) {
        return getContactRecordService.call(this, isRefresh).then((resultData) => {
          this.setData(resultData)
          this.onListScroll(resultData)
        })
      },
      // tab栏切换
      tabIdxChange(e) {
        const currIdx = e.detail.currentIndex
        const { myContactInfo, contactMeInfo, activeTabIdx } = this.data
        if (currIdx === activeTabIdx) {
          return
        }
        this.setData({ activeTabIdx: currIdx })
        if (PLTools.hasPageListData(currIdx ? contactMeInfo : myContactInfo)) {
          wx.$.selectComponent.call(this, '#evaluation-activity').then((comp) => comp.getDate())
          return
        }
        this.onService(false)
      },
      // 未开启评价活动
      closeActivity(e) {
        this.setData({ showHeaderTip: !e.detail.showActivity })
      },

      // ======== 卡片相关
      // 点击联系
      onClickContactBtn({ detail: { item: tempCardItem, index: tempCardItemIdx } }) {
        this.setData({ tempCardItem, tempCardItemIdx })
        const { isChat } = tempCardItem
        // 不用删除可能点击会导致点击弹窗显示
        const canShowPopup = ENV_IS_WEAPP && isChat
        // 我联系的人 存在评论 或 聊一聊 展示底部弹出框
        if (canShowPopup) {
          this.onFreeCall()
          // this.setData({ isShowContactChooseDrawer: true })
          return
        }

        // 谁联系过我 不展示聊一聊，直接走中间号流程
        if (this.data.activeTabIdx === 1) {
          this.whoContactMeCallPhone()
          return
        }
        callMyContactCardPhone.call(this)
      },
      // 谁联系过我拨打电话功能
      whoContactMeCallPhone() {
        const { tempCardItem, activeTabIdx } = this.data
        const { infoId, identityType, pullBackSwitch, userId } = tempCardItem || {}
        const { code } = identityType || {}
        const params = {
          toUserId: userId,
          infoId,
          infoType: code == 1 ? 2 : 1,
          getPrivacyTel: false,
        }
        if (!pullBackSwitch) {
          wx.$.l.recall(params, {
            pageName: '联系记录',
            infoId,
            source: activeTabIdx == 1 ? 2 : code,
          }, {
            failReport: () => {
              code == 2 && callMyContactCardPhoneReport.call(this, '0', 1, { sourceId: '23' })
            },
            successReport: (resReport) => {
              const { data } = resReport || {}
              const { isFirstView } = data || {}
              code == 2 && callMyContactCardPhoneReport.call(this, isFirstView == 1 ? '1' : '2', 1, { sourceId: '23' })
            },
          })
        } else {
          wx.$.l.recallV3(infoId, userId, false, {
            pageName: '联系记录',
            infoId,
            source: activeTabIdx == 1 ? 2 : code,
            fn: {
              failReport: () => {
                code == 2 && callMyContactCardPhoneReport.call(this, '0', 1, { sourceId: '23' })
              },
              successReport: (resReport) => {
                const { data } = resReport || {}
                const { isFirstView } = data || {}
                code == 2 && callMyContactCardPhoneReport.call(this, isFirstView == 1 ? '1' : '2', 1, { sourceId: '23' })
              },
            },
          }, code == 1)
        }
      },
      // 真实手机号
      onClickRealPhoneBtn(e) {
        const { tempCardItem: TCI } = this.data
        const tempCardItem = e?.detail?.item || TCI
        const clickItemIdxObj = typeof e?.detail?.index === 'number' ? { tempCardItemIdx: e.detail.index } : {}
        const isDeduceIntegral = e?.detail?.isDeductViewRealPhoneIntegral
        const tempObj = isDeduceIntegral ? updateDeduceViewRealPhoneCardInfo(this.data, { item: tempCardItem }) : {}
        this.setData({ ...clickItemIdxObj, ...tempObj, showMiddleVisible: 'real-copy', tempCardItem, isMidGetTel: null })
      },
      // 评价
      onClickEvaluateBtn({ detail: { item: tempCardItem, index: tempCardItemIdx } }) {
        this.setData({ showMiddleVisible: 'evaluation', isShowContactChooseDrawer: false, isMidGetTel: null, tempCardItem, tempCardItemIdx })
      },
      // 修改合作意向
      updateCooperation({ detail: { item } }) {
        if (this.data.activeTabIdx === 1) {
          // 谁联系过我
          const { contactMeInfo } = this.data
          const list = contactMeInfo.list.map((i) => {
            if (i.id == item.id) {
              i.cooperationType = item.cooperationType
            }
            return i
          })
          this.setData({ 'contactMeInfo.list': list })
        } else {
          // 我联系的人
          const { myContactInfo } = this.data
          const list = myContactInfo.list.map((i) => {
            if (i.id == item.id) {
              i.cooperationType = item.cooperationType
            }
            return i
          })
          this.setData({ 'myContactInfo.list': list })
        }
      },
      // 空状态
      goToRecruitPage() {
        wx.$.router.push({ path: '/pages/index/index' })
      },
      // 空状态
      goToResumePage() {
        wx.$.router.push({ path: '/pages/resume/index' })
      },
      goToPublishRecruitPage() {
        wx.$.router.push({ path: '/subpackage/recruit/fast_issue/index/index', query: { ptype: 'perfect' } })
      },
      goToPublishResumePage() {
        wx.$.router.push({ path: '/subpackage/resume/resume_publish/index' })
      },
      // 去加急
      async toTopping() {
        // wx.$.r.push({ path: '/subpackage/topset/topmset/index', query: { top_from: 'view_resume' } })
        const urlTop = encodeURIComponent('/urgent-resume?epc=active_jiajijianlih5')
        wx.$.r.push({
          path: `/subpackage/web-view/index?url=${urlTop}&isLogin=true`,
        })
      },

      // ======== 底部拨打选择弹窗(免费拨打/拨打 聊一聊 取消)
      // 免费联系 or 拨打
      onFreeCall() {
        this.onCloseDrawer()
        if (this.data.activeTabIdx == 1) {
          this.whoContactMeCallPhone()
          return
        }
        this.onCallPhone()
      },
      // 聊一聊
      async onGetChat() {
        this.onCloseDrawer()
        const { tempCardItem } = this.data
        const { identityType, infoId, subUuid } = tempCardItem
        // 招工
        if (identityType?.code == 1) {
          await wx.$.l.initGroup(infoId)
          return
        }
        // 找活
        dispatch(actions.timmsgActions.setState({ resumesInfo: tempCardItem }))
        await wx.$.l.initGroup(subUuid, 2)
      },
      // 关闭弹窗
      onCloseDrawer() {
        this.setData({ isShowContactChooseDrawer: false })
      },

      // ======== 中间号弹窗 ☎️☎️
      // 组件回调拨打中间号-这里区分谁联系过我，我联系的人
      async onCallMidPhone({ detail }) {
        // 谁联系过我
        if (this.data.activeTabIdx === 1) {
          const { tempCardItem, activeTabIdx } = this.data
          const { userId, infoId, identityType } = tempCardItem || {}
          const { code } = identityType || {}
          const params = {
            toUserId: userId,
            infoId,
            infoType: identityType.code == 1 ? 2 : 1,
            getPrivacyTel: false,
          }
          wx.$.l.recall(params, {
            pageName: '联系记录',
            infoId,
            source: activeTabIdx == 1 ? 2 : code,
          })
          return
        }
        callMyContactPeopleMidPhone.call(this, detail)
      },
      // 中间号填写(绑定手机号)
      onBinnedPhone() {
        operationMidCall.call(this, this.middleData)
      },
      // 中间号修改(修改手机号成功)
      async onReviseMidPhone() {
        // this.setData({ showMiddleVisible: 'call' })
        // const { specialType, infoId } = this.data.tempCardItem
        // const special_type = specialType != '3' ? 1 : 3
        // const reqParams: any = { id: infoId, special_type, is_privacy: 1, call_tel: 0, look_is_ended: 1 }
      },
      // 关闭修改手机号
      onReviseMidPhoneClose() {
        this.setData({ showMiddleVisible: 'call' })
      },
      // 修改拨出手机号 -- pop-phone-call 组件内无触发该事件的 revise (具体查看页面逻辑文件是否有调用的地方)
      onRevise() {
        this.setData({ showMiddleVisible: 'revise' })
      },

      // ======== 针对工厂 扣费弹窗
      // 工厂--扣费1号2号弹窗的监听事件
      onCallContinue(e) {
        this.onCloseDeductionPopup()
        callMidPhoneResume.call(this, { detail: e.detail || 2 })
      },
      // 关闭扣费弹窗
      onCloseDeductionPopup() {
        this.setData({ isShowDeductionPop: false })
      },

      // ======== 真实手机号弹窗
      // 打开充值弹窗
      onOpenRechargePopup({ detail: { textList } }) {
        this.setData({ isShowRecharge: true, textList })
      },
      onClosePagePopup() {
        this.setData({ showMiddleVisible: '' })
      },

      // ======== 评分弹窗(😯)
      // 评价成功的回调
      onEvaluationSuccess() {
        this.onClosePagePopup()
        this.onRefresh({ detail: { ealuationSuccess: true } })
      },

      // ======== 站内信互评
      onRefresh(e) {
        if (e.detail?.ealuationSuccess) {
          this.onPageRefresh()
        }
        wx.$.selectComponent.call(this, '#evaluation-activity').then((comp) => comp.getDate())
        const { activeTabIdx, myContactInfo, contactMeInfo, tempCardItem, tempCardItemIdx } = this.data
        if (tempCardItemIdx === -1) {
          this.onService(true)
          return
        }

        const pageList = activeTabIdx ? contactMeInfo.list : myContactInfo.list
        pageList[tempCardItemIdx] = { ...tempCardItem, canComment: false }
        this.setData({ [activeTabIdx ? 'contactMeInfo.list' : 'myContactInfo.list']: pageList })
      },
      // 关闭充值弹窗
      onCloseRechargePopup() {
        this.setData({ isShowRecharge: false, textList: [] })
      },

      // ============ other
      // 可以 聊一聊 且 拨打电话按钮 时展示该弹窗
      showClickContactPopup() {
        this.setData({ isShowContactChooseDrawer: true })
      },
      // 重新加载数据-其他页面会调用该方法做数据刷新所使用
      onPageRefresh() {
        this.setData({ loadFail: true })
      },
      // 拨打电话
      async onCallPhone(isAfterRefund = 0) {
        const { activeTabIdx, tempCardItem } = this.data
        const { identityType, infoId: id, subUuid } = tempCardItem
        const isRecruit = identityType?.code == 1
        const infoId = isRecruit ? id : subUuid
        // 我联系的人
        if (activeTabIdx === 0) {
          // 调用 contactCard.callPhone 需要 this.middleData 数据与 this.callMidPhone 方法
          await wx.$.l.callPhone.call(this, isRecruit, infoId, {
            isAfterRefund,
            scene: 5,
            midext: {
              infoId,
              source: activeTabIdx == 1 ? 2 : identityType.code,
              pageName: '我联系的人',
            },
          })
        }
      },
      // 拨打中间号 -- 不能修改名称有的地方内部通过 this 会调用该方法名称
      callMidPhone(factoryData, tel, res?) {
        const { specialType, identityType } = this.data.tempCardItem
        //! 如果是工厂找活，走扣费逻辑
        if (specialType == 2 && identityType.code == 2) {
          const deductionContent = factoryData?.factory_popup_content
          if (deductionContent && Object.keys(deductionContent).length > 0) {
            this.setData({ isShowDeductionPop: true, deductionContent })
            return
          }
          this.setData({ isShowDeductionPop: false })
        }
        const errCode = factoryData?.errcode
        // 弹出--「积分消耗提醒-拨打电话」弹窗
        if (errCode === 'after_refund_look') {
          wx.$.confirm({ content: res.head?.msg || '', confirmText: '联系老板', cancelText: '取消' }).then(() => this.onCallPhone(1))
          return
        }

        // 积分不足
        if (errCode === 'get_integral') {
          showGetIntegralModal.call(this, res)
          return
        }
        operationMidCall.call(this, factoryData, tel)
      },
      /** 监听列表滚动上报埋点 */
      async onListScroll(sData) {
        if (!this.listScrollTop) {
          const res = await getDom('#custom-header')
          this.listScrollTop = res && res.height
        }
        const { activeTabIdx } = this.data
        const currentData = sData[activeTabIdx == 1 ? 'contactMeInfo' : 'myContactInfo']
        listExposure.call(this, {
          page: currentData?.page || 1,
          elementId: `.${activeTabIdx == 1 ? 'contact-me' : 'my-contact'}`,
          top: -(this.listScrollTop || 0),
          callback: (res) => this.report(res),
        })
      },
      // 列表曝光埋点
      async report(res) {
        const { activeTabIdx } = this.data
        const { identityType } = res.item || {}
        if (identityType && identityType.code) {
          identityType.code == 1 && reportRecruitList4(res, {
            source: '联系记录',
            source_id: '11',
          })
          if (identityType.code == 2) {
            const { item } = res || {}
            const { userId, occupation, subUuid, resumeId } = item || {}
            const nRes = { ...res, item: { ...item, uuid: subUuid, id: resumeId, userInfo: { userId }, occupationStr: occupation.join(',') } }
            const classifys = await wx.$.l.getClassifyByNames(occupation || [])
            if (wx.$.u.isArrayVal(classifys)) {
              const classify = classifys[0] || {}
              nRes.item.occupation = { ...classify, occId: classify.id }
            }
            reportFindWorkList(nRes, activeTabIdx == 1 ? { source_id: '23', is_button_external: '0' } : { source_id: '22', is_button_external: '0' })
          }
        }
      },

      // 页面显隐上报
      onPageHideReport() {
        const { activeTabIdx, myContactInfo, contactMeInfo } = this.data
        const currentData = activeTabIdx == 1 ? contactMeInfo : myContactInfo
        isReportEvent.call(this, currentData.list, (res) => this.report.call(this, res))
      },
    }),
  ),
)

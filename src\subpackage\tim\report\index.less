page {
  background: #fff;
  min-height: 100vh;
}

.body {
  width: 100%;
  padding: 0 32rpx;
  .tiele {
    padding: 16rpx 0;
    display: flex;
    font-size: 34rpx;
    font-weight: bold;
    align-items: flex-end;
    color: rgba(0, 0, 0, 0.85);
    text {
      font-weight: 400;
      font-size: 30rpx;
      color: rgba(0, 0, 0, 0.45);
    }
  }
  .radio {
    width: 100%;
    .item {
      display: flex;
      align-items: center;
      padding: 28rpx 0;
      .icon {
        font-weight: bold;
      }
    }
    .label {
      font-size: 30rpx;
      margin-top: -1rpx;
      margin-left: 16rpx;
      color: rgba(0, 0, 0, 0.85);
    }
  }
  .remarks {
    padding: 32rpx 24rpx;
    width: 100%;
    background: #F5F6FA;
    border-radius: 8rpx;
  }
  .remark-content{
    height: 280rpx;
    width: 100%;
    font-size: 30rpx;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
  }
  .length {
    display: flex;
    font-size: 30rpx;
    justify-content: flex-end;
    align-items: flex-end;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    margin-top: 24rpx;
  }
  .btn {
    width: 100% !important;
    height: 96rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 64rpx;
    background: rgba(0, 146, 255, 1);
    border-radius: 12rpx;
    font-size: 34rpx;
    font-weight: bold;
    color: rgba(255, 255, 255, 1);
  }
  .disabled {
    opacity: 0.5;
  }
}
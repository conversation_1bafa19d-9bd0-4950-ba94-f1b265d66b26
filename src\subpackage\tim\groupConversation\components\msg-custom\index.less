.msg-txt-v {
  position: relative;
  display: flex;
  align-items: center;
}
.msg-txt-v-l {
  height: 100%;
}
.msg-txt-v-r {
  height: 100%;
}

.msg-txt-v-l:before,
.msg-txt-v-r:after,
.msg-txt-v-r-w::after {
  /*用伪类写出小三角形*/
  content: '';
  display: block;
  width: 0;
  height: 0;
  border: 8px solid transparent;
  position: absolute;
  top: 15px;
}
/*分别给左右两边的小三角形定位*/
.msg-txt-v-l:before {
  border-right: 8px solid #ffffff;
  left: -12px;
}
.msg-txt-v-r:after {
  border-left: 8px solid #0092ff;
  right: -12px;
}

.msg-txt-v-r-w:after {
  border-left: 8px solid #ffffff;
  right: -12px;
}

.msg-txt {
  position: relative;
  max-width: 474rpx;
  padding: 24rpx;
  background: #0092ff;
  border-radius: 24rpx 24rpx 4rpx 24rpx;
  color: rgba(255, 255, 255, 0.95);
  font-size: 34rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 48rpx;
  min-height: 96rpx;
  display: flex;
  align-items: center;
}

.msg-other {
  background: #ffffff;
  color: rgba(0, 0, 0, 0.85);
  border-radius: 24rpx 24rpx 24rpx 4rpx;
}

.arrow {
  position: absolute;
  top: 19px;
  width: 0;
  height: 0;
  font-size: 0;
  border: solid 8rpx;
}

.arrow-r {
  right: -8px;
  border-color: transparent transparent transparent #0092ff;
}

.arrow-r-w {
  border-color: transparent transparent transparent #ffffff;
}

.arrow-l {
  left: -8px;
  border-color: transparent rgba(0, 0, 0, 0.05) transparent transparent;
}

.custom-btn {
  display: contents;
}

.callphone-click {
  color: #0092ff;
}

.dom_em {
  display: inline;
  word-wrap: break-word;
  word-break: break-all;
}

.dom_em_v {
  height: 100%;
}

.js-hetong-v {
  height: 196rpx;
  width: 414rpx;
  padding: 24rpx;
  background: #fff;
  font-size: 28rpx;
  border-radius: 4rpx;
}

.js-hetong-title {
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
}

.js-hetong-detail {
  color: rgba(0, 0, 0, 0.65);
  margin-top: 16rpx;
}

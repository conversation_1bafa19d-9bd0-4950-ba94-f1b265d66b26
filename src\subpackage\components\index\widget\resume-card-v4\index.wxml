<view class="resume-card-component {{no_m_b ? 'no_margin_bottom' : ''}} {{item.viewed&&'viewed'}} custom-class" bind:tap="onClickToDetail">
  <view class="resume-container">
    <view class="resume-header">
      <view class="header-left">
        <!-- 头部信息 -->
        <view class="header-left-top">
          <view class="name">{{isVague ? userName : item.name}}</view>
          <!-- <view wx:if="{{item.realNameStatus==2}}" class="real-name-tag" catch:tap="onShowModal">已实名</view>
          <view wx:if="{{item.isTop}}" class="top-tag">加急</view>
          <block wx:if="{{item.rightInfo.hasTelRight}}">
            <view style="white-space: nowrap;" class="text">已联系</view>
          </block>
          <block wx:else>
            <view wx:if="{{item.online}}" class="dot" />
            <text style="white-space: nowrap;" class="text {{!item.online&&'not-online'}}">{{item.online ? '在线': item.activeStatusText}}</text>
          </block> -->
          <!-- 简历大列表，简历搜索列表，简历满足定价方案设置的信息标签“特惠拨打”
            简历配置了打电话按钮
            简历无生效中的打电话权益 -->
          <image src="https://cdn.yupaowang.com/yupao_mini/discout-call-flag.png" class="discount-call-flag" wx:if="{{(origin === 'resumeIndex' || origin === 'searchResult') && item.isSpecialTel  && ((btnObj.isShow && btnObj.type === 'phone') || detailHasCallBtn) && (item.rightInfo && !item.rightInfo.hasTelRight) }}" />
          <view class="head-tag-box">
            <view wx:if="{{item.isTop}}" class="head-tag tag-red">加急</view>
            <!-- <view wx:if="{{item.realNameStatus == 2}}" class="head-tag">已实名</view> -->
            <view wx:if="{{isActiveAb && item.userInfo.isNew}}" class="head-tag">新牛人</view>
            <view wx:if="{{item.rightInfo.hasTelRight}}" class="head-tag tag-gray">已联系</view>
            <block wx:if="{{!isActiveAb}}">
              <view wx:if="{{!item.rightInfo.hasTelRight && item.online}}" class="head-tag tag-green">
                <view class="tag-dot"></view>
                <view style="font-weight: bold;">在线</view>
              </view>
              <view class="hasComplainTag" wx:if="{{(item.complaintStatus == 1 || item.complaintStatus == 2 ) && isShowHasComplainTag}}">
                已投诉
              </view>
              <view class="hasCooperationTag" wx:if="{{item.cooperationType == 1 && isShowCooperationTag}}">
                <image class="hasCooperationTagIcon" src="https://cdn.yupaowang.com/yp_mini/images/gyf/mini_handshake.png" />
                有合作意向
              </view>
              <view wx:if="{{!item.online && item.activeStatusText}}" class="head-tag">
                {{item.activeStatusText}}
              </view>
            </block>
            <!-- 活跃标签 -->
            <block wx:if="{{isActiveAb}}">
              <block wx:for="{{item.userInfo.activeLabels}}" wx:key="index" wx:for-item="obj">
                <view wx:if="{{index == 0}}" class="head-tag tag-{{obj.color}}">
                  <view wx:if="{{obj.labelCode == 13}}" class="tag-dot"></view>
                  <view>{{obj.labelName}}</view>
                </view>
              </block>
            </block>
          </view>
        </view>
        <view class="header-left-bottom">{{item.userDesc}}</view>
      </view>
      <view class="header-right">
        <image class="image {{isVague ? 'filter': ''}}" mode='aspectFill' src="{{item.headPortrait}}" />
        <!-- <view wx:if="{{isVague}}" class="vague"></view> -->
      </view>
    </view>
    <view class="resume-mid-info" wx:if="{{item.occupationStr||item.forwardSalary}}">
      <view wx:if="{{salaryText || occupationStr}}" class="resume-mid-info-left">
        <view wx:if="{{occupationStr}}" class="occupation-str">{{occupationStr}}</view>
        <view wx:if="{{salaryText}}" class="salary-text">{{salaryText}}</view>
      </view>
    </view>
    <view class="resume-tags" wx:if="{{item.recallExtendTagList.length||item.applyPreference.length}}">
      <block wx:if="{{item.recallExtendTagList.length}}">
        <view class="primary-tag" wx:for="{{item.recallExtendTagList}}" wx:key="index" data-item="{{item}}" data-index="{{index}}">
          {{item}}
        </view>
      </block>
      <block wx:if="{{item.applyPreference.length}}">
        <view class="default-tag" wx:for="{{item.applyPreference}}" wx:key="index" data-item="{{item}}" data-index="{{index}}">
          {{item}}
        </view>
      </block>
    </view>
    <view class="resume-bottom">
      <text class="introduce-content">{{item.introduce}}</text>
      <view wx:if="{{showContentBtn && btnObj.isShow}}" class="resume-bottom-button" catch:tap="onCallPhone">
        <icon-font custom-class="icon" type="{{btnObj.type == 'chat'? 'yp-chat':'yp-re_phone'}}" size="24rpx" color="rgba(0, 146, 255, 1)" />
        <text class="text">{{btnObj.btnText}}</text>
      </view>
    </view>
  </view>
  <view wx:if="{{nearbyWorkerListApiSource == 'whoContactedMeToResume'}}" class="card-bottom">{{item.viewTimeStr}}</view>
  <view wx:if="{{nearbyWorkerListApiSource == 'BrowsingHistory'}}" class="card-bottom">
    <view wx:if="{{item.jobTitle}}" class="card-bottom-left">
      <view class="text">用</view>
      <view class="job-title">{{item.jobTitle}}</view>
      <view class="text">职位查看</view>
    </view>
    <view wx:if="{{item.viewTimeStr}}" class="card-bottom-right">{{item.viewTimeStr}}</view>
  </view>
</view>
/*
 * @Author: ji<PERSON><PERSON>
 * @LastEditors: Please set LastEditors
 */
import { helper } from '@/utils/index'

/**
 * 组件回调拨打中间号-我联系的人-找活
 * @param callType 用于决定是否返回中间号 2-使用中间号(虚拟号) 其它情况-使用真实号码(直接拨打电话)
 */
export async function callMidPhoneResume(callType) {
  await wx.$.u.waitAsync(this, callMidPhoneResume, [callType], 1500)
  try {
    const { subUuid: uuid } = this.data.tempCardItem
    // 1-直接拨打电话 2-虚拟号
    const { code, data } = await wx.$.l.resumeTelV3({ uuid, isPopup: 0, isPrivacy: callType == 2 ? 1 : 0, sceneV2: '5' }, { }, this.data.tempCardItem.buriedPointData)
    this.setData({ showMiddleVisible: '' })
    if (code != 0 || !data?.tel) {
      return
    }
    if (data.timeRemaining) {
      await wx.$.l.midPhoneTimeMsg(data.timeRemaining)
    }
    wx.$.u.callPhone(data.tel).catch(console.error)
  } catch (error) {
    console.error(error)
  }
}

/** 生成充值描述的信息 */
export function createIntegerDesc(msg?: string, rules?: any) {
  const newRules = rules?.map((rule) => ({ ...rule, length: rule.end }))
  return helper.common.initRuleTipsInfo(msg, newRules) || []
}

/**
 * 中间号相关弹窗--逻辑
 * @name 展示对应的中间号拨打弹窗
 */
export async function operationMidCall(data, tel?) {
  const { activeTabIdx } = this.data
  let { tempCardItem: item } = this.data
  // 评价页面兼容（评价页不叫tempCardItem）
  if (!item) {
    item = this.data?.item || this.data?.detailInfo
  }
  const popType = data?.pop_type
  if (!popType || popType == '0') {
    this.onClosePagePopup()
    // 拨打的是虚拟号提示有效时间
    if (data?.time_remaining) {
      await wx.$.l.midPhoneTimeMsg(data.time_remaining)
    }
    wx.$.u.callPhone(tel || data.tel)
    return
  }

  this.callTel = data.call_tel
  // 谁联系过我
  const isWhoContacted = item.identityType ? activeTabIdx === 1 : (item.titleType == 1 || item.titleType == 2)
  if (isWhoContacted) {
    this.setData({ showMiddleVisible: 'call', popType: 4, popContent: [{ color: '#000000', content: '为保证您的权益，请使用鱼泡安全号联系！' }] })
    return
  }

  const dataPopContent = data.pop_content || {}
  // 我联系过谁---招工
  const isBoss = item.identityType ? item.identityType.code == 1 : item.commentTargetType.code == 1
  if (isBoss) {
    const popContent = dataPopContent.content || []
    /** 3.7.1中间号弹窗-新增弹窗样式-后台可配置 */
    const { call_privacy_tel, call_real_tel } = dataPopContent
    const popNewContent = { call_privacy_tel, call_real_tel }
    // popContentData 参数对象部分
    const has_expense_integral = data.has_expense_integral || 0
    const is_expense_integral = data.is_expense_integral || 0
    const is_new_popup = dataPopContent.is_new_popup || 0
    const show_bubble = dataPopContent.show_bubble || 0
    const popContentData = { has_expense_integral, is_expense_integral, is_new_popup, show_bubble }
    this.setData({ showMiddleVisible: 'call', popType, popContent, popNewContent, popContentData })
    return
  }

  const popTypeContentKey = { 3: 'popup_three', 2: 'popup_two', 1: 'popup_one' }
  const popContent = dataPopContent[popTypeContentKey[popType]]
  this.setData({ showMiddleVisible: 'call', popType, popContent, popContentData: {} })
}

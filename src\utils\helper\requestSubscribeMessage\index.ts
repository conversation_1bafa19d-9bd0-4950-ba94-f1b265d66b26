/*
 * @Date: 2022-02-12 16:26:37
 * @Description: 消息订阅
 */

import { getState } from '@/store/index'
import config from '@/miniConfig/index'

const { subscribeMessage } = config

const templateIds = {
  message: {
    id: subscribeMessage.message,
    type: 1,
  },
  pay: {
    id: subscribeMessage.pay,
    type: 0,
  },
  complain: {
    id: subscribeMessage.complain,
    type: 5,
  },
  auth: {
    id: subscribeMessage.auth,
    type: 6,
  },
  recruit: {
    id: subscribeMessage.recruit,
    type: 3,
  },
  resume: {
    id: subscribeMessage.resume,
    type: 4,
  },
  midphone: {
    id: subscribeMessage.midphone,
    type: 8,
  },
}

// 模板消息 可选类型
type TemplateItemType = keyof typeof templateIds

export function message(type: TemplateItemType) {
  return new Promise<void>((resolve) => {
    // 不是微信小程序就退出
    if (!ENV_IS_WEAPP) {
      resolve()
      return
    }
    // 没开启就退出
    if (!subscribeMessage.useSubscribeMessage) {
      resolve()
      return
    }
    // 没登录就退出
    const { userState } = getState().storage
    if (!userState.userId) {
      resolve()
      return
    }
    if (!wx.canIUse('requestSubscribeMessage')) {
      resolve()
      return
    }
    const { id, type: tempType } = templateIds[type] || {}

    // 如果存在小程序没有配置模板，就不弹窗
    if (id) {
      // 当前类型的id-当前类型的 类型 后台存储的
      wx.requestSubscribeMessage({
        tmplIds: [id],
        success(res) {
          if (res.errMsg == 'requestSubscribeMessage:ok') {
            const status = res[id as any]
            if (status == 'accept') {
              wx.$.javafetch['POST/reach/v1/config/wechatMini/addSubscribeMsg']({ type: tempType, wechatToken: config.token })
              resolve()
            }
          }
        },
        fail: (err) => {
          console.log('err', err)
        },
        complete: resolve,
      })
    }
  })
}

/** 判断中间号消息订阅逻辑 */
export function messageMidohoneBool() {
  // 不是微信小程序就退出
  if (!ENV_IS_WEAPP) {
    return { code: 401 }
  }
  // 没登录就退出
  const { userState } = getState().storage
  if (!userState.userId) {
    return { code: 401 }
  }
  if (!wx.canIUse('requestSubscribeMessage')) {
    return { code: 300, msg: 'requestSubscribeMessage' }
  }

  // return { code: 201, msg: 'notTemplate' }
  return { code: 200, msg: 'ok' }
}

/** 推送中间号拨打虚拟号模版信息 */
export function messageMidphone() {
  return new Promise<any>((resolve) => {
    const bool = messageMidohoneBool()
    if (bool.code != 200) {
      resolve(bool)
      return
    }
    const { id, type: tempType } = templateIds.midphone
    // 当前类型的id-当前类型的 类型 后台存储的
    wx.requestSubscribeMessage({
      tmplIds: [id],
      success(res) {
        if (res.errMsg == 'requestSubscribeMessage:ok') {
          const status = res[id as any]
          if (status == 'accept') {
            wx.$.javafetch['POST/reach/v1/config/wechatMini/addSubscribeMsg']({ type: tempType, wechatToken: config.token }, { hideErrCodes: true, hideMsg: true })
              .then(() => {
                resolve({ code: 200, msg: '授权成功' })
              }).catch(() => {
                resolve({ code: 500, msg: '授权失败' })
              })
          } else {
            resolve({ code: 401, msg: '拒绝授权' })
          }
        } else {
          resolve({ code: 401, msg: '授权失败' })
        }
      },
      fail() {
        resolve({ code: 500, msg: '授权失败' })
      },
    })
  })
}

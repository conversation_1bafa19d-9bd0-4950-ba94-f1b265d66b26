<!-- -------- 页面头部 -->
<view class="page-navbar" id="custom-header">
  <header-contact type='contact' />
  <view class="m-switchs-style">
    <m-switchs tabList="{{tabList}}" tabIndex="{{activeTabIdx}}" bind:click="tabIdxChange" />
  </view>
  <block wx:if="{{isHeaderTip&&showHeaderTip}}">
    <header-tip wx:if="{{activeTabIdx==0&&myContactInfo.list.length>0}}" type="myContactHistory" title="已为您展示近{{contactConfig.showDay}}拨打记录，您可以点击关注进行永久管理！" />
    <header-tip wx:elif="{{activeTabIdx==1&&contactMeInfo.list.length>0}}" type="myContactHistory" title="已为您展示近{{contactConfig.showDay}}拨打记录，您可以点击关注进行永久管理！" />
  </block>
</view>
<view wx:if="{{activeTabIdx===0&&myContactInfo.list.length>0||activeTabIdx===1&&contactMeInfo.list.length>0}}" style="height:16rpx;" />
<!-- -------- 评价送积分次数展示部分 -->
<view class="activity-box" wx:if="{{activeTabIdx?contactMeInfo.list.length:myContactInfo.list.length}}">
  <evaluation-activity wx:if="{{activeTabIdx?contactMeInfo.list.length:myContactInfo.list.length}}" bind:closeActivity="closeActivity" id='evaluation-activity' />
</view>
<!-- -------- 我沟通的人 (B/C端共用卡片) -->
<view style="display:{{activeTabIdx===0?'block':'none'}}">
  <block wx:for="{{myContactInfo.list}}" wx:key="index">
    <view class="my-contact" data-index="{{index}}" data-item="{{item}}">
      <v4-contact-card item="{{item}}" sourceId="22" index="{{index}}" id="common-card-{{item.expenseId}}-{{item.infoId}}" cardType="myContacted" nearbyWorkerListApiSource="ContactListOut" bind:onClickContactBtn="onClickContactBtn" bind:onClickRealPhoneBtn="onClickRealPhoneBtn" bind:onClickEvaluateBtn="onClickEvaluateBtn" bind:updateCooperation="updateCooperation" followBtnSwitch />
    </view>
    <!-- 广告每隔四条数据一条 -->
    <block wx:if="{{index && (index + 1) % 4 === 0 && advertUnitId}}">
      <advert custom-class="advert-class" unitId="{{advertUnitId}}" />
    </block>
  </block>
  <yp-load-more show="{{!myContactInfo.isEmpty}}" state="{{myContactInfo.loadMoreState}}" finishText="- 近{{contactConfig.showDay}}暂无更多沟通记录 -" />
  <yp-empty wx:if="{{role == 2}}" img="https://cdn.yupaowang.com/yupao_mini/worker_myContactList_Empty.png" show="{{myContactInfo.isEmpty}}" style="padding-top:176rpx;">
    <view class="empty-tip">
      <view>近7天暂无沟通记录</view>
      <view>快去联系老板吧</view>
    </view>
    <view class="empty-btns">
      <m-button bind:tap="goToRecruitPage" custom-class="bt" borderRadius="8rpx" btnTxt="找工作" />
    </view>
  </yp-empty>
  <yp-empty wx:else show="{{myContactInfo.isEmpty}}" img="https://cdn.yupaowang.com/yupao_mini/worker_myContactList_Empty.png" tip="近7天暂无沟通记录" style="padding-top:176rpx;">
    <block wx:if="{{!isExistRecruit}}">
      <view class="empty-tip">快去发布职位或者主动沟通牛人吧</view>
      <view class="empty-btns">
        <m-button bind:tap="goToResumePage" custom-class="bt" borderRadius="8rpx" btnTxt="查看简历" />
      </view>
    </block>
  </yp-empty>
</view>
<!-- -------- 谁沟通过我 (B/C端共用卡片) -->
<view style="display:{{activeTabIdx===1?'block':'none'}}">
  <block wx:for="{{contactMeInfo.list}}" wx:key="index">
    <view class="contact-me" data-index="{{index}}" data-item="{{item}}">
      <v4-contact-card item="{{item}}" sourceId="23" index="{{index}}" id="common-card-{{item.expenseId}}-{{item.infoId}}" cardType="whoContacted" nearbyWorkerListApiSource="ContactListIn" bind:onClickContactBtn="onClickContactBtn" bind:onClickRealPhoneBtn="onClickRealPhoneBtn" bind:onClickEvaluateBtn="onClickEvaluateBtn" bind:updateCooperation="updateCooperation" followBtnSwitch sourceType="whoContactedMeToResume" />
    </view>
    <!-- 广告每隔四条数据一条 -->
    <block wx:if="{{index && (index + 1) % 4 === 0 && advertUnitId}}">
      <advert custom-class="advert-class" unitId="{{advertUnitId}}" />
    </block>
  </block>
  <yp-load-more show="{{!contactMeInfo.isEmpty}}" state="{{contactMeInfo.loadMoreState}}" finishText="- 近{{contactConfig.showDay}}暂无更多沟通记录" />
  <yp-empty wx:if="{{role == 2}}" show="{{contactMeInfo.isEmpty}}" img="https://cdn.yupaowang.com/yupao_mini/worker_myContactList_Empty.png" style="padding-top:176rpx;">
    <block wx:if="{{!hasResume}}">
      <view class="empty-tip">
        <view>近7天暂无沟通记录</view>
        <view>快去发布简历，让老板主动联系你</view>
      </view>
      <view class="empty-btns">
        <m-button bind:tap="goToPublishResumePage" custom-class="bt" borderRadius="8rpx" btnTxt="发布简历" />
      </view>
    </block>
    <block wx:elif="{{isShowTopping}}">
      <view class="empty-tip">
        <view>近7天暂无沟通记录</view>
        <view>去加急简历，让更多老板看到你</view>
      </view>
      <view class="empty-btns">
        <m-button bind:tap="toTopping" custom-class="bt" borderRadius="8rpx" btnTxt="去加急" />
      </view>
    </block>
    <block wx:else>
      <view class="empty-tip">近7天暂无沟通记录</view>
    </block>
  </yp-empty>
  <yp-empty wx:else show="{{contactMeInfo.isEmpty}}" img="https://cdn.yupaowang.com/yupao_mini/worker_myContactList_Empty.png" tip="近7天暂无沟通记录" style="padding-top:176rpx;">
    <block wx:if="{{!isPublished}}">
      <view class="empty-tip">快去发布职位或者主动沟通牛人吧</view>
      <view class="empty-btns">
        <m-button bind:tap="goToPublishRecruitPage" custom-class="bt" borderRadius="8rpx" btnTxt="发布职位" />
      </view>
    </block>
  </yp-empty>
  <!-- --------（B端下掉，https://yupaowang.feishu.cn/wiki/C9uZwUZSPiyt1Lk57dycCqIVnTh需求下掉此推荐的牛人列表） 可能适合您的牛人👷🏻‍♂️ v4.0.0 新增 https://axure.vrtbbs.com/app/project/dmvyvm/preview/j1jpv3 -->
  <recommend-workers show="{{role == 2 && contactMeInfo.finish}}" />
</view>
<!-- -------- 底部 tab-bar -->
<custom-tab-bar wx:if="{{query.isTabBar}}" id="tabbar" customActive="ucontact" />
<!-- -------- 联系卡片相关功能组合弹窗 -->
<v4-contact-card-popup cardType="{{activeTabIdx==1?'whoContacted':'myContacted'}}" tempCardInfo="{{tempCardItem}}" showMiddleVisible="{{showMiddleVisible}}" isMidGetTel="{{isMidGetTel}}" showCommentBtn query="{{query}}" isShowContactChooseDrawer="{{isShowContactChooseDrawer}}" bind:onFreeCall="onFreeCall" bind:onGetChat="onGetChat" bind:onCloseDrawer="onCloseDrawer" popType="{{popType}}" popContent="{{popContent}}" popNewContent="{{popNewContent}}" popContentData="{{popContentData}}" bind:onCallMidPhone="onCallMidPhone" bind:onBinnedPhone="onBinnedPhone" bind:onReviseMidPhone="onReviseMidPhone" bind:onReviseMidPhoneClose="onReviseMidPhoneClose" bind:onClickRealPhoneBtn="onClickRealPhoneBtn" bind:onOpenRechargePopup="onOpenRechargePopup" isShowDeductionPop="{{isShowDeductionPop}}" deductionContent="{{deductionContent}}" bind:onCallContinue="onCallContinue" bind:onCloseDeductionPopup="onCloseDeductionPopup" ext="{{ext}}" bind:onRefresh="onRefresh" isShowRecharge="{{isShowRecharge}}" textList="{{textList}}" bind:onCloseRechargePopup="onCloseRechargePopup" bind:onClosePagePopup="onClosePagePopup" />
<!-- -------- 真实号码评价弹窗 -->
<!-- <real-phone-evaluation-popup show="{{true}}" /> -->
<!-- 浮标 -->
<buoy-of-page buoyTop="{{buoyTop}}" buoyUnder="{{buoyUnder}}" zIndex="30" />
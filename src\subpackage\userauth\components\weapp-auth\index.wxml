<!-- authType为1为快捷登录 -->
<view class="user-auth-container" wx:if="{{authType===1}}" fixed="{{false}}">
    <!-- 头部 -->
  <custom-header title=" " customBack bind:back="onBack" myClass="phoneLoginHeader" bg-class="header-bg" />
  <view class="login-bg"></view>
  <image class="logo" mode="scaleToFill" src="{{LOGIN_LOGO}}"></image>
  <view class="desc">登录鱼泡直聘</view>
  <view class="login-text">找工作，上鱼泡直聘，电话聊就是快!</view>
  <button wx:if="{{isCheckAgreementOfOneKeyPhoneLogin}}" class="fast-login-btn login-btn" loading="{{kjpageLoading || (sunCodeToWebAuth && silentLoginIng)}}" size="default" data-name="手机号快捷登录" bind:tap="onFastLogin" openType="{{sunCodeToWebAuth && userState.login ? '' : 'getPhoneNumber'}}" bind:getphonenumber="onGetPhoneNumber" disabled="{{kjpageLoading || (sunCodeToWebAuth && silentLoginIng)}}">一键快捷登录</button>
  <button  wx:else class="fast-login-btn login-btn" loading="{{kjpageLoading || (sunCodeToWebAuth && silentLoginIng)}}" data-name="手机号快捷登录" size="default" bind:tap="onFastLoginTip" disabled="{{kjpageLoading || (sunCodeToWebAuth && silentLoginIng)}}">一键快捷登录</button>
  <view class="agreement agreementFast padding">
    <view class="box" bind:tap="onCheckAgreementOfPhoneLogin">
      <image hidden="{{!isCheckAgreementOfOneKeyPhoneLogin}}"  class="check" src="https://cdn.yupaowang.com/yupao_mini/checked.svg" />
      <image hidden="{{isCheckAgreementOfOneKeyPhoneLogin}}"  class="check" src="https://cdn.yupaowang.com/yupao_mini/unecked.svg" />
      <view class="agreement-text">
        <view>
          <text>已阅读并同意</text>
          <text class="color" data-type="user" catch:tap="onToGreement">《鱼泡直聘服务协议》</text>
          <text>和</text>
          <text class="color" data-type="privacy" catch:tap="onToGreement">《隐私政策》</text>
          <text>，允许鱼泡直聘统一管理本人账号信息</text>
        </view>
      </view>
    </view>
  </view>
  <!-- <button class="back-last-page-btn" bind:tap="onBack">返回上一页</button> -->
  <footer-auth-state />
</view>

<!-- authType为2为手机号登录 -->
<view class="user-register-container" wx:elif="{{authType===2}}">
  <!-- 头部 -->
  <custom-header title="注册/登录" customBack bind:back="onNavBack" />
  <!-- 标题 -->
  <view class="register-title">
    <!-- <view class="register-title-icon">
      <icon-font type="yp-icon_pop_smile" size="52rpx" />
    </view> -->
    <view class="register-title-text">手机号登录/注册</view>
  </view>
  <view class="tips">首次验证通过即注册鱼泡直聘账号</view>
  <view class="register-form">
    <!-- 手机号 -->
    <view class="register-form-item">
      <view class="{{active=='phone' ? 'phone-icon' : 'default-icon'}}">
        <icon-font type="yp-icon_signin_dl_phone" size="52rpx" />
      </view>
      <input class="form-item-input" type="number" catch:tap="onClick" data-type="phone" value="{{phone}}" bind:input="onChange" bind:blur="onBlurPhone" placeholder="请输入手机号码" maxlength="11" />
      <view class="close">
        <icon-font wx:if="{{phone && active=='phone'}}" data-type="phone" bind:tap="onClear" type="yp-icon_close" size="52rpx" color="rgba(0, 0, 0, 0.45)" />
      </view>
    </view>
    <!-- 验证码 -->
    <view class="register-form-item">
      <view class="{{active=='code' ? 'phone-icon' : 'default-icon'}}">
        <icon-font type="yp-icon_signin_dl_code" size="52rpx" />
      </view>
      <input class="form-item-input-pwd" type="number" catch:tap="onClick" data-type="code" value="{{code}}" bind:input="onChange" bind:blur="onBlurCode" placeholder="请输入验证码" maxlength="4" />
      <view class="close">
        <icon-font wx:if="{{code && active=='code'}}" data-type="code" bind:tap="onClear" type="yp-icon_close" size="52rpx" color="rgba(0, 0, 0, 0.45)" />
      </view>
      <verification-code
        biz="login"
        showMsg="{{false}}"
        initTime="{{initTime}}"
        bind:onShowTip="onShowErrorTip"
        bind:verifyToken="onVerifyToken"
        class="get-code-btn"
        tel="{{phone}}"
      />
    </view>
    <!-- 错误提示 -->
    <view class="word-tips">{{errorTip}}</view>
    <button class="register-btn" data-name="注册/登录" bind:tap="onLogin" loading="{{loading}}">注册/登录</button>
    <!-- <view class="user-register-toggle">
      <view bind:tap="onToggleLogin" class="register-toggle-text">使用一键快速登录</view>
    </view> -->
    <view class="agreement">
      <view class="box" bind:tap="onCheckAgreement">
        <view class="icon {{isCheckAgreement ? 'color' : ''}}">
          <icon-font type="yp-icon_sign_xieyi_y" size="42rpx" />
        </view>
        <view>
          已阅读并同意<text class="color" data-type="user" catch:tap="onToGreement">《鱼泡直聘服务协议》</text>和<text class="color" data-type="privacy" catch:tap="onToGreement">《隐私政策》</text>，允许鱼泡直聘统一管理本人账号信息
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 温馨提示弹窗(隐私协议) -->
<drawer catch:touchmove="true" visible="{{showPrivacyPopup}}" isMaskClose="{{false}}" zIndex="9999">
  <view class="loginProtocolPopup" >
    <view class="tipTitle">
      <text>温馨提示</text>
      <view catch:tap="onClosePrivacyPopup"><icon-font type="yp-icon_pop_close" size="48rpx" color="#666"  customClass="loginTipCloseIcon" /></view>
    </view>

    <view class="tipDesc">
      <text>请阅读并同意</text><text catch:tap="onToGreement" class="color" data-type="user" >《鱼泡直聘服务协议》</text>和<text catch:tap="onToGreement" class="color" data-type="privacy">《隐私政策》</text>，允许鱼泡直聘统一管理本人账号信息
    </view>

    <view class="footer">
      <button wx:if="{{authType==1}}" class="fast-login-btn login-btn btn-confirm" loading="{{kjpageLoading}}" size="default" bind:tap="onFastLogin" openType="{{sunCodeToWebAuth && userState.login ? '' : 'getPhoneNumber'}}" bind:getphonenumber="onGetPhoneNumber">同意并登录</button>
      <button wx:else class="fast-login-btn login-btn btn-confirm" bind:tap="onLogin" loading="{{loading}}">同意并登录</button>
    </view>
    <view class="emptyView" />
  </view>
</drawer>
<plugin-guard />
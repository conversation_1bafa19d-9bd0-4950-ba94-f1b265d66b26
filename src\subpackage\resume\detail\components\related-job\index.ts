import { DIRECT_CITY_IDS } from '@/utils/helper/location/index'

Component(class RelatedJob extends wx.$.Component<RelatedJob> {
  async onOpenDraft() {
    await wx.$.u.waitAsync(this, this.onOpenDraft, [])
    const { draftId } = this.data
    wx.$.collectEvent.event('job_tip_click', {
      source_id: '2',
      button_name: '打开职位',
    })

    this.triggerEvent('openDraft', { draftId })
  }

  async onModifyJob() {
    await wx.$.u.waitAsync(this, this.onModifyJob, [])
    const { jobId } = this.data
    wx.$.collectEvent.event('job_tip_click', {
      source_id: '2',
      button_name: '修改职位',
    })
    this.triggerEvent('modifyJob', { jobId })
  }

  lifetimes = {
    async ready() {
      const { selectedTab = {} } = this.data.query || {}
      if (!selectedTab || (!selectedTab.jobId && !selectedTab.jobDraftId)) {
        return
      }
      const isDirectCity = DIRECT_CITY_IDS.includes(String(selectedTab.provinceId))
      const cityName = isDirectCity ? selectedTab.provinceName : selectedTab.cityName
      const brief = [cityName, selectedTab.salary, selectedTab.recruitType === 1 ? '全职' : '兼职'].filter(Boolean).join(' · ')
      const newData: any = { ...selectedTab, showCard: true, brief }

      if (selectedTab.jobDraftId) {
        newData.draftId = selectedTab.jobDraftId
        newData.jobType = -1
      }
      if (selectedTab.jobId) {
        newData.jobId = selectedTab.jobId
        newData.jobType = selectedTab.checkStatus
      }
      this.setData(newData)
    },
  }

  properties = {
    visible: {
      type: Boolean,
      value: false,
    },
    query: {
      type: Object,
      value: {},
    },
  }

  setInitData() {
    return {
      /** 待开放 -1 审核失败 0 审核中 1 已通过 2 */
      jobType: 0,
      /** showCard 是否展示卡片 */
      showCard: false,
    }
  }
})

/*
 * @Author: ji<PERSON><PERSON>
 * @LastEditors: 肖嘉俊 <EMAIL>
 */
import { store } from '@/store/index'

/** 添加和重新上传（编辑）面试视频 */
export function addOrEditVideo(resUpload: any) {
  const { resumeUuid, videoResp, userInfoResp } = store.getState().storage.myResumeDetails
  const { uuid } = videoResp || {}
  const { userId } = userInfoResp || {}
  wx.$.collectEvent.event('addOrEditVideo', {
    type: uuid ? '编辑' : '添加',
    userId,
  })
  // console.log('resUpload', resUpload)
  // return {}
  if (uuid) {
    // 重新上传（编辑）
    return wx.$.javafetch['POST/resume/v3/video/modify']({
      uuid,
      resourceId: resUpload.data.resourceId,
      filePath: resUpload.data.url,
      uploadSource: 1,
      resumeUuid,
    })
  }

  return wx.$.javafetch['POST/resume/v3/video/add']({
    resourceId: resUpload.data.resourceId,
    filePath: resUpload.data.url,
    resumeUuid,
    uploadSource: 1,
  })
}

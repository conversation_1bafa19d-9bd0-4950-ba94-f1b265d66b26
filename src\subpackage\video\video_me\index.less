.video-me {
  position: relative;
  background: #000;
  width: 750rpx;
  height: 100vh;
  overflow: hidden;
}

.video-wrap {
  position: relative;
  overflow: hidden;
  width: 100%;
}

.video {
  width: 100%;
  position: absolute;
  bottom: 0;
}

.header {
  position: absolute;
  left: 0;
  z-index: 2;
  width: 100%;
  padding: 0 32rpx;
}

.header-cell {
  display: flex;
  align-items: center;
}

.is-check {
  margin-left: 40rpx;
  width: 382rpx;
  height: 72rpx;
  text-align: center;
  line-height: 72rpx;
  background: #000;
  border-radius: 36rpx;
  opacity: 0.6;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
}

.tips {
  margin-top: 20rpx;
  width: 686rpx;
  padding: 24rpx 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 16rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
  line-height: 44rpx;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.play-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 3;
  transform: translate(-50%, -50%);
  width: 108rpx;
  height: 108rpx;
}

.footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx 32rpx;
}

.footer-left {
  display: flex;
  align-items: center;
}

.footer-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .custom-btn {
    padding-left: 0;
    padding-right: 0;
    margin-left: 16rpx !important;
    font-weight: 400;
  }
}

.footer-action {
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0.7;
  border-radius: 16rpx;
  padding: 6rpx 22rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 16rpx;
}

.safe-area {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
  background-color: transparent;
}

.video-timeline {
  border-radius: 16rpx;
  font-size: 28rpx;
  padding: 14rpx 18rpx;
  margin-right: 16rpx;
  opacity: 0.7;
  line-height: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.download-progress {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 62rpx;
  background: rgba(0, 146, 255, 0.5);
  border-radius: 8rpx;
  padding: 0 24rpx;
  margin-left: 16rpx;
}

.download-progress-bar {
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
  width: 70rpx;
  height: 62rpx;
  background-color: #0092FF;
}

.progress-text {
  position: relative;
  z-index: 2;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
}

.download-modal-wrap {
  width: 464rpx;
  height: 256rpx;
  background: rgba(0, 0, 0, 0.65);
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.95);
}

.hidden-block {
  display: none !important;
}

.progress-wrap {
  position: relative;

  .progress-slider {
    position: absolute;
    top: -14rpx;
    z-index: 10001;
    width: 750rpx;
    margin: 0;
    opacity: 0;
  }

  .progress-bar {
    width: 100%;
    background-color: #4d4d4d;
    height: 8rpx;
  }

  .progress-item {
    width: 0;
    height: 8rpx;
    background: #fff;
    border-radius: 0 6rpx 6rpx 0;
    transition: width 0.25s linear;
  }
}

const https = require('https');
const { formatTime } = require('./utils')
const { spawn } = require('cross-spawn');
const { log } = require('console');

const e = {
  DEV: '测试站',
  PRO: '正式站',
  PRE: '预发布',
  DEVELOP: '开发站',
}

/** 发送飞书通知 */
function sendNotifyFeishu(option) {
  // if(option.development !== 'PRO') {
  //   return
  // }
  const names = option.miniName.map(item => {
    return `<font color=${item.error ? "'red'" : "'black'"}>${item.name}<\/font>`
  })

  const data = JSON.stringify({
      msg_type: 'interactive',
      card: {
        'config': {
          'wide_screen_mode': true,
        },
        'i18n_elements': {
          'zh_cn': [
            {
              'tag': 'markdown',
              'content': `
**小程序打包日志**
当前打包(${names.length}个)：${names.join('、')}
版本：${option.version}
环境：${e[option.development]}
备注：${option.desc}
操作人：${option.userName}
打包分支：${option.branchName}
主包大小：${option.mainSize}

**微信小程序23个包(除开鱼泡网)：** [查看体验码](https://w3nu1yaadv.feishu.cn/wiki/DCTywwQj3iDC5kkMA3KcMweIndg?table=tblZwPYQCmP30hDK&view=vew9S9o7Np)
当前未打包的有${option.miniStatus.length}个
            `,
            },
          ],
        },
      },
    }
  );

  /** 发送机器人通知 */
  postRequest({ path: '/open-apis/bot/v2/hook/af746ce0-1d13-4527-adc6-eabd0a1e865a', data })
}

/** 获取飞书文档文本内容 */
async function getFeishuDocContent(options) {
  const data = JSON.stringify({
    app_id: 'cli_a4e8abd7a53ad00e',
    app_secret: 'i20sps0EiqZB3v6ujsrikgBdrAW0Nnn8'
  })
  try {
    /** 获取应用凭证 */
    const res = await postRequest({ path: '/open-apis/auth/v3/tenant_access_token/internal', data })
    if (res) {
      const token = res.tenant_access_token || ''
      /** 获取知识库文档token */
      const obj_res = await getRequest({ path: 'https://open.feishu.cn/open-apis/wiki/v2/spaces/get_node?token=DCTywwQj3iDC5kkMA3KcMweIndg', token })
      const { obj_token } = obj_res.node
      /** 获取文档内容 */
      const data = await getRequest({ path: `https://open.feishu.cn/open-apis/bitable/v1/apps/${obj_token}/tables/tblZwPYQCmP30hDK/records?page_size=30`, token })
      updateDoc(data.items, options, token, obj_token)
    }
  } catch (error) {
    console.log(error)
  }
}

/** 更新飞书文档内容 */
function updateDoc(docContent, params, token, obj_token) {
  /** 当前分支信息 */
  const branchResult = spawn.sync('git', ['rev-parse', '--abbrev-ref', 'HEAD'])
  /** 分支名称 */
  const branchName = branchResult.status === 0 ? branchResult.stdout.toString().trim() : ''
  /** 当前用户信息 */
  const result = spawn.sync('git', ['config', 'user.name'])
  /** 用户名称 */
  const userName = result.status === 0 ? result.stdout.toString().trim() : ''
  const time = new Date()
  /** 更新或添加的文档数据 */
  const records = []
  /** 剩余小程序状态 */
  const miniStatus = []
  const development = {
    DEVELOP: '开发站',
    DEV: '测试站',
    PRE: '预发布',
    REL: '预发布正式站',
    PRO: '正式站',
  }
  params.miniName.forEach((item) => {
    item.name.indexOf('上传失败') == -1 && records.push({
      fields: {
        name: item.name,
        trialVersion: params.version,
        env: development[params.development],
        updateTime: formatTime(time, 'MM-dd hh:mm'),
        appid: item.appid,
        branchName,
        userName,
      }
    })
  })
  docContent.forEach((item) => {
    const index = records.findIndex(record => {
      return record.fields.name === item.fields.name || record.fields.appid === item.fields.appid
    })
    if (index !== -1) {
      records[index].record_id = item.record_id
    } else {
      miniStatus.push(`${item.fields.name}-${item.fields.env}-${item.fields.trialVersion}`)
    }
  })
  const addRecords = records.filter(item => !item.record_id)
  const updateRecords = records.filter(item => item.record_id)
  /** 更新文档数据 */
  updateRecords.length && postRequest({
    path: `/open-apis/bitable/v1/apps/${obj_token}/tables/tblZwPYQCmP30hDK/records/batch_update`,
    data: JSON.stringify({ records: updateRecords }),
    headers: { 'Authorization': `Bearer ${token}` }
  })
  /** 新增文档数据 */
  addRecords.length && postRequest({
    path: `/open-apis/bitable/v1/apps/${obj_token}/tables/tblZwPYQCmP30hDK/records/batch_create`,
    data: JSON.stringify({ records: addRecords }),
    headers: { 'Authorization': `Bearer ${token}` }
  })
  /** 发送飞书通知 */
  sendNotifyFeishu({...params, branchName, miniStatus, time, userName})
}

/** POST请求 */
function postRequest(options) {
  return new Promise((resolve, reject) => {
    const { path, data, headers} = options;
    const req = https.request({
      hostname: 'open.feishu.cn',
      port: 443,
      path,
      method: 'POST',
      headers: {
        'Content-Type': '"application/json; charset=utf-8"',
        ...headers
      }
    }, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        resolve(JSON.parse(responseData))
      });
    });
    req.on('error', (error) => {
      reject(`${options.path}：${error.message}`)
    });
    req.write(data);
    req.end();
  })
}

/** GET请求 */
function getRequest(options) {
  return new Promise((resolve, reject) => {
    const req = https.request(options.path,
      { method: 'GET', headers: { 'Authorization': `Bearer ${options.token}` } }, (res) => {
        let responseData = '';
        res.on('data', (chunk) => {
          responseData += chunk;
        });
        res.on('end', () => {
          const { data, code } = JSON.parse(responseData)
          if(code == '0') {
            resolve(data)
          }
        });
      });
      req.on('error', (error) => {
        reject(`${options.path}：${error.message}`)
      });
      req.end();
  })
}

module.exports = {
  getFeishuDocContent
}

/** 测试数据 */
const testData = {
  miniType: 'weapp',
  sub: 'yupao',
  mode: 'build',
  development: 'DEV',
  desc: '7.8.0-测试站',
  miniName: [
    {
      name: '鱼泡直聘招聘求职找工作(鱼泡网)',
      appid: 'wx31a1c86a67bc6c54',
      error: true
    }
  ],
  version: '7.8.0',
  mainSize: '1379KB',
  totalSize: '7446KB',
  branchName: 'develop-v7.8.0',
  miniStatus: [
    '装修招工-正式站-7.8.0',
    '工地招工-正式站-7.8.0',
    '急招水电工-正式站-7.8.0',
    '急招铲车司机-测试站-7.8.0',
    '招建筑木工-正式站-7.8.0',
    '工地急招-正式站-7.8.0',
    '鱼泡焊工-正式站-7.8.0',
    '急招焊工-正式站-7.8.0',
    '鱼泡招工(工地活)-正式站-7.8.0',
    '鱼泡简历（招工程八大员）-正式站-7.8.0',
    '鱼泡网 | 兼职招聘找工作（工程综合承包）-正式站-7.8.0',
    '找机械司机-正式站-7.8.0',
    '鱼泡工厂招工（招瓦工）          -正式站-7.8.0',
    '招挖机驾驶员-正式站-7.8.0',
    '鱼泡快招(找建筑工人)-正式站-7.8.0',
    '鱼泡建筑工人招聘(招建筑工人)-测试站-7.8.0',
    '鱼泡货运(招司机｜搬运工)-正式站-7.8.0',
    '鱼泡直聘(找架子工)-正式站-7.8.0',
    '鱼泡网招聘求职(急招塔吊司机)-正式站-7.8.0',
    '鱼泡装修师傅招聘-正式站-7.8.0',
    '招机械司机-测试站-7.9.0',
    '鱼泡网丨求职招聘找工作(找钢筋工)-正式站-7.8.0',
    '鱼泡厨师服务员招聘（找油漆工）-正式站-7.8.0',
    'undefined-预发布-undefined',
    'undefined-测试站-undefined'
  ],
  time: '2024-11-15T02:44:21.346Z',
  userName: 'jianglong'
}

// getFeishuDocContent(testData)

/* eslint-disable max-len */
import { MapStateToData, actions, connectPage, dispatch, store } from '@/store/index'

import {
  addJobTopInfoApi,
  getJobTopInfoApi,
  getJobTopPriceInfoApi,
  getOtherCanTopJobListApi,
  getTopConfig,
  handleTopJobFail,
  modJobTopInfoApi,
} from './api-server-new'

import { BPOfClickJobTopConfirmBtn, BPOfTopPageExposure } from './api-burying-point'
import { disabledCities, topRules, ypSafeNumberBeginList } from './config'
import { getDefaultTopTypeCode, getJobTopEndTimeStr, initPageDataService, initRenewPageDataService } from './tool'
import { IJobTopPriceInfo, ITopJobConfigRes } from './types'
import { publishType } from './utils'
import { toLogin } from '@/utils/helper/common/toLogin'

const mapStateToData: MapStateToData = (state) => {
  const { user } = state
  return {
    to_auth: user.userInfo.to_auth,
  }
}

/**
 * @name 招工置顶页
 */
Page(
  connectPage(mapStateToData)({
    data: {
      // 禁用选择的城市
      disabledCities,
      // 置顶规则(端上写死)
      topRules,

      // 路由参数存储及说明
      query: {
        defaultTopArea: '', // 单个地区信息，如：1
        classify_id: '', // 工种id，如：1，2，3
        type: '', // 类型，用于判断是发布成功后的置顶还是正常置顶，可选值：'psuc': 发布招工成功后, 'psucFactory'：发布工厂招工后, factoyRecommend：工厂专属人才推荐进入，''：正常进入
        subscribe: '',
        job_id: '', // 招工id
        origin: '', // 来源
      },

      // 是否为发布招工页面跳转过来的
      isFromPublishSuccess: false,
      // 鱼泡安全号列表
      virtualNumberList: ypSafeNumberBeginList,
      // 显示返回弹窗
      showBackTip: false,

      // 置顶配置
      topConfig: {},
      // 置顶的城市列表
      topCityListOld: [],
      // 置顶的城市列表新
      topCityListNew: [],
      // 置顶时间配置
      topTimeList: [],
      // 当前选中(默认)置顶时间配置
      topTimeActive: {},
      // 置顶时间选择项数据
      dayPickerList: [],
      // 置顶时间选择项数据-当前选中数据
      dayPickerActive: {},
      // 当前选中(默认)置顶时间配置 下标
      dayPickerIdx: 0,

      // 当前招工置顶信息
      jobTopInfo: {},
      // 置顶续期价格信息
      jobTopPriceInfo: {},
      // 去置顶信息
      jobTopMealInfo: {} as ITopJobConfigRes,
      // 当前选择的置顶套餐I
      activeTopMeal: '',
      // 置顶到期日期
      jobTopEndTimeStr: '',
      // 置顶状态 -1-未置顶 0-预约置顶 1-置顶中 2-暂停置顶中 3-置顶已结束
      jobTopStatus: 0,
      // 是否置顶中
      isTopping: true,
      // 是否延长时间状态
      isOvertime: false,

      /** 展示充值弹窗 */
      showRechargePopup: false,
      /** 充值弹窗内展示的积分 */
      rechargeNum: 0,
      // 是否充值
      isRecharge: false,

      /** 选择招工信息列表显示隐藏 */
      showRecommend: false,
      /** 选择招工信息列表数据 */
      recommendList: [],
      /** 查看置顶详情显示隐藏 */
      showTopRulePopup: false,
      // 页面加载状态
      pageLoading: true,
      /** 是否有置顶推荐 */
      noJob: false,
    },
    /** 是否弹出了企业认证弹框 */
    isCompanyModal: false,

    async onLoad(query) {
      // 初始化路径参数
      const isFromPublishSuccess = publishType.includes(query.type)
      this.setData({ query, isFromPublishSuccess })

      try {
        wx.$.loading('加载中')
        await this.initJobTopPageData()
      } catch (error) {
        console.error('err +>', error)
      } finally {
        wx.hideLoading()
        this.setData({ pageLoading: false })
      }

      BPOfTopPageExposure.call(this)
    },
    /** 跳转到发布招工 */
    onPublish() {
      wx.$.nav.push('/subpackage/recruit/fast_issue/index/index')
    },
    // 初始化页面置顶数据信息(用于复用刷新页面操作)
    async initJobTopPageData() {
      const { query, isOvertime } = this.data
      let jobId = String(query.job_id || '').trim()

      if (isOvertime) {
        this.setData({ isOvertime: false })
      }

      // 选择其它岗位列表
      try {
        const jobIdObj: any = jobId ? { jobId } : {}
        const recommendList = await getOtherCanTopJobListApi(jobIdObj)
        // 是否置顶弹出 选择其它岗位列表 底部弹出层
        const isRecommend = query.is_recommend
        // 没有置顶职位可选
        if (isRecommend && !jobId && recommendList.length === 0) {
          this.setData({ noJob: true })
          return
        }

        // 弹出 选择其它岗位信息列表 底部弹出框
        if (isRecommend && !jobId && recommendList.length > 1) {
          this.setData({ recommendList, ...(isRecommend ? { showRecommend: true } : {}) })
          return
        }

        // 展示当前需要置顶的招工信息 并设置更新 query.job_id jobId 方便后续接口获取数据及处理对应情况
        if (isRecommend && !jobId && recommendList.length === 1) {
          const job_id = recommendList[0].jobId
          query.job_id = job_id
          jobId = job_id
          this.setData({ recommendList: [], query: { ...query, job_id } })
        } else {
          // 否则 不满足上述情况
          this.setData({ recommendList })
        }
      } catch (error) {
        console.error('选择其他职位 error =>', error)
      }

      if (!jobId) {
        return
      }

      let jobTopInfo = null
      try {
        // 获取当前招工置顶信息
        jobTopInfo = await getJobTopInfoApi(jobId)
      } catch (res) {
        handleTopJobFail.call(this, res)
        return
      }
      // 置顶状态 -1-未置顶 0-预约置顶 1-置顶中 2-暂停置顶中 3-置顶已结束
      const jobTopStatus = jobTopInfo.jobTopStatus?.code
      const isTopping = jobTopStatus == 1 || jobTopStatus == 2
      if (isTopping) {
        const tmpPageData = await initRenewPageDataService.call(this, jobTopInfo, query)
        if (!tmpPageData) {
          return
        }
        this.setData(tmpPageData)
      } else {
        const tmpPageData = await initPageDataService.call(this, jobTopInfo, query)
        if (!tmpPageData) {
          return
        }

        this.setData(tmpPageData)
      }
    },
    // -------- 导航栏逻辑函数
    async onBack() {
      const { jobTopStatus, isTopping } = this.data

      if (!isTopping) {
        this.setData({ showBackTip: true })
        return
      }
      wx.$.r.back()
      // try {
      //   const res = await wx.$.javafetch['POST/cms/popup/v1/showConditionCheck']({ popCode: 'ZDWL' })
      //   const showBackTip = res?.data?.result
      //   this.setData({ showBackTip })
      // } catch (error) {
      //   console.error(error)
      // }
    },
    onUnload() {
      dispatch(actions.otherActions.setState({ recruitBackSource: false }))
    },
    // -------- 置顶--选择城市部分逻辑
    // 选择其他置顶信息--针对首页的刷新招工弹窗来源
    async onOpenOtherToTopInfo() {
      const { job_id } = this.data.query
      // 埋点上报
      wx.$.collectEvent.event('miniPageClick', { page_name: '招工置顶', click_button: '选择其他信息' })
      const recommendList = await getOtherCanTopJobListApi({ jobId: job_id })
      if (recommendList.length <= 0) {
        wx.$.msg('无其他可置顶信息')
        return
      }

      this.setData({ recommendList, showRecommend: true, isHandleChoose: true })
    },
    onClickCityItem(e) {
      const { isTopping } = this.data
      if (isTopping) {
        wx.$.msg('置顶生效中不支持修改地区')
        return
      }

      const { index } = e.currentTarget.dataset
      const topCityListNew = Array.from(this.data.topCityListNew)
      topCityListNew.splice(index, 1)

      this.setData({ topCityListNew })
      // 重新计算价格
      isTopping ? this.getJobTopPriceInfo() : this.getJobTopMealInfo()
    },
    onClickToppingCityTip() {
      wx.$.msg('置顶生效中不支持修改地区')
    },
    onAddMoreTopCity() {
      const { topConfig, disabledCities, topCityListNew: selectCities, isTopping, jobTopMealInfo } = this.data
      const { maxCityNum: maxCity = 3, maxProvinceNum: maxProvince = 2 } = isTopping ? topConfig : jobTopMealInfo
      const maxNumberTips = `最多可同时置顶${maxCity || 3}个市、${maxProvince || 2}个省或直辖市`

      /** 选择城市页面需要的Model参数 */
      const config = { origin: 'recruit', maxNumberTips, maxProvince, maxCity, disabledCities, selectCities }
      dispatch(actions.topCityActions.setState(config))
      /** 跳转城市选择页面 */
      wx.$.r.push({ path: '/subpackage/topset/topcity/index' })
    },
    // -------- 置顶--置顶规则部分逻辑
    onCallPhone() {
      wx.$.u.callCustomerService()
    },
    // -------- 置顶--选择置顶时间部分逻辑
    // 更新置顶时间
    onTopTimeChange(e) {
      const { value } = e.detail
      const { topTimeList, dayPickerList, dayPickerIdx, jobTopInfo, isTopping, isOvertime } = this.data
      if (+value == dayPickerIdx && !isTopping) {
        return
      }

      const dayPickerActive = dayPickerList[+value]
      const topTimeActive = topTimeList[+value]

      // 更新到期时间
      const jobTopEndTimeStr = getJobTopEndTimeStr(jobTopInfo, dayPickerActive)
      const isOvertimeObj = isTopping && !isOvertime ? { isOvertime: true } : {}
      this.setData({ dayPickerIdx: +value, dayPickerActive, topTimeActive, jobTopEndTimeStr, ...isOvertimeObj })

      // 重新计算价格
      this.getJobTopPriceInfo()
    },
    onCancelOvertime() {
      this.setData({ isOvertime: false })
    },
    async getJobTopPriceInfo() {
      try {
        const { query, jobTopInfo, dayPickerActive, topCityListNew, isTopping, activeTopMeal } = this.data
        const jobId = query.job_id
        const topType = getDefaultTopTypeCode(jobTopInfo)
        const topDays = isTopping ? dayPickerActive.value : activeTopMeal.days

        const provinces = []
        const cities = []
        topCityListNew.forEach((it) => (it.pid == 1 ? provinces.push(it.id) : cities.push(it.id)))
        if (topCityListNew.length <= 0) {
          return
        }

        const jobTopPriceInfo = await getJobTopPriceInfoApi({ jobId, topType, topDays, provinces, cities })

        jobTopPriceInfo && this.setData({ jobTopPriceInfo })
        return jobTopPriceInfo as IJobTopPriceInfo
      } catch (error) {
        handleTopJobFail.call(this, error, true)
        return null
      }
    },
    async getJobTopMealInfo() {
      const { query, topCityListNew, activeTopMeal } = this.data

      const provinces = []
      const cities = []
      topCityListNew.forEach((it) => (it.pid == 1 ? provinces.push(it.id) : cities.push(it.id)))
      if (topCityListNew.length <= 0) {
        return
      }
      const jobTopMealInfo: any = await getTopConfig({ bizType: 0, targetId: String(query.job_id), provinces, cities })
      const newActiveTopMeal = (() => {
        if (activeTopMeal?.days) {
          return jobTopMealInfo.configList.find((item) => item.days == activeTopMeal.days) || jobTopMealInfo.configList.find((item) => item.defaultShow)
        }
        return jobTopMealInfo.configList.find((item) => item.defaultShow) || jobTopMealInfo.configList[0]
      })()

      this.setData({
        jobTopMealInfo,
        activeTopMeal: newActiveTopMeal,
      })
    },
    // 确定置顶
    async onTopping() {
      if (this.data.pageLoading) {
        return
      }

      this.setData({ pageLoading: true })

      try {
        const loginStatus = store.getState().storage.userState.login
        if (!loginStatus) {
          BPOfClickJobTopConfirmBtn.call(this, '')
          await wx.$.confirm({ title: '您的登录已过期，请重新登录', cancelText: '取消', confirmText: '去登录' })
          toLogin(true)
          this.setData({ pageLoading: false })
          return
        }

        const { topCityListNew, isOvertime, isTopping, jobTopPriceInfo, activeTopMeal } = this.data
        if (!Array.isArray(topCityListNew) || topCityListNew.length <= 0) {
          wx.$.msg('请选择您的置顶城市')
          this.setData({ pageLoading: false })
          BPOfClickJobTopConfirmBtn.call(this, '')
          return
        }

        // 置顶中 且 非延长 点击确定后
        if (isTopping && !isOvertime) {
          BPOfClickJobTopConfirmBtn.call(this, '')
          handleTopJobFail.call(this, { data: { dialogData: { dialogIdentify: 'ZDCGYD' } } })
          return
        }

        const tmpJobTopPriceInfo = (await this.getJobTopPriceInfo()) as IJobTopPriceInfo
        if (!tmpJobTopPriceInfo) {
          BPOfClickJobTopConfirmBtn.call(this, '')
          this.setData({ pageLoading: false })
          return
        }

        // 置顶价格异常 端上判断
        const totalConsume = isTopping ? jobTopPriceInfo.totalConsume : activeTopMeal.discountPrice
        if (tmpJobTopPriceInfo.totalConsume != totalConsume) {
          BPOfClickJobTopConfirmBtn.call(this, '')
          handleTopJobFail.call(this, { data: { dialogData: { dialogIdentify: 'ZDZGDJ' } } })
          return
        }

        wx.showLoading({ title: '正在置顶...', mask: true })
        // 编辑置顶服务
        if (isTopping) {
          await modJobTopInfoApi.call(this)
          return
        }

        //  添加置顶服务
        await addJobTopInfoApi.call(this)
      } catch (error) {
        console.error(error)
        this.setData({ pageLoading: false })
      }
    },
    // -------- 充值相关逻辑
    // 充值弹窗成功充值反馈
    onRechargeSuccess() {
      this.setData({ showRechargePopup: false, isRecharge: true })
      this.onTopping()
    },
    // 充值弹窗默认关闭事件
    onRechargeClose() {
      this.setData({ showRechargePopup: false, isRecharge: false })
    },
    // 更新当前页面的招工置顶信息
    async onUpdatePageJobTopInfo(e) {
      const job_id = e.detail.item.jobId
      const { query } = this.data
      this.setData({ showRecommend: false })
      this.onLoad({ ...query, job_id })
    },
    /** 关闭选择其他信息的弹窗 */
    onCloseTopRecommendJobModal() {
      this.setData({ showRecommend: false })
    },
    // 设置充值弹窗显示与否
    setRechargePopup(showRechargePopup, num) {
      this.setData({ showRechargePopup, rechargeNum: num })
    },
    // 城市选择页专用方法
    saveCitys(citys: []) {
      const { isTopping } = this.data
      // 更新置顶城市信息
      const topCityListNew = citys
      this.setData({ topCityListNew })

      // 重新计算价格
      isTopping ? this.getJobTopPriceInfo() : this.getJobTopMealInfo()
    },
    // 去置顶时间套餐选择
    onTopMealChange(e) {
      const { meal } = e.currentTarget.dataset
      this.setData({ activeTopMeal: meal })
    },
    onshowTopRuleClick() {
      this.setData({ showTopRulePopup: true })
    },
    onTopRulePopupClose() {
      this.setData({ showTopRulePopup: false })
    },
  }),
)

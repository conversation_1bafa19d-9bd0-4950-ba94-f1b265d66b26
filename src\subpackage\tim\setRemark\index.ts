/**
 * @description: 编辑单行文本框
 * @query: {
 *    type: ''
 *  }
 */

import { getHeaderHeight } from '@/utils/tools/common/index'
import { getContent } from './utils'

Page(class extends wx.$.Page {
  data = {
    title: ' ',
    maxContent: 20,
    /** 输入框的值 */
    content: '',
    /** 旧的输入框的值 */
    contentOld: '',
    /** 顶部高度 */
    top: getHeaderHeight('120rpx', true),
    /** 是否获取了焦点 */
    isFocus: false,
    /** 键盘高度 */
    bottomHeight: 0,
    placeholder: '请输入',
  }

  onLoad(options) {
    const { title, placeholder, max } = options || {}
    const content = getContent()
    this.setData({
      placeholder,
      title,
      content,
      contentOld: content,
      maxContent: max || 20,
      bottomHeight: 0,
    })
  }

  onReady() {
    this.setData({ isFocus: true })
  }

  onHide() {
    this.onHideKey()
  }

  /** 点击提交按钮 */
  onSubmit() {
    const content = `${this.data.content}`.trim()
    if (!this.saveBool()) {
      return
    }
    this.onHideKey()
    wx.$.nav.event({ content })
    wx.$.nav.back()
  }

  /** 判断输入的内容是否有效 */
  saveBool() {
    const { content, maxContent, contentOld } = this.data
    if (content === contentOld) {
      if (!content) {
        wx.$.msg('您还没有输入')
        return false
      }
      wx.$.r.back()
      return false
    }
    if (content.length > maxContent) {
      wx.$.msg('已超出最大字数限制')
      return false
    }
    return true
  }

  /** 点击返回按钮逻辑 */
  onNavBack() {
    this.onHideKey()
    const { content, contentOld } = this.data
    if (content === contentOld) {
      wx.$.r.back()
      return
    }
    if (!this.data.content || `${this.data.content}`.trim() == '') {
      wx.$.r.back()
      return
    }
    wx.$.confirm({
      content: '内容尚未保存,确定退出?',
    }).then(() => {
      wx.$.r.back()
    }).catch(() => {})
  }

  onFocus(e) {
    this.setData({
      isFocus: true,
      bottomHeight: e.detail.height || 0,
    })
  }

  /** 收起键盘 */
  onHideKey() {
    this.setData({ isFocus: false, bottomHeight: 0 })
  }

  /** 输入框input事件 */
  onInput(e) {
    this.setData({
      content: e.detail.value,
    })
  }

  onClear() {
    this.setData({ content: '' })
  }

  /** 禁止滚动操作 */
  onCatch() { }
})

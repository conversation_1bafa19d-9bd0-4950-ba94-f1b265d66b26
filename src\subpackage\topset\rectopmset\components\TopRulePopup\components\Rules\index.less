.container {
  width: 100%;
  padding: 24rpx 32rpx;
  background-color: #f5f6fa;

  .card {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 16rpx;
    padding: 24rpx;
    background-color: #fff;
    border-radius: 16rpx;

    .cardTitle {
      padding-bottom: 8rpx;
      font-weight: 700;
      font-size: 34rpx;
    }

    .cardText {
      color: @text-secondary-color;
      font-weight: 400;
      font-size: 30rpx;
    }
  }

  .bottomCard {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 32rpx 24rpx;
    background-color: #fff;
    border-radius: 16rpx;

    .img {
      width: 96rpx;
      height: 96rpx;
      border-radius: 50%;
    }

    .subtitle {
      padding: 24rpx 0;
      font-weight: 700;
      font-size: 30rpx;
    }

    .button {
      display: flex;
      align-items: center;
      height: 72rpx;
      padding: 0 32rpx;
      font-weight: 700;
      font-size: 30rpx;
      border-radius: 152rpx;
      color: #fff;
      background-color: @primary-color;
    }
  }
}

page {
  background-color: #fff;
}

.hidden {
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
}

.body {
  padding: 32rpx;
  padding-bottom: 0;
  position: relative;
}

.title {
  font-size: 54rpx;
  font-weight: bold;
  line-height: 76rpx;
}

.desc {
  padding-top: 12rpx;
  font-size: 26rpx;
  line-height: 36rpx;
  color: rgba(0, 0, 0, 0.65);
}

.input-box {
  position: relative;
  display: flex;
  align-items: center;
  .bottom-line();
}

.input-right {
  display: flex;
  align-items: center;
  .input-clear{
    opacity: 0.7;
    padding: 6rpx 24rpx;
    display: inline-flex;
  }
  .info-num {
    display: flex;
    align-items: center;
  }
  .num {
    color: @primary-color;
  }
  .num-err {
    color: @error-color;
  }
  .num-gray {
    color: rgba(0, 0, 0, 0.45);
  }
}

.input {
  font-size: 30rpx;
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  display: block;
  height: 112rpx;
  display: flex;
  align-items: center;
  flex: 1;
}

.placeholder {
  word-break: break-word;
  white-space: pre-wrap;
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.45);
}

.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx 32rpx;
  background-color: #FFF;
  .safe-area(24rpx);
  border-top: 1rpx solid rgba(233, 237, 243, 1);
}

.disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  pointer-events: none !important;
}

.btn {
  color: #FFF;
  font-size: 34rpx;
  height: 80rpx;
  line-height: 80rpx;
  display: flex;
  font-weight: bold;
  text-align: center;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  background: #0092ff;

  &.btn-mini{
    height: 72rpx;
    line-height: 72rpx;
    display: inline-flex;
    align-items: center;
    padding: 0 30rpx;
    margin-left: 24rpx;
  }
}

.custom-btn{
  background: rgba(153, 211, 255, 1) !important;
}
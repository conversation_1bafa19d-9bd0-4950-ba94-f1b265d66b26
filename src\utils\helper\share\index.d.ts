/*
 * @Date: 2022-02-25 11:24:57
 * @Description: 分享类型
 */
/**
 * @description 获取小程序分享参数
 * @description 为了可以在任何一个页面分享时截的当前页面的图，并且不带描述文字，取消掉了desc和imageUrl这两个参数
 */
export interface MiniShareInfoResult {
  /** 小程序分享标题 */
  title: string
  /** 小程序分享描述 */
  desc?: string
  /** 小程序分享页面路径 */
  path: string
  /** 小程序分享图片 */
  imageUrl?: string
  /** 分享图是否走json */
  resetImg?: boolean
  /** 分享图片用当前页面 */
  isCurPageImg?: boolean
  /** 是否自定义的 canvas 画布分享图 */
  canvasShareImg?: string
}

// 调用获取小程序分享方法默认参数
export interface MiniShareInfoParams extends MiniShareInfoResult {
  /** 小程序分享参数 */
  params?: MiniShareInfoPathParams
  /** 小程序分享标题 */
  title?: string
  /** 小程序分享页面路径 */
  path?: string
  /** from分享触发类型(必传---option.from) */
  from: string
  /** 是否需要track_seed */
  needTrackSeed?: boolean
}

// 调用获取小程序分享方法默认参数 - 携带分享页面信息的参数
export interface MiniShareInfoPathParams {
  /** 其他分享参数 */
  [key: string]: any
}

// 朋友圈分享
export interface MiniShareTimeLine {
  /** 朋友圈分享的标题 */
  title: string
  /** 朋友圈分享参数 */
  query: string
  /** 朋友圈分享的图片 */
  imageUrl: string
  /** 分享图是否走json */
  resetImg?: boolean
  /** 朋友圈自定义图片 */
  photoUrl?: string
  /** promise */
  promise?: promise
  /** path */
  path?: string
}

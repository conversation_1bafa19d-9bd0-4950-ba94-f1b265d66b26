<view wx:if="{{loadedComp}}" class="backTip">
  <view class="maskBg {{(startOpacity) ? 'open' : ''}}" />

  <zoom opened="{{show}}" catch:exited="unloadComp" catch:entering="setOpacityBg">
    <view class="box">
      <view class="bg" />
      <view class="title">真的不再考虑下了吗?</view>
      
      <view class="subTitle">
        <text>置顶后招聘效率提升</text>
        <text class="primaryColor">{{topTimeActive.exposureNum||3}}倍</text>
      </view>

      <view class="randomTopInfoRow">
        <view class="topUserNums">
          <text>今日已有</text>
          <text class="topUserNum">{{topJobUserNum}}</text>
          <text>位用户完成置顶</text>
        </view>
        <view class="topUserImgs">
          <view wx:for="{{randomAvatarNumArr}}" class="avatarBox {{index!=0?'avatarBox1':''}}"><image src="https://cdn.yupaowang.com/yp_mini/images/zddq/avatar/a{{item}}.png" class="avatar" /></view>
        </view>
        <image src="https://cdn.yupaowang.com/yp_mini/images/zddq/top_recruit.png" class="topImg" />
      </view>

      <view class="btns">
        <view bind:tap="handleCancel" class="cancel">放弃置顶</view>
        <m-button bind:tap="handleConfirm" width="542rpx" height="80rpx" style="font-size: 30rpx;" borderRadius="12rpx">立即置顶</m-button>
      </view>
    </view>
  </zoom>
</view>
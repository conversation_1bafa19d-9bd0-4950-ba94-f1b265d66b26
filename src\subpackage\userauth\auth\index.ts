/*
 * @Date: 2021-12-29 19:04:35
 * @Description: 用户登录
 * @path-query: { auth_type: 微信登录认证类型 默认为1为快捷登录，2为手机号登录 }
 */

import { MapStateToData, connectPage } from '@/store/index'
import { clearLoginState } from '@/utils/helper/common/toLogin'

const mapStateToData: MapStateToData = (state) => {
  const { storage } = state

  return {
    loginInfo: storage.userState,
  }
}

Page(connectPage(mapStateToData)({
  data: {
    sunCodeToWebAuth: '', // web登录授权页所使用的sunCode
  },
  onShow() {
    clearLoginState()
  },
  onLoad(options) {
    const { fromPage, sunCode } = options || {}
    if (sunCode) {
      this.setData({ sunCodeToWebAuth: sunCode })
      return
    }

    if (this.data.loginInfo?.login && fromPage !== 'changeAccount') {
      wx.$.r.back()
    }
  },
  onUnload() {
    clearLoginState()
  },
}))

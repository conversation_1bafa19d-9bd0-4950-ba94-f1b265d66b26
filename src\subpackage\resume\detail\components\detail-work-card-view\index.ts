/*
 * @Date: 2022-01-08 15:49:39
 * @Description: 我的找活名片项目和工作的卡片样式
 */

Component({
  properties: {
    // 数据
    item: {
      type: Object,
      value: {},
    },
    isHiddenMode: {
      type: Boolean,
      value: false,
    },

  },

  methods: {
    // 预览图片
    onPreviewImage({ target: { dataset: { item } } }) {
      const images = this.data.item.attaches.map((i) => i.filePath)
      const index = images.findIndex((i) => i == item.filePath)
      wx.previewImage({
        urls: images,
        current: images[index],
      })
    },
    // 预览
    onPreviewMedia(e) {
      const { currentTarget: { dataset: { item, index } } } = e
      this.triggerEvent('priviewmedia', { item })
      /** 百度不支持previewMedia，只能预览图片 */
      if (!ENV_IS_WEAPP) {
        if (item.type == 1) {
          wx.$.r.push({
            path: '/subpackage/video/video_others/index',
            params: {
              pageSource: 'normal',
              item: {
                videoUrl: item.filePath,
                videoCoverImg: item.cover,
              },
            },
          })
        } else {
          this.onPreviewImage(e)
        }
        return
      }
      const files:Array<any> = this.data.item.attaches.map(item => ({
        url: item.filePath,
        type: item.fileType == 1 ? 'video' : 'image',
        /* poster: item.cover || '', */
      }))

      wx.previewMedia({
        sources: files,
        current: index,
      })
    },
  },
})

/*
 * @Date: 2022-04-14 18:54:28
 * @Description: 接口加密配置
 */

import { request } from '../request/request_java'

export const getHomeConfig = (() => {
  const promise = { current: null }
  const config = {
    nonceKey: '',
    diffTime: 0,
    done: false, // 是否从后端接口获取完成
  }
  return () => {
    if (!promise.current) {
      // 获取后端时间
      const fetchServerMicroTime = request<any>({
        type: 'POST',
        url: '/eventreport/v1/base/serverTime',
        data: {},
        extra: { hideErrCodes: false },
      })
      promise.current = new Promise(async (resolve) => {
        const [time] = await Promise.all([
          fetchServerMicroTime,
        ])
        config.diffTime = time.data.serverTime - Date.now()
        config.done = true
        resolve(null)
      })
    }
    return config
  }
})()

import { SERVER_PHONE } from '@/config/app'
import { actions, dispatch } from '@/store/index'

import { dealDialogApi } from '@/utils/helper/dialog/index'
import { loginWhite } from './utils'
import { toLogin } from '../helper/common/toLogin'
import { getLoginAb } from '../helper/login/index'

export const java_errcode = [
  400, // 参数错误
  401, // 请求未授权
  403, // 拒绝请求
  404, // 未找到资源
  429, // 请求限流
  430, // 签名错误
  500, // 服务器内部错误
  502, // 网关错误
  503, // 服务不可用
  504, // 请求超时
  509, // 超时执行
  601, // 要求用户登录
  701, // 强制升级
]

/** 内部处理的code码, 拦截器主动弹出的弹框 */
export const commonErrCodes = ['10001']

/** 内部处理的弹框标识, 拦截器主动弹出 */
export const commonErrDialogs = [
  /** 用户用户状态是拉黑的情况，这个跳转到登录页 */
  'blacklistOperate',
  /** 积分充值风控 */
  'point_recharge_riskmanagement',
]

/** 系统跳转升级页面开关 */
let systemUpgradeFlag = true

// 拆分url中路径
export const getUrlPath = (url: string) => {
  let path = url.split('?')[0]
  let domain = ''
  if (path.indexOf('http') != -1) {
    // eslint-disable-next-line prefer-destructuring
    path = path.split('//')[1]
    // eslint-disable-next-line prefer-destructuring
    domain = path.split('/')[0]
  }
  return path.replace(domain, '')
}

/** 跳转到登录页 */
function handleToLogin() {
  wx.hideLoading()
  wx.$.l.timLogout()
  dispatch(actions.storageActions.removeItem('userState'))
  dispatch(actions.storageActions.removeItem('myResumeDetails'))
  const pages = getCurrentPages()
  if (pages[pages.length - 1].route !== 'subpackage/userauth/auth/index') {
    wx.$.r.reLaunch({ path: '/subpackage/userauth/auth/index' })
  }
}

// 默认的额外参数
const extraDef = {
  hideMsg: false,
}
/** 处理请求接口返回异常兼容处理 */
export const handleErrorCode: (res, url: string, $extra?: Record<string, any>) => Promise<boolean> = async (res, url: string, $extra) => {
  const extra: any = { ...extraDef }
  if ($extra && typeof $extra === 'object') {
    // 合并对象并改变 extra 的引用
    Object.assign(extra, $extra)
  }
  if (res) {
    let errcode = res.code
    //! 注意：code=0 时，data必然是一个封装对象(请求成功)；code !=0 时 data 为null
    if (!errcode) {
      errcode = res.data ? res.data.error_code : res.error_code
    }
    const msg = res?.message || ''
    // 去系统升级中页面
    if (errcode === 701) {
      if (!systemUpgradeFlag) {
        return false
      }
      systemUpgradeFlag = false
      const sysData = res?.data?.system_pause_data
      if (sysData?.type == 'url') {
        wx.$.r.reLaunch({
          path: '/subpackage/update-prompt/index',
          query: {
            url: sysData?.url,
            arrow: sysData?.allow_return,
          },
        })
      } else if (sysData?.type == 'html') {
        wx.$.r.reLaunch({ path: '/subpackage/update-prompt/index' })
      }
      return false
    }
    // 拦截登录过期，model接口显示过期状态下跳转到登录页
    if (errcode === 601 || errcode === 401) {
      wx.$.l.timLogout()
      const urlPath = getUrlPath(url)
      wx.hideLoading()
      dispatch(actions.storageActions.removeItem('userState'))
      dispatch(actions.storageActions.removeItem('myResumeDetails'))
      dispatch(actions.storageActions.setItem({ key: 'resumeTabPosition', value: [] }))
      dispatch(actions.storageActions.setItem({ key: 'selectPositionTabId', value: { userId: '', selectItem: {} } }))
      if (loginWhite.indexOf(urlPath) === -1) {
        const currentPage = wx.$.r.getCurrentPage()
        // 当前页是切换账号就跳验证码登录页
        const query = currentPage && currentPage.route == 'subpackage/member/change-account/index' ? { auth_type: 2, fromPage: 'changeAccount' } : {}
        toLogin(true, query).then(async () => {
          // 登录成功后刷新当前页
          const isPopup = await getLoginAb(['RegLog_Only_Button', 'RegLog_All_New'])
          if (!query.auth_type && isPopup) {
            const params = wx.$.r.getParams()
            wx.$.r.replace({ path: `/${currentPage.route}`, query: currentPage.options, params })
          }
        })
      }
      return false
    }
    // 是否展示 错误消息提示
    const showMsgTip = msg && !extra.hideMsg
    if (showMsgTip) {
      wx.hideLoading()
      wx.$.msg(msg)
    }
    return false
  }
  return true
}

/** 异常通用弹框的逻辑处理 */
export const handleErrorPopup = async (dialogData): Promise<false> => {
  if (!dialogData.dialogIdentify) {
    return false
  }
  const popup = await dealDialogApi(dialogData)
  wx.hideLoading()
  wx.$.showModal({
    ...popup,
    only: true,
    success(result) {
      const routePath = wx.$.u.getObjVal(result, 'routePath', '')

      /** 这里根据弹框标识来做不同的处理 */
      switch (`${dialogData.dialogIdentify}`) {
        case 'blacklistOperate': // 用户用户状态是拉黑的情况，这个跳转到登录页
          handleToLogin()
          return
        case 'jgtzwnmb':
        case 'wgyhjg':
          const pages = getCurrentPages()
          const currentPage = pages[pages.length - 1]
          if (currentPage.route == 'subpackage/tim/groupConversation/index') {
            console.log('currentPage:', currentPage)
            if (currentPage.onLoad && currentPage.options) {
              currentPage.onLoad(currentPage.options)
            } else {
              wx.$.r.back()
            }
          }
          return
        default:
      }

      /** 这里根据弹框点击按钮的参数来做不同的处理 */
      switch (`${routePath}`) {
        case 'viewDetails': // 账号异常的跳转处理
          wx.$.r.push({ path: '/subpackage/systips/index' })
          break
        case 'toLogin': // 用户用户状态是拉黑的情况，这个跳转到登录页
          handleToLogin()
          break
        case 'callServicePhone':
          wx.makePhoneCall({ phoneNumber: SERVER_PHONE })
          break
        case 'goback':
          const pages = getCurrentPages()
          const currentPage = pages[pages.length - 1]
          const noBackArr = [
            'pages/index/index',
            'subpackage/recruit/details/index',
            'subpackage/recruit/listSearchResultsPage/index',
            'pages/resume/index',
            'subpackage/resume/detail/index',
            'subpackage/resume/listSearchResultsPage/index',
          ]
          if (!noBackArr.includes(currentPage.route)) {
            wx.$.r.back()
          }
          break
        default:
      }
    },
  })
  return false
}

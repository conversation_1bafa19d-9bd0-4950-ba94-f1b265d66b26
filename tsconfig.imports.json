{
  "compilerOptions": {
    "skipLibCheck": true,
    "allowJs": true,
    "module": "CommonJS",
    "lib": ["ESNext", "dom"],
    "strict": false,
    "esModuleInterop": true,
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"]
    },
    "typeRoots": ["./typings"],
    "experimentalDecorators": true,
    "noEmit": true,
    "moduleResolution": "node",
    
    // 只检查模块解析相关的错误
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noImplicitAny": false,
    "noImplicitThis": false,
    "noImplicitReturns": false,
    "noFallthroughCasesInSwitch": false,
    "strictNullChecks": false,
    "strictFunctionTypes": false,
    "strictBindCallApply": false,
    "strictPropertyInitialization": false,
    "noImplicitOverride": false,
    
    // 关闭大部分类型检查，只保留模块解析
    "suppressImplicitAnyIndexErrors": true,
    "suppressExcessPropertyErrors": true
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "packages"
  ]
}

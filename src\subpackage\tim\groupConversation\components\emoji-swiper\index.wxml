<!-- 表情 -->
<!-- <swiper indicator-dots>
  <swiper-item wx:for="{{EmojiList}}" wx:for-item="emoji" wx:for-index="idx" wx:key="em_p_{{idx}}">
    <view class="swiper-v">
      <view class="item-img-v " wx:for="{{emoji}}" wx:for-index="cidx" wx:key="em_c_{{cidx}}" wx:for-item="it" data-em="{{it}}" catch:tap="onEmojiClick">
        <image lazy-load="{{true}}" wx:if="{{!!it}}" class="swiper-item-img" src="../emoji/{{cidx+idx*26}}.png" />
        <view wx:else class="swiper-item-img"></view>
      </view>
      <view class="item-img-v " catch:tap="onEmojiDel">
        <image lazy-load="{{true}}" class="swiper-item-img" src="../emoji/1000.png" />
      </view>
    </view>
  </swiper-item>
</swiper> -->
<view class="emoji">
  <scroll-view 
    enhanced
    scroll-y 
    show-scrollbar="{{false}}"
    class="emoji-scroll"
    bindscroll="onScroll"
  >
    <view class="emoji-v">
        <view class="emoji-cotent">
          <view wx:for="{{EmojiList}}" wx:key="index" catch:tap="onEmojiClick" style="opacity:{{obem[index] ? (obem[index].opacity || 0) : 1}}" data-em="{{item}}" data-index="{{index}}">
            <image lazy-load="{{true}}" style="opacity:{{isOK?1:0}}" data-index="{{index}}" class="emoji-img" src="../emoji/{{index}}.png" />
          </view>
        </view>
    </view>
  </scroll-view>
  <view class="emoji-btn-v">
      <view class="emoji-btn" catch:tap="onEmojiDel">
          <image lazy-load="{{true}}" class="del-img" src="../emoji/1001.png" />
      </view>
  </view>
</view>
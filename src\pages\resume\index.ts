/*
 * @Date: 2021-12-23 11:22:18
 * @Description: 找活大列表
 */

import { MapStateToData, actions, connectPage, dispatch, messageQueue, store } from '@/store/index'
import { fakerThrottle, getDom, getHeaderHeight, getMenuButtonBoundingClientRect, rpxToPx } from '@/utils/tools/common/index'
import { nwSaveFilterStoreByIds, rqCommonButtonFreeStatus } from '@/utils/helper/common/index'
import { streamerModelArr } from '@/utils/helper/pop-list/index'
import { publishPage } from '@/lib/mini-component-page/index'
import resource from '@/components/behaviors/resource'
import { dealDialog, uploadDialog } from '@/utils/helper/dialog/index'
import { moveToHeader } from '../utils'
import { setWechatTDK } from './seo'
import { getIsNewLogic, shareTimelineMe } from './utils'
import { onScreePickerChange } from './components/position-tab/utils'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { isIos } from '@/utils/tools/validator/index'
import { getShareInfoByTypeV2 } from '@/utils/helper/share/index'
import { SHARE_CHANNEL } from '@/config/share'
import miniConfig from '@/miniConfig/index'

const mapStateToData: MapStateToData = (state) => {
  const { storage } = state
  const { selectPositionTabId, resumeTabPosition, isNewLogic } = storage
  // eslint-disable-next-line no-nested-ternary
  const jobTipType = selectPositionTabId.selectItem?.isDraft ? 'draft' : (selectPositionTabId.selectItem?.checkStatus == 0 ? 'fail' : 'check')
  return {
    login: storage.userState.login,
    selectPositionTabId,
    resumeTabPosition,
    // 职位状态
    jobTipType,
    // 未发布职位简历列表ab实验
    isNewLogic,
    // 招工列表的未登录文案
    loginTextGroup: state.config.loginTextGroup,
  }
}
const { top, height: rectHeight } = getMenuButtonBoundingClientRect()
let scrollTimer = null
let initPage = true
Page(
  connectPage(mapStateToData)(
    publishPage(['onReachBottom', 'onPullDownRefresh', 'onPageScroll'])({
      ...(!ENV_IS_SWAN ? { behaviors: [resource] } : {}),
      data: {
        ...(ENV_IS_SWAN ? resource.data : {}),
        // 导航高度
        // navHeight: getHeaderHeight(`${Math.floor(rpxToPx(14))}px`),
        navHeight: getHeaderHeight('14rpx'),
        filterOffset: getHeaderHeight('14rpx'),
        cpNavHeight: 0,
        paddingTop: top, // 高度动态计算
        noData: false,
        firstLoaded: false, // 第一次加载列表是否已完成
        occResumeShow: false, // 判断用户是否点击跳转工种页面,选择工种返回时必现show和选中工种时重复请求列表接口
        cityResumeShow: false, // 判断用户是否点击跳转城市页面,选择城市返回时必现show和选中工种时重复请求列表接口
        isSupply: false,
        // 筛选框距离顶部的偏移量
        filterOffsetTop: 0,
        resumesLeftTopIcon: {
          desc: '优质牛人',
        },
        recommendWorkerParams: { workerNumber: 0, provinceId: '', cityId: '', classifyIds: '', miniQualityWorkerEnterId: 3 },
        newsTickerResume: {},
        iconJobDiamond: {},
        /** 资源位标识对应的数据key */
        resourceKeys: {
          news_ticker_resume: 'horseRLData',
          icon_resume_diamond: 'iconDiamond',
          float_resume_under: 'buoyUnder',
          float_resume_top: 'buoyTop',
        },
        /** 资源位——跑马灯 */
        horseRLData: null,
        /** 资源位——金刚区 */
        iconDiamond: null,
        /** 资源位——浮标下 */
        buoyUnder: null,
        /** 资源位——浮标上 */
        buoyTop: null,
        /** 智能推荐岗位弹窗 */
        showRecommendPop: false,
        /** 发布招工弹窗  */
        showPublishWorkPop: false,
        /** 管理招工弹窗 */
        showContinuePop: false,
        /** 当前弹窗 */
        currDialog: {},
        fixedHeader: false,
        /** 更换职位属性 */
        isListOk: false, // 判断列表是否有数据
        cpShow: false, // 是否显示更换职位卡片
        cpList: [], // 更换职位的数据
        cpVisible: false, // 是否显示更换职位弹框
        cpJobId: '', // 选中的更换职位的id
        cpCityIds: [], // 请求更换职位的城市参数
        cpOccupationIds: [], // 请求更换职位的职位参数
        cpDialogIdentify: '', // 选择职位弹框1，弹框2
        cpConfirmImInit: false, // 选择职位后，点击确定是否跳转IM会话详情
        isShowPublishButton: false, // 是否展示发布职位按钮
        marketingVisible: false, // 成都投放专项活动弹出
      },
      ...(ENV_IS_SWAN ? resource.methods : {}),

      /**
       * 计算筛选框吸顶状态
       * scrollPage 表示 页面执行滚动的时候触发 的函数
      */
      async computedFilterFixedStatus(origin: 'scrollPage' | undefined) {
        const domInfo = await getDom('#filter')
        let { filterOffsetTop } = this.data
        if (!domInfo) {
          return
        }
        if (filterOffsetTop < 10) {
          const header = await getDom('#header')
          if (header && header.bottom) {
            filterOffsetTop = (header.bottom || (rpxToPx(14) + top + rectHeight)) + (rpxToPx(66) + rpxToPx(40)) + (isIos() ? 1 : 0)
            this.setData({ filterOffsetTop })
          }
        }
        /**
       * 如果是滚动 时调用的 当前函数，那么在滚动结束后 200ms 再次调用函数。
       * 避免最后一次调用 获取到 domInfo 和 filterOffsetTop 的值不对，
       */
        if (origin === 'scrollPage') {
          if (scrollTimer) {
            clearTimeout(scrollTimer)
          }
          scrollTimer = setTimeout(() => {
            this.computedFilterFixedStatus()
          }, 200)
        }

        if ((domInfo.top) <= filterOffsetTop) {
          if (!this.data.fixedHeader) {
            this.setData({ fixedHeader: true })
          }
        } else if (this.data.fixedHeader) {
          this.setData({ fixedHeader: false })
        }
      },
      /** 发布职位 */
      onJumpFastIssue() {
        // 埋点
        wx.$.collectEvent.event('post_job_click', {
          source_id: '1',
          click_button: '发布职位',
        })
        wx.$.r.push({ path: '/subpackage/recruit/fast_issue/index/index' })
      },
      onPageScroll: fakerThrottle(function (e) {
        if (this.timer) {
          clearInterval(this.timer)
          this.timer = null
        }
        this.computedFilterFixedStatus()
      }, 16),
      onUnload() {
        if (this.timer) {
          clearInterval(this.timer)
          this.timer = null
        }
      },
      // 改变是否无数据
      onCheckNoData({ detail: { value, currentPage, isSupply } }) {
        const sData: any = { noData: value, isSupply }
        if (currentPage == 1) {
          sData.isSupply = isSupply
        }
        this.setData(sData)
      },
      async onShow() {
        // const { userLocationCity } = store.getState().storage
        // dispatch(actions.storageActions.setItem({ key: 'userLocationCity', value: { ...userLocationCity, resumeSearchCityObj: null } }))
        ENV_IS_SWAN && resource.pageLifetimes.show.call(this)
        if (this.data.occResumeShow) {
          this.setData({ occResumeShow: false })
          return
        }
        if (this.data.cityResumeShow) {
          this.setData({ cityResumeShow: false })
          return
        }
        await onScreePickerChange()
        // 如果实验改变则重新获取
        this.getSelectHeight()
        this.setData({ filterOffsetTop: 0 })
        // 获取筛选框距离顶部的偏移量
        // 城市或工种改变时刷新列表或执行事件
        this.cityOrClassifyChange()
        /** 获取加急推荐列表 */
        // ! 已和产品乔雪沟通，暂时下掉推荐师傅弹框
        // this.getUrgentRecommend()
        // 返回页面时也要判断吸顶状态
        wx.nextTick(() => {
          !initPage && this.computedFilterFixedStatus()
          initPage = false
        })
      },
      getSqIds(cityObj) {
        const { resumeCityObj, id } = cityObj || {} as any
        const { citys: resCitys, id: resId } = resumeCityObj || {}
        let resArr = []
        if (wx.$.u.isArrayVal(resCitys)) {
          resArr = resCitys.map(item => item?.id).filter(Boolean).join(',')
        } else if (resId) {
          resArr = [resId]
        } else if (id) {
          resArr = [id]
        }
        return resArr
      },
      // 城市或工种改变时刷新列表或执行事件
      cityOrClassifyChange() {
        // 城市变化时重新请求接口
        const { userLocationCity } = store.getState().storage
        const nIds = this.getSqIds(userLocationCity)
        const oIds = this.getSqIds(this.oldUserLocationCity)
        let isChange = false
        if (nIds && oIds && !wx.$.u.arraysEqual(nIds, oIds)) {
          isChange = true
          // 获取列表
          this.onRefresh(true)
        }
        // 工种变化时重新请求接口
        if (this.oldResumeClassify && !isChange) {
          const { resumeClassify } = store.getState().storage
          const oldResumeClassify = JSON.stringify(this.oldResumeClassify)
          const newResumeClassify = JSON.stringify(resumeClassify)
          if (oldResumeClassify != newResumeClassify) {
            isChange = true
            // 获取列表
            this.onRefresh()
          }
        }
        if (this.oldResumeFilter && !isChange) {
          const { resumeFilterScreen } = store.getState().listFilter
          const oldResumeFilter = JSON.stringify(this.oldResumeFilter.value)
          const newResumeFilter = JSON.stringify(resumeFilterScreen.value)
          const ageFilter = resumeFilterScreen.filter.age && JSON.stringify(resumeFilterScreen.filter.age)
          const oldAgeFilter = this.oldResumeFilter.filter.age && JSON.stringify(this.oldResumeFilter.filter.age)
          if (oldResumeFilter != newResumeFilter || ageFilter != oldAgeFilter) {
            isChange = true
            // 获取列表
            this.onRefresh()
          }
        }

        if (!isChange) {
          this.checkNeedChange()
        }
      },
      // 打开工种筛选
      onShowClassFilter() {
        this.setData({
          showClassFilter: true,
        })
      },
      // 选择职位tab
      onChangeFilter() {
        this.setData({
          firstLoaded: false,
        })
        this.isRefresh = false
        this.onRefresh(true)
      },
      /** 筛选栏展开或关闭 */
      onTogglePicker(e) {
        if (e.detail.currentName == 'class' || e.detail.currentName == 'resumeFilterScreen') {
          this.setData({ occResumeShow: true })
        }
        if (e.detail.currentName == 'resumeAreaIdFilter') {
          this.setData({ cityResumeShow: true })
        }

        // this.setData({ fixedHeader: true })
      },
      /** 关闭加急推荐弹窗 */
      onClose() {
        this.setData({ recommendList: [] })
      },
      onHide() {
        // 保存旧城市用作对比
        const { userLocationCity } = store.getState().storage
        this.oldUserLocationCity = { ...userLocationCity }
        // 保存旧工种用作对比
        const { resumeClassify } = store.getState().storage
        this.oldResumeClassify = resumeClassify
        // 保存旧的筛选用作对比
        const { resumeFilterScreen } = store.getState().listFilter
        this.oldResumeFilter = resumeFilterScreen
      },
      async onLoad(options) {
        // 未登录才请求接口-显示未登录引导横幅
        const pageCode = getPageCode()
        dispatch(actions.configActions.getNoLoginCurrentText(pageCode))
        if (ENV_IS_SWAN || ENV_IS_TT) {
          /** 兼容百度小程序在调用relaunch后tabbar会重新出现的bug */
          wx.hideTabBar()
        }
        if (ENV_IS_SWAN) {
          resource.lifetimes.attached.call(this)
          const { top, width } = getMenuButtonBoundingClientRect()
          this.setData({ paddingTop: top, cachetWidth: width })
        }
        // 支持路由带参(分享配置的参数)
        const { area_id, classify_id, industry } = options || {}
        if (area_id || classify_id || industry) {
          let classIds = []
          if (classify_id) {
            classIds = await wx.$.l.transformClsIdHidsByStrIds(classify_id)
          } else if (industry) {
            classIds = [{ id: industry, hids: [industry] }]
          }
          // 处理页面传入页面的路由参数
          await nwSaveFilterStoreByIds(area_id, classIds, 'resume')
          /** 等待第一次请求列表结束 */
          await messageQueue(() => this.data.firstLoaded)
          this.onRefresh()
          setWechatTDK(area_id)
        }
        this.aggregation()
        const { isShowPublishButton } = await rqCommonButtonFreeStatus(4)
        getIsNewLogic()
        this.setData({ isShowPublishButton })
      },
      async onReady() {
        const params = wx.$.r.getParams()
        // 站内携带参数
        if (params.area_id || params.classify_id) {
          let classIds = []
          if (params.classify_id) {
            classIds = await wx.$.l.transformClsIdHidsByStrIds(params.classify_id)
          }
          // 处理页面传入页面的路由参数
          await nwSaveFilterStoreByIds(params.area_id, classIds, 'resume')
        }
        // 初始化数据
        this.init()
        this.getSelectHeight()
        this.timer = setInterval(() => {
          wx.nextTick(() => {
            this.computedFilterFixedStatus()
          })
        }, 1000)
      },
      // 初始化数据
      async init() {
        // 刷新列表
        this.onRefresh()
      },
      /** 触发第一次加载完成事件，并关闭骨架屏 */
      setFirstLoad() {
        !this.data.firstLoaded && setTimeout(() => this.setData({ firstLoaded: true }), 150)
      },
      async saveCity(searchPageSltedJob) {
        const { userLocationCity } = store.getState().storage
        const { cityId } = searchPageSltedJob || {}
        const { resumeSearchCityObj } = userLocationCity || {}
        if (cityId && !resumeSearchCityObj) {
          const item = (await wx.$.l.getAreaById(cityId)).current
          await dispatch(actions.storageActions.setItem({ key: 'userLocationCity', value: { ...userLocationCity, ...(item || {}), children: [], resumeSearchCityObj: { ...(item || {}), citys: [item], cityLen: 1 } } }))
        }
      },
      // 点击搜索框
      onSearch() {
        const { resumeTabPosition, searchPageSltedJob, selectPositionTabId, userState } = store.getState().storage
        const { login } = userState || {}
        const list = (resumeTabPosition || []).filter((rt) => rt.checkStatus == 2)
        wx.$.collectEvent.event('resume_search_box_click', { source_id: '1' })
        const path = '/subpackage/resume/search/index'
        if (login && wx.$.u.isArrayVal(list)) {
          const { jobId } = searchPageSltedJob || {}
          const oSearchPageSltedJob = list.find((rt) => rt.jobId == jobId)
          const { checkStatus: oCheckStatus } = oSearchPageSltedJob || {}
          const { selectItem } = selectPositionTabId || {}
          const { checkStatus } = selectItem || {} as any
          if ((!jobId || oCheckStatus != 2) && checkStatus == 2) {
            dispatch(actions.storageActions.setItem({ key: 'searchPageSltedJob', value: selectItem }))
            this.saveCity(selectItem)
          } else {
            const idx = list.findIndex((l) => l.jobId == jobId && l.checkStatus == 2)
            if (idx < 0) {
              dispatch(actions.storageActions.setItem({ key: 'searchPageSltedJob', value: list[0] }))
              this.saveCity(list[0])
            }
          }
        }
        wx.$.r.push({
          path,
          success: () => {
            // 关闭筛选器
            wx.$.selectComponent.call(this, '#filter').then((comp) => comp.onClose())
          },
        })
      },
      // 刷新
      async onRefresh(notToHeader = false) {
        if (this.isRefresh) return
        this.isRefresh = true
        setTimeout(() => {
          this.isRefresh = false
        }, 2000)
        if (!notToHeader) {
          moveToHeader()
        } else {
          wx.pageScrollTo({
            scrollTop: 0, // 滚动到的位置（距离顶部 px）
            duration: 200, // 滚动所需时间 如果不需要滚动过渡动画，设为0（ms）
          })
        }
        wx.$.selectComponent.call(this, '#card-list').then((comp) => comp.onRefresh())
        this.getJoinGroupParams()
      },
      // 更新卡片拨打电话或聊一聊按钮状态
      async onCardBtnRefresh(resumeSubUuid, type) {
        const comp = await wx.$.selectComponent.call(this, '#card-list')
        const { list = [] } = comp.data
        const idx = list.findIndex(item => item.resumeSubUuid == resumeSubUuid)
        if (idx < 0) {
          return
        }
        const item = list[idx] || {}
        const { rightInfo } = item || {}
        if (type == 'chat') {
          comp.setData({ [`list[${idx}]`]: { ...item, rightInfo: { ...rightInfo, hasImRight: true } } })
        } else if (type == 'call') {
          comp.setData({ [`list[${idx}]`]: { ...item, rightInfo: { ...rightInfo, hasTelRight: true } } })
        }
      },
      /** 刷新并滚到顶部 */
      onRefreshAndTop(e) {
        const { name } = e.detail || {}
        const { resumeOption } = store.getState().storage.common
        if (name == 'resumeAreaIdFilter' && !resumeOption.city) {
          dispatch(actions.storageActions.setCommonItem({ resumeOption: { ...resumeOption, city: true } }))
        }
        if (name == 'class' && !resumeOption.classify) {
          dispatch(actions.storageActions.setCommonItem({ resumeOption: { ...resumeOption, classify: true } }))
        }
        this.onRefresh()
        moveToHeader()
      },
      /** 点击搜索跳转 */
      onSearchClick() {
        wx.$.r.push({ path: '/subpackage/common/ypsearch/index', query: { origin: 'resume' } })
      },
      /** 获取加群组件的请求参数 */
      async getJoinGroupParams() {
        const { selectPositionTabId } = this.data
        const { selectItem } = selectPositionTabId
        const isR = !!selectItem?.jobId
        const userOccList = []
        let province

        if (!isR) {
          // 获取工种数据及label，id
          const { resumeOcc2Value } = store.getState().classify
          const { userLocationCity } = store.getState().storage
          const { resumeCityObj } = userLocationCity || {} as any
          await Promise.all(resumeOcc2Value.map(item => item.occIds).flat().filter(item => !!item).map(async item => {
            const occV2 = await wx.$.l.getClassifyByIds([item])
            userOccList.push({
              occName: occV2 && occV2[0] && occV2[0].name,
              occId: item,
            })
          }))
          province = (await wx.$.l.getAreaById(resumeCityObj?.id))?.province
        } else {
          await Promise.all(selectItem.selectOccList.map(item => {
            const i = selectItem.occList.find(i => i.occId == item)
            userOccList.push(i)
          }))
          province = (await wx.$.l.getAreaById(selectPositionTabId.selectItem.cityId))?.province
        }
        const params = {
          provinceName: province ? province.name : '',
          userOccList,
        }
        this.setData({ joinGroupParams: params })
      },
      /** 设置当前页面的分享内容 */
      async onShareAppMessage(options) {
        // 截图分享
        if (this.data.screenshotShareData && !!this.data.screenshotShareData.sharePage && options.from != 'button') {
          return getShareInfoByTypeV2({ type: SHARE_CHANNEL.SHARE_WECHAT_FRIEND, ext: {}, from: options.from })
        }
        if (miniConfig.token == 'zjzg') {
          return getShareInfoByTypeV2({ type: SHARE_CHANNEL.SHARE_WECHAT_FRIEND, sharePage: 'WorkingPeopleList', sharePath: 'WorkingPeopleList_B', ext: {}, from: options.from })
        }
        return getShareInfoByTypeV2({ type: SHARE_CHANNEL.SHARE_WECHAT_FRIEND, sharePage: 'DNFindWorkersList', sharePath: 'DN_FindWorkersList', ext: {}, from: options.from })
      },
      /** 分享朋友圈 */
      onShareTimeline() {
        return shareTimelineMe()
      },

      // 金刚区接口请求返回的数据，用户左上角显示
      onBannerBack(e) {
        this.setData({ resumesLeftTopIcon: e.detail })
      },

      onDisableMove() { },

      onPullDownRefresh() {
        const { isNewLogic, resumeTabPosition } = this.data
        if (isNewLogic && !resumeTabPosition.length) return
        this.setData({
          firstLoaded: false,
        })
        wx.$.selectComponent.call(this, '#position-tab').then((comp) => comp.init(false))
          .then(() => {
            this.onIsChat({ detail: { have: false } })
            this.onRefresh(1, false, false).finally(() => {
              wx.stopPullDownRefresh()
            })
          }).catch(() => {
            this.onIsChat({ detail: { have: false } })
            this.onRefresh(1, false, false).finally(() => {
              wx.stopPullDownRefresh()
            })
          })
      },
      /** 刷新供其他页面所使用（同下拉刷新一个逻辑） */
      pageRefresh() {
      },
      /** 关闭智能推荐岗位弹窗 */
      onCloseRecommend() {
        uploadDialog({ popCode: this.data.currDialog.popCode, action: 2 })
        this.setData({ showRecommendPop: false, currDialog: {} })
      },

      onClosePublishWork() {
        uploadDialog({ popCode: this.data.currDialog.popCode, action: 2 })
        this.setData({ showPublishWorkPop: false, currDialog: {} })
      },
      onCloseAggregation({ target }) {
        const visibleKey = target.dataset && target.dataset.visible
        this.setData({
          currDialog: {},
          ...(visibleKey ? { [visibleKey]: false } : {}),
        })
      },

      /** 聚合弹窗 */
      async aggregation() {
        const result = await dealDialog('resume_list')
        if (!result) {
          return
        }
        const { currDialog, dialogKey, dialog_identify } = result
        const currentPage = wx.$.r.getCurrentPage()
        if (currentPage.route !== 'pages/resume/index') {
          return
        }
        if (streamerModelArr.includes(dialog_identify)) {
          const { logicTransferData } = currDialog || {}
          wx.$.model.streamerModel({ isQueue: true, dialogKey: dialog_identify, logicTransferData })
          return
        }
        /** 成都专项活动弹窗 */
        if (dialog_identify == 'marketing_AuthenticationEvent') {
          this.setData({ currDialog, marketingVisible: true })
          return
        }
        /** 智能推荐弹窗 */
        if (dialog_identify === 'zhinengtuijiangangwei') {
          this.setData({ currDialog, showRecommendPop: true })
          return
        }

        /** 发布招工弹窗 */
        if (['fast_job_mini', 'fast_job_mini1'].includes(dialog_identify)) {
          this.setData({ currDialog, showPublishWorkPop: true })
          return
        }
        /** 管理招工弹窗 */
        if (dialog_identify === 'refresh_job_new1') {
          this.setData({ currDialog, showContinuePop: true })
          return
        }
        if (dialogKey) {
          // 通用弹框
          wx.$.showModal({
            ...result,
          })
        }
      },

      // 获取选择器的高度
      getSelectHeight() {
        getDom('#resume-filter').then((res) => {
          const { height } = res || {}
          if (height) {
            this.setData({
              cpNavHeight: getHeaderHeight(` calc(66rpx + 40rpx + 14rpx - 4rpx) + ${height}px`),
              navHeight: getHeaderHeight('calc(66rpx + 40rpx + 14rpx - 4rpx)'),
            })
          }
        })
      },
      // 判断onShow的时候是否需要重新请求更换职位接口
      checkNeedChange() {
        const { updatePositionShow, isSendCommunicate } = store.getState().index
        const { isListOk, cpShow } = this.data
        if ((updatePositionShow || isSendCommunicate) && isListOk && !cpShow && this.isChatReq) {
          dispatch(actions.recruitIndexActions.setState({ isSendCommunicate: false }))
          this.setData({ cpShow: true })
        } else if (!updatePositionShow && isListOk && cpShow && this.isChatReq) {
          this.setData({ cpShow: false })
        }
      },
      async onCpConfirm(e) {
        const { jobId } = e.detail
        const { isListOk, cpShow, selectPositionTabId } = this.data
        if (isListOk && cpShow && !selectPositionTabId.selectItem.jobId) {
          const changePosition = await wx.$.selectComponent.call(this, '#changePosition')
          changePosition.onConfirm(e)
        } else {
          this.onResumeConfirm(jobId)
          this.setData({ cpVisible: false })
        }
        const { cpConfirmImInit, reGetImChat } = this.data
        if (cpConfirmImInit && reGetImChat) {
          reGetImChat({ relatedInfoId: jobId })
          this.setData({ cpConfirmImInit: false, reGetImChat: null })
        }
      },
      onCpNoSend() {
        const { isListOk, cpShow, selectPositionTabId } = this.data
        wx.$.javafetch['POST/reach/v2/im/chat/clearUserRelatedJobId']({}, { isNoToken: true, hideMsg: true }).then(async () => {
          if (isListOk && cpShow && !selectPositionTabId.selectItem.jobId) {
            const changePosition = await wx.$.selectComponent.call(this, '#changePosition')
            changePosition.resumeInit()
          }
        })
        const { cpConfirmImInit, reGetImChat } = this.data
        if (cpConfirmImInit && reGetImChat) {
          reGetImChat()
          this.setData({ cpConfirmImInit: false, reGetImChat: null })
        }
      },
      onCpSelected(e) {
        const { jobId } = e.detail
        this.setData({ cpJobId: jobId })
      },
      onCpListChange(e) {
        const { list } = e.detail
        this.setData({
          cpList: list,
        })
      },
      onCpShowChange(e) {
        const { show } = e.detail
        if (!show) {
          this.isChatReq = false
        }
        this.setData({
          cpShow: show,
        })
      },
      onCpClick() {
        this.setData({
          cpVisible: true,
        })
      },
      onCpClose() {
        this.setData({
          cpVisible: false,
          cpConfirmImInit: false,
          reGetImChat: null,
        })
      },
      onIsChat(e) {
        const { have } = e.detail
        if (!have) {
          this.isChatReq = false
          this.setData({ cpShow: false })
        } else {
          const { cpShow } = this.data
          if (!this.isChatReq && !cpShow) {
            this.isChatReq = true
            this.setData({ cpShow: true })
          }
        }
      },
      onListChange(e) {
        if (this.isRefresh) {
          this.isRefresh = false
          this.onIsChat({ detail: { have: false } })
        }
        const { cpCityIds, cpOccupationIds } = this.data
        const { list, cpCityIds: nCpCityIds, cpOccupationIds: nCpOccupationIds } = e.detail
        const { isListOk } = this.data
        const sData: any = {}
        if (list.length > 0 && !isListOk) {
          sData.isListOk = true
        } else if (list.length == 0 && isListOk) {
          sData.isListOk = false
        }
        if (!wx.$.u.arraysEqual(cpCityIds, nCpCityIds)) {
          sData.cpCityIds = nCpCityIds
        }
        if (!wx.$.u.arraysEqual(cpOccupationIds, nCpOccupationIds)) {
          sData.cpOccupationIds = nCpOccupationIds
        }
        if (!wx.$.u.isEmptyObject(sData)) {
          this.setData(sData)
        }
      },
      async onSelectPostionBack(e) {
        const { isListOk, cpShow, selectPositionTabId } = this.data
        const { dialogIdentify, reGetImChat } = e.detail
        this.setData({ cpDialogIdentify: dialogIdentify, cpConfirmImInit: true, reGetImChat })
        if (isListOk && cpShow && !selectPositionTabId.selectItem.jobId) {
          const changePosition = await wx.$.selectComponent.call(this, '#changePosition')
          await changePosition.resumeBossJobList()
        } else {
          this.resumeBossJobList()
        }
        wx.hideLoading()
      },

      async resumeBossJobList() {
        const { cityIds, occupationIds } = this.data
        const res = await wx.$.javafetch['POST/reach/v2/im/chat/bossJobList']({ cityIds, occupationIds })
        const { data } = res || {}
        const { jobList, jobToast } = data || {}
        const nsData: any = { jobToast }
        if (wx.$.u.isArrayVal(jobList)) {
          nsData.list = jobList
        } else if (jobToast) {
          wx.$.msg(jobToast)
          return
        }
        this.setData({ cpList: nsData.list, cpVisible: true })
      },
      onResumeConfirm(jobId) {
        wx.$.javafetch['POST/reach/v2/im/chat/changeUserRelatedJobId']({ relationJobId: jobId }).then((res) => {
          dispatch(actions.recruitIndexActions.setState({ updatePositionId: jobId }))
        })
      },
      /** 跳转职位 */
      onJumpJob() {
        const { selectPositionTabId } = this.data
        const path = selectPositionTabId.selectItem?.isDraft ? '/subpackage/recruit/edit-draft/index' : '/subpackage/recruit/jisu_issue/index'
        // 埋点
        wx.$.collectEvent.event('job_tip_click', {
          source_id: '1',
          click_button: '发布职位',
        })
        wx.$.r.push({
          path,
          query: {
            id: selectPositionTabId.selectItem.jobId,
            draftId: selectPositionTabId.selectItem.jobDraftId,
          },
        })
      },
    }),
  ),
)

// .swiper-v {
//   padding: 5rpx;
//   height: auto;
//   display: flex;
//   justify-content: flex-start;
//   flex-wrap: wrap;
// }

// .item-img-v {
//   padding: 20rpx;
// }

// .swiper-item-img {
//   height: 42rpx;
//   width: 42rpx;
// }

.emoji {
  position: relative;
  background: rgba(245, 246, 250, 1);
}

.emoji-scroll {
  height: 440rpx;
}

.emoji-v {
  padding: 32rpx 32rpx 32rpx 33rpx;
}

.emoji-cotent {
  margin: -20rpx -17rpx;
  display: flex;
  flex-wrap: wrap;
}

.emoji-img {
  width: 57rpx;
  height: 57rpx;
  margin: 20rpx 17rpx;
}

.emoji-btn-v {
  position: absolute;
  padding: 24rpx;
  right: 0;
  bottom: -2rpx;
}

.emoji-btn {
  background: rgba(255, 255, 255, 1);
  padding: 12rpx 32rpx;
  border-radius: 8rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.del-img {
  width: 48rpx;
  height: 48rpx;
}

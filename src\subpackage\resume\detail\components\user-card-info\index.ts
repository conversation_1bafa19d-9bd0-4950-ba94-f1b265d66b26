/*
 * @Date: 2022-03-24 13:56:15
 * @Description: 找活名片的卡片信息
 */

import { getDom } from '@/utils/tools/common/index'
import { MapStateToData, store, connect } from '@/store/index'
import { getQueryScene } from '../../utils'

const mapStateToData: MapStateToData = () => {
  return {}
}

Component(
  connect(mapStateToData)({
    properties: {
      detail: { type: null, value: {} },
      /** 路由参数 */
      query: { type: null, value: {} },
      /** isHiddenMode */
      maskAndVisible: { type: Object,
        value: {
          hidden: {
          /** 是否隐藏VIP banner */
            vipBanner: false,
            /** 隐藏视频简历 */
            video: false,
            /** 隐藏附近工人 */
            nearbyWorkers: false,
          },
          mask: {
          /** 模糊工作经历 */
            workExp: false,
            /** 模糊教育经历 */
            edu: false,
            /** 模糊项目经验 */
            projExp: false,
            /** 模糊技能证书 */
            cert: false,
          },
        } },

      hasRight: { type: Boolean, value: false },
    },
    data: {
      // 是否展示展示更多内容按钮
      showExpandBtn: false,
      // 是否展示显示更多工作经历按钮
      showExpandAllExp: false,
      showAllIntroduce: false,
      // 是否展示更多求职期望更多的按钮,
      isShowMore: true,
      // 是否展示 全部资格证书的按钮
      isShowMoreCates: true,
      /** 是否开启活跃标签AB实验 */
      isActiveAb: false,
      /** 近期查看职位 */
      myBrowseJobCount: '',
      /** 近期被查看 */
      browseMeResumeCount: '',
      /** */
      maskImage: false,
    },
    lifetimes: {
      ready() {
        wx.$.u.isAbUi('resumeTags', 'newLogic').then(isActiveAb => {
          this.setData({
            isActiveAb,
          })
        })
      },
    },
    observers: {
      'detail.introduce': function (val) {
        wx.nextTick(async () => {
          if (val) {
            const outer = (await getDom.call(this, '#introduce')) || {} as any
            const inner = (await getDom.call(this, '#introduce-content')) || {} as any
            if (outer.height < inner.height) {
              this.setData({ showExpandBtn: true })
            } else {
              this.setData({ showExpandBtn: false })
            }
          }
        })
      },
      'detail.subs': function (val) {
        wx.nextTick(async () => {
          if (val?.length > 0) {
            const outer = (await getDom.call(this, '#tag-box')) || {} as any
            const inner = (await getDom.call(this, '#tag-cover')) || {} as any
            if (outer.height < inner.height) {
              this.setData({ isShowMore: true })
            } else {
              this.setData({ isShowMore: false })
            }
          }
        })
      },
      'detail.userCertificateInfos': function (val) {
        wx.nextTick(async () => {
          if (val?.length > 0) {
            const outer = (await getDom.call(this, '#tag-stabilization')) || {} as any
            const inner = (await getDom.call(this, '#tag-cates-cover')) || {} as any
            if (inner.height > outer.height) {
              this.setData({ isShowMoreCates: true })
            } else {
              this.setData({ isShowMoreCates: false })
            }
          }
        })
      },
      'detail.myBrowseJobCount': function (count) {
        if (count && count > 99) {
          this.setData({ myBrowseJobCount: '99+' })
          return
        }
        this.setData({ myBrowseJobCount: count })
      },
      'detail.browseMeResumeCount': function (count) {
        if (count && count > 99) {
          this.setData({ browseMeResumeCount: '99+' })
          return
        }
        this.setData({ browseMeResumeCount: count })
      },
      'query.name, detail.name, detail.hasImChatRight': function () {
        const { name, hasImChatRight } = this.data.detail || {}
        const { query = {} } = this.data || {}
        const nameText = query.name || name
        const sceneV2 = getQueryScene(query)
        /** 来自搜索结果页没有IM权益，收藏来自于搜索结果页没有IM权益，搜索畅聊卡无权益 */
        if ((sceneV2 == 3 && !hasImChatRight) || (sceneV2 == 11 && !hasImChatRight && query.infoSource == 1) || (sceneV2 == 14 && !hasImChatRight)) {
          this.setData({ nameText: nameText && nameText !== '先生' && nameText !== '女士' ? `${Array.from(nameText)[0]}**` : '**', maskImage: true })
        } else {
          this.setData({ nameText: nameText || '先生', maskImage: false })
        }
      },
    },
    methods: {
      // 展开求职期望的更多
      onClickMore() {
        this.setData({ isShowMore: false })
      },
      // 展开全部资格证书
      onClickMoreCates() {
        this.setData({ isShowMoreCates: false })
      },
      /** 点击面试视频 */
      onClickVideo() {
        const { videoResp } = this.data.detail
        wx.$.r.push({
          path: '/subpackage/video/video_others/index',
          params: {
            detail: this.data.detail,
            pageSource: 'resumeDetail',
            item: {
              videoUrl: videoResp.attach.filePath,
              videoCoverImg: videoResp.attach.cover,
            },
            showPhoneBtn: true,
          },
        })
      },
      /** 展示所有工作经历 */
      handleShowAllExp() {
        this.setData({ showExpandAllExp: true })
      },
      /** 展示所有信息 */
      handleShowAllIntroduce() {
        this.setData({ showExpandBtn: false, showAllIntroduce: true })
      },
      /** 投诉事件 */
      onComplain() {
        this.triggerEvent('complain')
      },
      /** 点击实名认证弹出弹窗 */
      async onShowModal() {
        await wx.$.u.waitAsync(this, this.onShowModal, [], 500)
        const { to_auth } = store.getState().user.userInfo
        const { login } = store.getState().storage.userState
        // 已登陆跳转认证并且没有实名
        if (login && to_auth) {
          wx.$.model.realName({
            type: 'realNameGrayHead',
            confirmText: '立即认证',
            cancelIcon: true,
          }).then(() => {
            wx.$.r.push({ path: '/subpackage/member/realname/index?origin=100021' })
          })
          return
        }
        wx.$.model.realName({ type: 'realNameSimple' })
      },
    },
  }),
)

<!-- 沟通卡片（老板卡片） -->
<view wx:if="{{isBoss}}" catch:tap="onClickContactCard" class="contact-card custom-class {{index===0?'no-margin-top':''}}" style="{{customStyle}}">
  <!-- 已下架 -->
  <image wx:if="{{isShowSoldOutIcon}}" class="icon-img" src="https://staticscdn.zgzpsjz.com/miniprogram/images/jl/yp-mini_concat_tag_out.png" lazy-load />
  <!-- 老板-已招满 -->
  <image wx:elif="{{isShowBoosFullIcon}}" class="icon-img" src="https://staticscdn.zgzpsjz.com/miniprogram/images/jl/yp-mini_concat_tag_max.png" lazy-load />
  <!-- ------ 头部信息 -->
  <view class="header">
    <view class="header-left">
      <view class="username">{{item.userName}}</view>
      <view wx:if="{{item.telMask}}" class="telMask">{{item.telMask}}</view>
      <yp-tag size="small">老板</yp-tag>
      <view class="hasCooperationTag" wx:if="{{item.cooperationType == 1}}">
        <image class="hasCooperationTagIcon" src="https://cdn.yupaowang.com/yp_mini/images/gyf/mini_handshake.png" />
        有合作意向
      </view>
      <yp-tag my-class="tag-bdr" wx:if="{{item.isShowMassBombing}}" class="mass_bom_label"  style="background:#E0F3FF;color:#0092FF;" size="small">群发炸弹</yp-tag>
    </view>
    <view class="isCooperation" wx:if="{{pageOrigin=='myContactHistory' && item.cooperationSwitch}}">
      <image catch:tap="markCooperation" class="isCooperationImg" src="https://cdn.yupaowang.com/yp_mini/images/gyf/mini_three_point.png" />
      <zoom opened="{{isCooperation}}" wx:if="{{isCooperation}}">
        <view class="mask" catch:touchmove="disabledMove" catch:tap="closeMarkCooperation"></view>
        <view class="tip-text-nearby {{item.cooperationType!=1?'mark-cooperation':''}}">
          <text class="text-value" catch:tap="updateCooperation">{{item.cooperationType!=1?'标记有合作意向':'取消意向标记'}}</text>
        </view>
      </zoom>
    </view>
  </view>
  <!-- ------ 招工展示标题信息 -->
  <view wx:if="{{isBoss&&item.title}}" class="title">{{item.title}}</view>
  <!-- ------ 到岗时间 -->
  <view class="on-duty-time" wx:if="{{item.onDutyTime && item.cooperationType == 1}}">
    <view class="call-start-time">{{item.onDutyTime}}</view>
  </view>
  <!-- ------ 拨打电话描述信息 -->
  <view class="call-phone-info">
    <view class="call-start-time">{{item.callStartTime}}</view>
    <view class="call-status">
      <!-- isNotConnect 是否未接通；0-已接通；1-未接通；2-已绑定，未拨打电话 -->
      <view wx:if="{{item.isNotConnect==1}}" class="not-connected">未接通</view>
      <view wx:elif="{{item.callTime}}">{{item.callTime}}</view>
    </view>
  </view>
  <!-- ------ 非我联系过谁卡片(谁联系过我卡片)已下架状态 -->
  <view wx:if="{{isShowSoldOutText}}" class="sold-out custom-sold-out">信息下架，无法拨打</view>
  <!-- ------ 底部操作 -->
  <view wx:elif="{{isShowComplainBtn}}" class="footer">
    <!-- 投诉 -->
    <view catch:tap="onClickComplainBtnBossCard" class="footer-btn {{item.canComplaint?'':'btn-gray'}}" hover-class="hover-btn">
      {{item.canComplaint?'投诉':'已投诉'}}
    </view>
  </view>
  <view wx:else class="footer">
    <!-- 评价 -->
    <view wx:if="{{item.canComment}}" catch:tap="onClickEvaluateBtn" class="footer-btn" hover-class="hover-btn">
      <icon-font custom-class="cont-ifont" type="yp-icon_edit_grzl" size="32rpx" color="rgba(0, 0, 0, 0.65)" />
      评价他
    </view>
    <!-- 真实号码 -->
    <view wx:if="{{isShowRealTelBtn}}" catch:tap="onClickRealPhoneBtn" class="footer-btn" hover-class="hover-btn">
      真实号码
    </view>
    <!-- 点击联系 -->
    <yp-button wx:if="{{isShowContactBtn}}" catch:tap="onClickCallPhoneBtn" debounceMS="{{2000}}" btnText="拨打电话" style="margin-left:16rpx;" my-class="call-phone-btn" ghost />
  </view>

</view>

<!-- 沟通卡片（牛人卡片） -->
<view wx:if="{{isWorker}}" catch:tap="onClickContactCard" class="contact-worker-card custom-class {{index===0?'no-margin-top':''}}" style="{{customWorkerStyle}}">
  <block wx:if="{{false}}">
    <!-- 师傅(工友)-已找到 -->
    <!-- <image wx:if="{{isShowWorkerFindItIcon}}" class="icon-img" src="https://staticscdn.zgzpsjz.com/miniprogram/images/jl/yp-mini_concat_tag_fill.png" lazy-load /> -->
    <!-- ------ 头部信息 -->
    <view class="header">
      <view class="header-left">
        <view class="username">{{item.userName}}</view>
        <view wx:if="{{item.telMask}}" class="telMask">{{item.telMask}}</view>
        <yp-tag type="success" size="small">牛人</yp-tag>
        <view class="hasCooperationTag" wx:if="{{item.cooperationType == 1}}">
          <image class="hasCooperationTagIcon" src="https://cdn.yupaowang.com/yp_mini/images/gyf/mini_handshake.png" />
          有合作意向
        </view>
        <yp-tag my-class="tag-bdr" wx:if="{{item.isShowMassBombing}}" class="mass_bom_label"  style="background:#E0F3FF;color:#0092FF;" size="small">群发炸弹</yp-tag>
      </view>
      <view class="isCooperation" wx:if="{{pageOrigin=='myContactHistory' && item.cooperationSwitch}}">
        <image catch:tap="markCooperation" class="isCooperationImg" src="https://cdn.yupaowang.com/yp_mini/images/gyf/mini_three_point.png" />
        <zoom opened="{{isCooperation}}" wx:if="{{isCooperation}}">
          <view class="mask" catch:touchmove="disabledMove" catch:tap="closeMarkCooperation"></view>
          <view class="tip-text-nearby">
            <text class="text-value" catch:tap="updateCooperation">{{item.cooperationType!=1?'标记有合作意向':'取消意向标记'}}</text>
          </view>
        </zoom>
      </view>
    </view>
    <!-- ------ 找活展示工种信息 -->
    <view wx:if="{{isWorker&&item.occupation.length}}" class="work-type-tags">
      <block wx:for="{{item.occupation}}" wx:for-item="tag" wx:key="tag" >
        <yp-tag wx:if="{{tag}}" class="work-type-tag" type="grey">{{tag}}</yp-tag>
      </block>
    </view>
    <!-- ------ 到岗时间 -->
    <view class="on-duty-time" wx:if="{{item.onDutyTime && item.cooperationType == 1}}">
      <view class="call-start-time">{{item.onDutyTime}}</view>
    </view>
    <!-- ------ 拨打电话描述信息 -->
    <view class="call-phone-info">
      <view class="call-start-time">{{item.callStartTime}}</view>
      <view class="call-status">
        <!-- isNotConnect 是否未接通；0-已接通；1-未接通；2-已绑定，未拨打电话 -->
        <view wx:if="{{item.isNotConnect==1}}" class="not-connected">未接通</view>
        <view wx:elif="{{item.callTime}}">{{item.callTime}}</view>
      </view>
    </view>
  </block>

  <!-- 已关闭-斜标 -->
  <image wx:if="{{isShowSoldOutIcon}}" class="oblique-icon-img" src=" https://cdn.yupaowang.com/yp_rn_app/ic_contact_close_tag.png" lazy-load />
  <resume-card-v4 custom-class="main-resume-info-class" isShowHasComplainTag="{{true}}" isShowCooperationTag="{{true}}" sourceId="{{cardType == 'myContacted' ? '22' :'23'}}" sceneV2="{{cardType == 'myContacted' ? '5' :'7'}}" item="{{item}}" data-item="{{item}}" data-index="{{index}}" showDutyLabel nearbyWorkerListApiSource="myContactHistory"/>


  <view class="footerBox">
    <!-- 左侧的沟通时间   - 时间显示优先级：安全号拨打时间>联系时间 -->
    <view class="footer-left">{{item.callStartTime || item.contactTimeStr}}<text class="connectStatus {{item.connectStatus == 1 ? 'red_c_style': ''}}">{{item.connectStatus == 1 ? '未接通' : item.callTime}}</text></view>
    <!-- <view wx:if="{{isShowComplainBtn}}" class="footer"> -->
      <!-- 投诉 -->
      <!-- <view catch:tap="onClickComplainBtnWorkerCard" class="footer-worker-btn {{item.complaintStatus ==  3 ?'':'btn-gray'}}" hover-class="hover-btn">
        {{item.complaintStatus ==  3 ? '投诉':'已投诉'}}
      </view>
    </view> -->
    <!-- 右侧按钮 -->
    <view wx:if="{{item.complaintStatus != 1 && item.complaintStatus != 2}}" class="footer">
      <!-- 真实号码 / 意向标记 -->
      <view class="zoomBox" wx:if="{{pageOrigin=='myContactHistory' && isShowMoreBtn}}">
        <image catch:tap="handleWorkerCooperation" class="cooperationImg" src="https://cdn.yupaowang.com/yupao_common/0fb12387.png " />
        <zoom opened="{{isWorkerCooperation}}" wx:if="{{isWorkerCooperation}}">
          <view class="mask" catch:touchmove="disabledMove" catch:tap="handleCloseWorkerCooperation"></view>
          <view class="tip-text-nearby-worker {{item.cooperationType!=1?'mark-cooperation-worker':''}} {{!isShowContactBtn ? 'tip-text-nearby-worker-right' : ''}}">
            <view class="text-value-worker" wx:if="{{isShowComplainBtn}}" catch:tap="onClickComplainBtnWorkerCard">投诉</view>
            <view class="text-value-worker" wx:if="{{item.canComment}}" catch:tap="onClickEvaluateBtn">评价他</view>
            <view class="text-value-worker" wx:if="{{isShowRealTelBtn}}" catch:tap="onClickRealPhoneBtn" >获取真实号码</view>
            <view class="text-value-worker" catch:tap="updateCooperation" wx:if="{{item.cooperationSwitch}}">{{item.cooperationType!=1?'标记有合作意向':'取消意向标记'}}</view>
          </view>
        </zoom>
      </view>
      <!-- 评价 -->
      <!-- <view wx:if="{{item.canComment}}" catch:tap="onClickEvaluateBtn" class="footer-worker-btn" hover-class="hover-btn">
        <icon-font custom-class="cont-icont" type="yp-icon_edit_grzl" size="32rpx" color="rgba(0, 0, 0, 0.65)" />
        评价他
      </view> -->
      <!-- 点击联系 -->
      <yp-button wx:if="{{isShowContactBtn}}" catch:tap="onClickCallPhoneBtn" debounceMS="{{2000}}" btnText="拨打电话" style="margin-left:16rpx;" my-class="call-phone-worker-btn" ghost />
    </view>
  </view>
</view>
.msg-red {
  height: 10rpx;
  width: 10rpx;
  background: red;
  border-radius: 50%;
}

.mr-l {
  margin-left: 24rpx;
}

.mr-r {
  margin-right: 24rpx;
}

.send-fail {
  width: 32rpx;
  height: 32rpx;
}

.sending {
  width: 48rpx;
  height: 48rpx;
}

.sending2 {
  width: 80rpx;
  height: 80rpx;
}

.animg {
  animation: rotateImg 0.5s infinite linear;
}

@keyframes rotateImg {
  from {
    transform: rotate(0);
  }

  to {
    transform: rotate(360deg);
  }
}

.msg-success {
  height: 100%;
  color: rgba(0, 0, 0, 0.25);
  font-size: 22rpx;
  margin-right: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.other-state {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.img-v {
  position: absolute;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  background: linear-gradient(rgba(0, 0, 0, 0.55), rgba(0, 0, 0, 0.55));
  border-radius: 24rpx;
}

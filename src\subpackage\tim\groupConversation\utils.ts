/*
 * @Author: xia<PERSON><PERSON><PERSON><PERSON>
 * @FilePath: /yp-mini/src/subpackage/tim/groupConversation/utils.ts
 * @Description: 会话窗口
 */

import { dispatch, store, actions } from '@/store/index'

/** 用户找活卡片跳转 */
export const toResumeDetail = (id, isSelf, params = {}) => {
  if (!id) {
    return
  }
  if (isSelf) {
    wx.$.r.push({ path: '/subpackage/resume/publish/index' })
  } else {
    dispatch(actions.resumeActions.setInfo({ uuid: id }))
    dispatch(actions.resumeActions.fetchGetDetailOther({ uuid: id })).then(() => {
      const { conversation } = store.getState().timmsg
      const { telRightsInfo, relatedJobId } = conversation || {}
      const { telType, infoId, infoType } = telRightsInfo || {}
      wx.$.r.push({
        path: '/subpackage/resume/detail/index',
        query: { sceneV2: '21', jobId: relatedJobId, resumeSubUuid: id, nearbyWorkerListApiSource: 'Im', type: 'groupConversation', telType, infoId, infoType },
        params,
      })
    })
  }
}

// 获取滚动id
export const getScrollLocId = (messageList, scrollLocId) => {
  const id = 'group_msg_scroll_view_btm'
  const len = messageList.length
  if (len > 0) {
    const nid = messageList[len - 1].id
    if (nid == scrollLocId) {
      return id
    }
    return nid
  }
  return id
}

/** 修改消息的状态
 * @param msgId 消息ID
 * @param status 状态
*/
export const setMsgStatus = (msgID, status) => {
  const messageList = [...(store.getState().timmsg.messageList || [])]
  if (messageList && messageList.length > 0) {
    const idx = messageList.findIndex(item => item.ID == msgID)
    if (idx >= 0) {
      const item = { ...messageList[idx] }
      item.status = status
      messageList[idx] = item
      dispatch(actions.timmsgActions.setState({ messageList }))
    }
  }
}

// 获取常用语列表
export const getGreetingList = async () => {
  const res = await wx.$.javafetch['POST/reach/v2/im/userGreeting/greetingList']()
  const { code, data } = res
  if (code == 0) {
    const { list = [] } = data || {}
    dispatch(actions.comwordsActions.setState({ greetingList: list }))
    return list
  }
  return []
}

/** 获取底部聊天功能高度 */
export function getChatBottomHeightOnly(callback?) {
  wx.createSelectorQuery()
    .select('#group_conv_btm_btn_bodv')
    .boundingClientRect((rect) => {
      // 使页面滚动到底部
      this.setData({ chatBtmHeight: rect?.height || 0 })
      callback && callback()
    })
    .exec()
}

/** 获取顶部聊天功能高度 */
export function getChatTopHeightOnly(callback?) {
  wx.createSelectorQuery()
    .select('#topbtn')
    .boundingClientRect((rect) => {
      // 使页面滚动到底部
      this.setData({ chatTopHeight: rect?.height || 0 })
      callback && callback()
    })
    .exec()
}

/**
 * 发起交换申请
 * @param exchangeType :EXCHANGE_TEL :交换电话 EXCHANGE_WECHAT :微信 EXCHANGE_RESUME_FILE :附件简历 ADDRESS_LOCATION :位置
 * */
export function applyExchange(exchangeType: 'EXCHANGE_TEL' | 'EXCHANGE_WECHAT' | 'EXCHANGE_RESUME_FILE' | 'ADDRESS_LOCATION' = 'EXCHANGE_TEL', extData = {}, callback: any = {}) {
  const { conversation } = store.getState().timmsg
  const { conversationId } = conversation || {}
  const { success, fail } = callback || {}
  wx.showLoading({ title: '请求中...' })
  wx.$.javafetch['POST/reach/v2/im/exchange/applyExchange']({ conversationId, exchangeType, extData }).then((res) => {
    wx.hideLoading()
    const { code, message } = res || {}
    if (code != 0) {
      wx.$.msg(message || '请求失败')
    }
    success && success()
  }).catch((err) => {
    wx.hideLoading()
    const { error, message } = err || {}
    let msg = '请求异常,请稍后重试'
    if (error && message) {
      msg = message
    }
    wx.$.msg(msg)
    fail && fail()
  })
}

interface HandleExchangeReq {
  exchangeMsgId?: string
  exchangeType?: 'EXCHANGE_TEL' | 'EXCHANGE_WECHAT' | 'EXCHANGE_RESUME_FILE' | 'ADDRESS_LOCATION' | 'SEARCH_CHAT_CARD_ATTACHMENT' | 'SEARCH_CHAT_CARD_TEL' | 'SEARCH_CHAT_CARD_WECHAT' | 'INPUT_BOX_POP_SEND_TEL'
  agree?: boolean
}

/**
 * 同意/拒绝交换邀请
 * @param exchangeMsgId
 * @param agree 同意/拒绝
 * @param exchangeType :EXCHANGE_TEL :交换电话 EXCHANGE_WECHAT :微信 EXCHANGE_RESUME_FILE :附件简历
 * */
export function handleExchange(
  param: HandleExchangeReq = {},
  extData = {},
  callback: any = {},
) {
  const { conversation } = store.getState().timmsg
  const { conversationId } = conversation || {}
  const { success, fail } = callback || {}
  const { exchangeType, exchangeMsgId, agree } = param || {}
  wx.showLoading({ title: '请求中...' })
  wx.$.javafetch['POST/reach/v2/im/exchange/handleExchange']({ exchangeMsgId, agree, conversationId, exchangeType, extData }).then((res) => {
    wx.hideLoading()
    const { code, message } = res || {}
    if (code != 0) {
      wx.$.msg(message || '请求失败')
    }
    success && success()
  }).catch((err) => {
    wx.hideLoading()
    const { error, message } = err || {}
    let msg = '请求异常,请稍后重试'
    if (error && message) {
      msg = message
    }
    wx.$.msg(msg)
    fail && fail()
  })
}

// 埋点上报
export const reportBuryingPoint = (msgInfo, ky, extParam = {}) => {
  const { payload } = msgInfo || {} as any
  const { data } = payload || {}
  const { content } = data || {}
  const { fromType } = content || {}
  const { conversation } = store.getState().timmsg
  const { fromUserImId, toUserImId, infoDetail } = conversation || {}
  const { infoType, infoId } = infoDetail || {}
  const report: any = { exposure_source: fromType ? `${fromType}` : 1, job_id: infoType == 1 ? `${infoId}` : '' }
  if (fromUserImId.indexOf('_2') >= 0) {
    report.worker_user_id = fromUserImId.replace('_2', '')
  } else {
    report.worker_user_id = toUserImId.replace('_2', '')
  }
  wx.$.collectEvent.event(ky, { ...report, ...extParam })
}

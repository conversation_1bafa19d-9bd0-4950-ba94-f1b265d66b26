<view class="body">
  <view class="header" id="header" style="padding-top:{{paddingTop}}px;" catch:touchmove="onDisableMove">
    <header-list-search bind:search="onSearch" searchPlaceholder="搜索您想要的牛人" custom-class="custom-class" />
  </view>
  <!-- 工种tab栏 -->
  <view class="filter-container" style="top: {{filterOffset}};">
    <position-tab id="position-tab" isNewLogic="{{isNewLogic}}" bind:change="onChangeFilter" />
  </view>
  <!-- 大列表 -->
  <view class="list">
    <view wx:if="{{!isNewLogic || resumeTabPosition.length}}" id="banner" class="banner-v">
      <banner wx:if="{{!selectPositionTabId.selectItem.jobId}}" id="onlyBanner" iconData="{{iconDiamond}}" bind:back="onBannerBack" bind:changeFilter="onRefreshAndTop" />
      <!-- 联系客服 -->
      <!-- #ifdef weapp || tt -->
      <!-- <join-group wx:if="{{login}}" id="w-join-group" custom-class="join-group" pageName="简历列表" sourceFrom="加群卡片-简历页" sectionId="{{1}}" params="{{joinGroupParams}}" /> -->
      <!-- #endif -->
      <!-- #ifdef swan -->
      <!-- <join-group wx:if="{{login}}" id="s-join-group" custom-class="join-group" pageName="简历列表" sectionId="{{1}}" isShowLink="{{true}}" /> -->
      <!-- #endif -->
      <!-- 跑马灯 -->
      <horse-race-lamp id="horseRaceLampBox" horseRLData="{{horseRLData}}" fromPage="resume" custom-class="custom-lamp-class" />
      <!-- 未登录引导浮窗 -->
      <view class="boxClass">
        <no-login-float-pop wx:if="{{!login && loginTextGroup.resume_list}}" formSource="list" text="{{loginTextGroup.resume_list}}" pointId="4" />
      </view>
    </view>
    <view id="resume-filter" wx:if="{{resumeTabPosition.length > 0 }}" class="container-header" style="top:{{navHeight}};" catch:touchmove="onDisableMove">
      <!-- 过滤框 -->
      <resume-filter wx:if="{{selectPositionTabId.selectItem.checkStatus == 2}}" bind:ready="getSelectHeight" fixed="{{fixedHeader}}" id="filter" bind:togglepicker="onTogglePicker" bind:showClassFilter="onShowClassFilter" bind:refresh="onRefreshAndTop" isMoveToTop="{{true}}" sourcePageName="简历列表" pointSource="3" selectTit="我选择的工种" />
      <job-tip wx:if="{{selectPositionTabId.selectItem.checkStatus != 2}}" type="{{jobTipType}}" bind:onJumpJob="onJumpJob" />
    </view>
    <view style="position: relative;">
      <skeleton wx:if="{{!firstLoaded && (!isNewLogic || resumeTabPosition.length)}}" />
    </view>
    <empty-box wx:if="{{firstLoaded && isSupply}}" img-class="tj-data-img" title-class="tj-title" text-class="tj-text" title="暂无匹配信息，更换筛选项试一试吧" imgType='search' text='- 为您推荐其他优秀牛人 -' />
    <!-- 未发布职位列表一 -->
    <block wx:if="{{isNewLogic && !resumeTabPosition.length}}">
      <view class="empty-box">
        <image src="https://cdn.yupaowang.com/yupao_mini/published-no-data2.png" class="empty-box-img" />
        <view class="empty-box-text">发布职位后才会有牛人对你感兴趣</view>
        <view class="empty-box-text">快去发布职位吧！</view>
        <m-button bind:tap="onJumpFastIssue" custom-class="empty-btn">发布职位</m-button>
      </view>
    </block>
    <block wx:else>
      <!-- 列表展示 包含广告和内容 -->
      <card-list topHeight="{{navHeight}}" relatedInfoId="{{cpJobId}}" firstLoaded="{{firstLoaded}}" id="card-list" bind:checkNoData="onCheckNoData" bind:aggregation="aggregation" bind:loadFinished="setFirstLoad" bind:localchange="onRefreshAndTop" bind:ischat="onIsChat" bind:listchange="onListChange" bind:selectpostionback="onSelectPostionBack" origin="resumeIndex" bind:onSearch="onSearch" />
    </block>
  </view>
  <!-- #ifdef weapp -->
  <view style="height: 98rpx; padding-bottom: constant(safe-area-inset-bottom); padding-bottom: env(safe-area-inset-bottom); box-sizing: content-box"></view>
  <!-- #endif -->
</view>
<!-- 浮标 -->
<buoy-of-page buoyTop="{{buoyTop}}" buoyUnder="{{buoyUnder}}" />
<delay-render>
  <!-- 成都投放专项活动弹窗 -->
  <marketing data-visible="marketingVisible" currDialog="{{currDialog}}" visible="{{marketingVisible}}" bind:close="onCloseAggregation" />
  <!-- 发布职位 -->
  <view wx:if="{{isShowPublishButton && (!isNewLogic || resumeTabPosition.length)}}" class="fast-tips-box">
    <view class="fast-tips" bind:tap="onJumpFastIssue" />
  </view>
  <!-- 智能推荐-引导发布招工（选择地址和工种） 弹窗 流程1 -->
  <pop-recommend-pulish-work data-visible="showRecommendPop" visible="{{showRecommendPop}}" bind:close="onCloseAggregation" currDialog="{{currDialog}}"></pop-recommend-pulish-work>
  <!-- 发布招工弹窗 -->
  <pop-publish-work data-visible="showPublishWorkPop" pageName="找活列表" visible="{{showPublishWorkPop}}" bind:close="onCloseAggregation" currDialog="{{currDialog}}"></pop-publish-work>
  <!-- 继续招工 弹窗 -->
  <pop-continue data-visible="showContinuePop" visible="{{showContinuePop}}" bind:close="onCloseAggregation" currDialog="{{currDialog}}"></pop-continue>
  <!-- 更换职位弹框 -->
  <select-position list="{{cpList}}" dialogIdentify="{{cpDialogIdentify}}" source="1" value="{{cpJobId}}" visible="{{cpVisible}}" catch:close="onCpClose" bind:nosend="onCpNoSend" catch:confirm="onCpConfirm" />
  <!-- 回到顶部 -->
  <back-top />
</delay-render>
<!-- #ifdef swan || tt -->
<custom-tab-bar />
<!-- #endif -->
import { getResumeDetails } from '@/utils/helper/resume/index'
import { APIJavaService } from '@/utils/request/index'

import { dataCleanOfQualityMaster } from './data-clean'
import { BPOfQualityWorker } from './burying-point'

/**
 * @name 获取优质师傅ids数组
 * @param talentIds 人才 resumeSubUuid ids 列表
 * @param params 请求参数
 * @param isLogin 登录情况
 */
export async function getRecommendResumeSubUuidService(isRefresh = false) {
  const { params, isLogin, resumeSubUuids } = this.data

  try {
    if (!isRefresh && resumeSubUuids.length) {
      return
    }
    const { provinceId, cityId, classifyIds } = params
    const occupationIdList = String(classifyIds || '').split(',').filter(Boolean)
    const { data } = await APIJavaService('POST/resume/v1/recommend/highQualityList', { specialArea: [1], provinceId, cityId, occupationIdList })
    let subUuids = data?.resumeSubUuidList || []
    if (isLogin) {
      subUuids = await filterSelfResumeSubUuid(subUuids)
    }

    this.setData({ resumeSubUuids: subUuids })
  } catch (error) {
    console.error(error)
    this.setData({ resumeSubUuids: [] })
  }

  async function filterSelfResumeSubUuid(subUuids: any[]) {
    try {
      const { subListResp } = await getResumeDetails()
      const selfSubUuids = subListResp.map((i) => i.uuid || '').filter(Boolean)
      return subUuids.filter((subUuid) => !selfSubUuids.some((id) => id === subUuid))
    } catch (error) {
      return subUuids
    }
  }
}

/** @name 根据获取的ids获取优质师傅推荐列表 */
export async function getWorkerInfoService(isRefresh = false) {
  const { resumeSubUuids, workerInfoArr, miniQualityWorkerEnterId, swiperIdx } = this.data
  if (resumeSubUuids.length === 0) {
    this.setData({ workerInfoArr: [] })
    return
  }

  const swiperIdxWorkerInfo = workerInfoArr[swiperIdx]
  if (!isRefresh && (swiperIdxWorkerInfo?.freeCallNum === 0 || swiperIdxWorkerInfo?.hide)) {
    this.recommendTimer && clearTimeout(this.recommendTimer)
    this.recommendTimer = setTimeout(() => BPOfQualityWorker('qualityWorkersExposure', swiperIdxWorkerInfo, { enter_id: miniQualityWorkerEnterId }), 2000)
    this.setData({ swiperIdxWorkerInfo })
    return
  }

  const resumeSubUuid = resumeSubUuids[swiperIdx]
  if (!resumeSubUuid) {
    return
  }

  this.setWorkerInfoArrTimer && clearTimeout(this.setWorkerInfoArrTimer)
  this.setWorkerInfoArrTimer = setTimeout(async () => {
    const workerInfo = await getWorkerInfoApi(resumeSubUuid, swiperIdx)
    this.recommendTimer && clearTimeout(this.recommendTimer)
    this.recommendTimer = setTimeout(() => BPOfQualityWorker('qualityWorkersExposure', workerInfo, { enter_id: miniQualityWorkerEnterId }), 2000)
    workerInfoArr[swiperIdx] = workerInfo
    this.setData({ workerInfoArr, swiperIdxWorkerInfo: workerInfo })
  }, 500)

  async function getWorkerInfoApi(resumeSubUuid: string, swiperIdx: number) {
    try {
      const { data } = await APIJavaService('POST/resume/v1/recommend/detail', { resumeSubUuid })
      return { ...dataCleanOfQualityMaster(data), item_idx: swiperIdx + 1 }
    } catch (error) {
      console.error(error)
      return { hide: true }
    }
  }
}

export async function getGlobalDefaultConfigApi() {
  try {
    const res = await APIJavaService('POST/resume/v1/resumeBasic/globalDefaultConfig', {})
    return res.data.goodWorkers || {}
  } catch (err) {
    console.error(err)
    return {}
  }
}

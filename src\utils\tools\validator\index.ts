/*
 * @Date: 2021-12-15 11:31:46
 * @Description: 校验、正则相关工具，所有函数名均以is开头
 */

import { getSystemInfoSync } from '@/utils/tools/common/index'

/** 判断手机机型，根据机型做一些性能优化 true 高端机，false 低端机 */
export const phoneType = (): boolean => {
  const result = getSystemInfoSync()
  // 如果是ios就认为是高端机
  if (/^iOS/.test(result.system)) {
    return true
  }
  // 安卓需要区分安卓系统版本
  if (result.platform === 'android') {
    const [, version] = result.system.split(' ')
    // 如果小于安卓7的就默认为低端机
    if (Number(version) <= 7) {
      return false
    }
    return true
  }
  return true
}

/**
 * @name: isPhone for jsxin
 * @params: tel: string 当前需要验证的手机号
 * @return: boolean
 * @description: 验证传入的手机号是否为真
 */
export const isPhone = (tel: string | undefined): boolean => {
  if (!tel) {
    return false
  }
  const reg = /^1[3456789][0-9]{9}$/
  return reg.test(tel)
}

/**
 * @params: null
 * @return: boolean
 * @description:  判断当前机型是否是ios设备
 */
export const isIos = (() => {
  let platform
  return (): boolean => {
    if (!platform) {
      platform = getSystemInfoSync().platform
    }
    return platform.toLowerCase() === 'ios'
  }
})()

/**
 * @name: randIntNumber for jsxin
 * @params: min: number 最小区间(包括)
 * @params: max: number 最大区间(不包括)
 * @return: number 生成的随机数
 * @description: 生成一个在 [min-max) 该区间的整数
 */
export const randIntNumber = (min = 0, max = 20): number => {
  return Math.floor(Math.random() * (max - min)) + min
}

/**
 * @name: isRequire for jsxin
 * @params: val: string 传入需要被验证字符串
 * @params: min: 最小必须达到多少字
 * @param: max:最多不超过多少字 0为不验证最大字数
 * @return: boolean
 * @description: 验证内容 是否必须有汉字 且不少于 min 字 不大于max字
 */
export const isVaildVal = (value: string, min = 15, max = 0): boolean => {
  const reg = /[\u4E00-\u9FFF]+/
  let val = `${value}`
  if (val?.length > 1000) {
    val = value.substring(0, 1000)
  }
  return max ? reg.test(val) && (val.length >= min) && (val.length <= max) : reg.test(val) && (val.length >= min)
}

/**
 * @return: boolean
 * @description: 验证内容 传入的val数字是否在指定的区间内，包含最大最小区间
 */
export const isVaildNum = (val: number | string, min: number, max: number): boolean => {
  // 如果不是数字
  if (Number.isNaN(Number(val))) {
    return false
  }
  return val >= min && val <= max
}

/**
 * @name: allChinese for jsxin
 * @params: str: string 需要被验证的字符串
 * @return: boolean
 * @description: 当前字符串是否是2-5个全中文
 */
export const allChinese = (str: string): boolean => {
  // eslint-disable-next-line prefer-regex-literals
  const reg = new RegExp('^[\u4E00-\u9FA5]{2,5}$')
  return reg.test(str)
}

/**
 * @name: validPassWord for jsxin
 * @params: str: string 需要被验证的密码
 * @return: boolean
 * @description: 检测当前密码是否符合规范
 */
export const validPassWord = (str: string): boolean => {
  // eslint-disable-next-line no-useless-escape
  const reg = /([a-zA-Z0-9_\.@#$%^&*]){6,20}/
  return reg.test(str)
}

/** 正则匹配手机号 */
export function matchContentPhone(content: string) {
  const regexp = /1[3-9]\d{9}/g
  const phone = typeof content == 'string' && content.match(regexp)
  if (Array.isArray(phone)) {
    return phone[0]
  }
  return ''
}

/** 正则邮箱 */
export function isEmail(email) {
  const reg = /^[a-zA-Z0-9][a-zA-Z0-9._-]*@[a-zA-Z0-9][a-zA-Z0-9-]*\.[a-zA-Z]{2,}$/
  return reg.test(email)
}

/** 微信号验证 6～10位字符长度 */
export function isWeChat(weChat) {
  // const reg = /^[a-zA-Z_][-_a-zA-Z0-9]{5,19}$/
  const reg = /^[-_a-zA-Z0-9]{6,20}$/
  return reg.test(weChat)
}

/** 判断是否是数组并且返回数组
 * @param arr数组
 * @param length数组长度最少长度,默认至少1条
 */
export function getArrayVal(arr, len = 1) {
  if (Array.isArray(arr) && arr.length >= len) {
    return arr
  }
  return []
}

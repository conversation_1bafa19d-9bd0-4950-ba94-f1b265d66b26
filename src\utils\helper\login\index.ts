/*
 * @Date: 2022-01-07 18:54:35
 * @Description:
 */

import { dispatch, actions, store, storage, messageQueue } from '@/store/index'
import { SERVER_PHONE } from '@/config/app'
import { resume } from '@/utils/helper/index'
import { dealDialogByApi } from '@/utils/helper/dialog/index'
import { getBaseConfig } from '../common/index'
import { reportSnByScene } from '@/utils/taskSn'
import { fetchDeviceValid, fetchLoginUserInfo, fetchSnsCode2Session, fetchTicketLogin } from './utils'
import { fetchResumeExist } from '../resume/index'
import { getResumeProcessPath } from '../resume/utils'
import miniConfig from '@/miniConfig/index'
import { toLogin } from '../common/toLogin'
import { getIsNewLogic } from '@/pages/resume/utils'

/**
 * 用户标识获取上报
 * @param isUpload true 自动上报 false 仅获取标识
 * @returns
 */
export const getWechatAuthInfo = (isUpload?: boolean) => {
  return new Promise((resolve, reject) => {
    if (ENV_IS_WEAPP) {
      wx.login({
        success: async (data) => {
          if (!data || !data.code) {
            reject()
            dispatch(actions.userActions.setState({ silentLoginIng: false }))
            return
          }
          const res = await fetchSnsCode2Session({ jsCode: data.code })
          if (res.code != 0) {
            dispatch(actions.userActions.setState({ silentLoginIng: false }))
            return
          }
          // 根据code获取用户信息存入本地
          dispatch(actions.storageActions.setItem({ key: 'loginAuthData', value: res.data }))
          resolve(res.data)
          const userState = await storage.getItem('userState')
          /* 510老用户默认登录，一天节约90块 */
          if (userState.login && userState.token) {
            dispatch(actions.userActions.setState({ silentLoginIng: false }))
            return
          }
          const pages = getCurrentPages()
          const perpage = pages[pages.length - 1]
          const loginPages = [
            'pages/index/index',
            'subpackage/recruit/details/index',
            'subpackage/recruit/listSearchResultsPage/index',
            // 加群分流引导落地页-鱼泡快招
            'subpackage/member/group/wecom_group/index',
            /** 存在页面先获取登录数据，后登录 或登录数据未获取到的情况 */
            // 发布流程2
            'subpackage/recruit/fast_issue/fast_pulish_second/index',
            // 发布流程1-
            'subpackage/recruit/fast_issue/index/index',
            'pages/resume/index',
            'subpackage/resume/detail/index',
            'subpackage/recruit/quick_trick/index/index',
            'subpackage/share-activity/receive-red-envelope/index',
            'subpackage/share-activity/inviter-red-envelope/index',
            'subpackage/member/group/wecom_group/index',
            'subpackage/attachment-resume/upload/index',
          ]
          if (perpage?.route === 'subpackage/userauth/auth/index') {
            if (perpage.options?.scene) {
              const sceneParams: any = decodeURIComponent(perpage.options.scene).split('&').reduce((acc, pair) => {
                const [key, value] = pair.split('=')
                acc[key] = value
                return acc
              }, {})

              if (sceneParams?.sunCode) {
                console.log('------------------web授权登录页面开启静默登录----------------------')
                loginPages.push('subpackage/userauth/auth/index')
              }
            }
            if (perpage.options?.sunCode) {
              console.log('------------------web授权登录页面开启静默登录----------------------')
              loginPages.push('subpackage/userauth/auth/index')
            }
          }
          if (ENV_DEVELOPMENT !== 'PRO' && wx.getStorageSync('DEV_STATIC_LOGIN') === false) {
            /** 非正式站环境, 并关闭了自动登录，那么跳过静默登录 */
            perpage.quietlyLoginEnd?.()
            console.log('--------------------非静默登录----------------------')
            dispatch(actions.userActions.setState({ silentLoginIng: false }))
            return
          }

          if (perpage && loginPages.includes(perpage.route) && !userState.login) {
            console.log('--------------------静默登录----------------------', perpage.route)
            const loginRes = await fetchTicketLogin() // 获取token
            if (loginRes && loginRes.code == 0 && Object.keys(loginRes.data).length) {
              const { token } = loginRes.data
              const initRes = await fetchLoginUserInfo({
                token,
              })
              if (initRes.code == 0) {
                const initResData = initRes.data
                initResData.token = token
                const newMember = initResData.newMember ? 1 : 0
                const user = {
                  userId: Number(initResData.userId),
                  token: initResData.token,
                  uuid: initResData.uuid,
                  login: true,
                  tel: initResData.tel,
                  /** 当前瀛湖角色 */
                  role: initResData.role.nowRole,
                  /** B/C分端后第一次选择用户的角色 */
                  firstRole: initResData.role.nowRole,
                  type: '',
                  newMember,
                  header_image: initResData.headPortrait,
                  headPortrait: initResData.headPortrait,
                  tokenTime: new Date().getTime(),
                  lastLoginTime: new Date().getTime(),
                }
                await afterLogin(user, true)
                console.log('------------------静默登录成功----------------------')
                dispatch(actions.userActions.setState({ silentLoginIng: false })) // 静默登录成功
                await messageQueue((state) => state.storage.userState.userId)
                const curPages = getCurrentPages()
                const curPerpage = curPages[curPages.length - 1]
                curPerpage.getResList && curPerpage.getResList()
                curPerpage.pageRefresh && curPerpage.pageRefresh()
                curPerpage.onUpdateTabData && curPerpage.onUpdateTabData()
                curPerpage.loginStatus === false && (curPerpage.loginStatus = true)
                curPerpage.initData && curPerpage.initData()
                curPerpage.rqRefreshActivityEntry && curPerpage.rqRefreshActivityEntry()
                // 更新列表发布招工按钮文案
                curPerpage.setData && curPerpage.setData({ fastEntryRefresh: true })
                // 触发 被邀请人分享红包页面的接口刷新
                curPerpage.initRqInviteeData && curPerpage.initRqInviteeData(true)
                // 自动登录后 刷新简历详情的接口
                curPerpage.commonRefreshWorkerResumeDetails && curPerpage.commonRefreshWorkerResumeDetails()
                await wx.$.l.fetchTenCentData()
                await saveAccount()
              } else {
                handlerLoginErr(loginRes, token)
                dispatch(actions.userActions.setState({ silentLoginIng: false }))
              }
            } else {
              dispatch(actions.userActions.setState({ silentLoginIng: false }))
            }
          } else {
            // 非静默登录直接结束
            dispatch(actions.userActions.setState({ silentLoginIng: false }))
          }
          /**
           * 未登录情况
           * 执行 静默登录后，不管是否静默登录成功，都会回调页面上的具体方法
           */
          perpage.quietlyLoginEnd?.()
        },
        fail: (err) => {
          wx.$.collectEvent.event('errorPosition', { name: 'EP-wxLogin', ePLogin: JSON.stringify(err) })
          dispatch(actions.userActions.setState({ silentLoginIng: false }))
        },
      })
    } else {
      dispatch(actions.userActions.setState({ silentLoginIng: false }))
    }
  })
}

/**
 * @description 获取登录流程
 * @param stgyIds 实验组id
 * @returns 是否命中传入到实验组
*/
export const getLoginAb = async (stgyIds: string[]) => {
  if (miniConfig.token === 'gdh') {
    return false
  }
  await messageQueue(state => state.storage.loginAuthData.unionid, true).catch(() => { })
  const { unionid } = storage.getItemSync('loginAuthData') || {}
  const { loginAb } = store.getState().testAbUi
  if (!unionid) {
    return false
  }
  if (!wx.$.u.isEmptyObject(loginAb)) {
    return stgyIds.includes(loginAb.strategyId)
  }
  const { data } = await wx.$.javafetch['POST/ab/v1/ui/get']({ distinctId: unionid, testId: 'Mini_RegLog_Group' }).catch(err => err || {})
  if (data && data.join == 1) {
    dispatch(actions.testAbUiActions.setState({ loginAb: data }))
    return stgyIds.includes(data.strategyId)
  }
  return false
}

/** 设置用户信息 */
export const setUserInfo = async () => {
  const { storage } = store.getState()
  if (storage.userState.login) {
    await dispatch(actions.userActions.fetchUserInfo(storage.loginAuthData))
  }
}

/** @name 授权后跳转判断 */
export async function jumpTo() {
  // 判断用户从哪里进来的，处理YP - 14433需求优化登录后页面跳转逻辑
  // const path = wx.getStorageSync('frompushresume401')
  // todo 判断frompushresume401 逻辑
  if (toLogin.callResolve() === false) {
    loginBack()
  }
}

/** @name 授权登录后返回 */
export function loginBack() {
  const { redirect } = wx.$.r.getCurrentPage().options
  // 如果有带重定向参数，就重定向到指定页面
  if (redirect) {
    const url = decodeURIComponent(redirect)
    wx.redirectTo({ url })
    return
  }
  if (ENV_IS_WEAPP || ENV_IS_TT) {
    wx.$.r.back()
  }
  if (ENV_IS_SWAN) {
    const pages = getCurrentPages()
    // 路由栈大于2时为密码登录等，登录之后需要回退两个层级
    if (pages.length > 2) {
      const prePage = pages[pages.length - 2]
      if (prePage.route === 'subpackage/userauth/auth/index') {
        wx.$.r.back(2)
      } else {
        wx.$.r.back()
      }
      // 快捷登录页面，登录后返回
    } else if (pages.length == 2) {
      const prePage = pages[pages.length - 1]
      // 若两级路由栈，第一级为登录授权页，登录之后则默认跳转首页
      if (prePage.route === 'subpackage/userauth/auth/index') {
        wx.$.r.push({
          path: '/pages/index/index',
        })
      } else {
        wx.$.r.back()
      }
    } else {
      const prePage = pages[pages.length - 1]
      // 如果默认启动页在登录，则跳转首页(判断做兼容)
      if (prePage.route === 'subpackage/userauth/auth/index' || prePage.route === 'subpackage/userauth/tel/index') {
        wx.$.r.push({
          path: '/pages/index/index',
        })
      }
    }
  }
}

/**
 * @description 注销账号的弹窗
 * @param data 传入数据
 * @param action 恢复账号成功回调
 * @param cancelAction 取消恢复账号回调
 */
export const logOutModel = (data, action, cancelAction?) => {
  wx.$.confirm({
    content: data.head.msg,
    cancelText: '取消',
    confirmText: '恢复账号',
  }).then(() => {
    // 恢复账号，并且得到用户信息请求
    dealRecoveryCount(data?.data?.singletoken).then((nRes) => {
      typeof action == 'function' && action(nRes, nRes && nRes.data && nRes.data.new_member)
    })
  }).catch(() => {
    if (cancelAction && typeof cancelAction == 'function') {
      cancelAction()
    }
  })
}

/** 更新UserState数据，返回res和userState */
export const updateUserState = async (token, type = '') => {
  const tokenTime = new Date().getTime()
  const res = await fetchLoginUserInfo({ token })
  let user: any
  if (res.code == 0) {
    const loginData = res.data
    user = {
      userId: Number(loginData.userId),
      token,
      tokenTime,
      uuid: loginData.uuid,
      login: true,
      tel: loginData.tel,
      /** 当前用户角色 */
      role: loginData.role.nowRole,
      /** B/C分端后第一次选择用户的角色 */
      firstRole: loginData.role.nowRole,
      /** 登录方式  sm:3为手机号登录, 否则为手机号登录 */
      type,
      newMember: 0,
      header_image: loginData.headPortrait,
      headPortrait: loginData.headPortrait,
      lastLoginTime: new Date().getTime(),
    }
    saveAccount(user)
  } else {
    wx.$.msg(res.message || '登录失败，请重新登录')
  }
  return { res, user }
}

/** @name 恢复账号后进行的操作 */
const dealRecoveryCount = async (token) => {
  wx.$.loading('加载中...')
  await wx.$.l.timLogout()
  dispatch(actions.messageActions.setState({ imlogin: false, isSyncCompleted: false }))
  dispatch(actions.messageActions.clearCoverData())
  const data = await wx.$.javafetch['POST/account/v1/user/close/unfreeze']({ tenantKey: 'YPZP' }, { headers: { token } })
  if (data.code == 0) {
    const { res, user } = await updateUserState(token)
    // const res = await fetchLoginUserInfo({ token })
    if (res.code == 0) {
      // const loginData = res.data
      await afterLogin(user)
      if (wx.$.tim) {
        // im重登
        await wx.$.l.timLogoutAndReLogin()
      } else {
        await wx.$.l.timLogin()
      }
      return { ...res, isLoginSuccess: true, userState: user }
    }
    wx.$.msg(res.message || '登录失败，请重新登录')

    return { ...res, isLoginSuccess: false }
  }
  wx.$.msg(data.message)
  return false
}

/** @name 百度鱼泡登录验证账号获取账号信息方法 */
export const detailUserLoginInfo = async (res, suc, fail) => {
  const { storage } = store.getState()
  wx.hideLoading()
  if (res && res.head && res.head.code == 200) {
    wx.$.fetch['GET/job/member/init'](
      {
        new_member: res.data.new_member,
        origin: res.data.origin,
        refid: storage.sourceCode,
        share_source: storage.source_share,
      },
      {
        headers: { singletoken: res?.data?.token },
        hideErrCodes: ['member_shielding', 'freeze'],
      },
    )
      .then((resp) => {
        dispatch(actions.userActions.setState({ isRefeshData: { status: true, id: 0 } }))
        suc && suc(resp, res.data.new_member)
      })
      .catch(async (resp) => {
        if (resp?.head?.code == 500 && resp.data?.errcode == 'member_shielding') {
          // 拉黑用户弹窗
          wx.$.confirm({
            content: resp.head.msg,
            cancelText: '知道了',
            confirmText: '联系客服',
          }).then(() => {
            wx.$.u.callPhone(SERVER_PHONE)
          })
        } else if (resp?.head?.code === 500 && resp.data?.errcode === 'freeze') {
          // 注销账号弹窗
          logOutModel(resp, (result, new_member) => suc && suc(result, new_member))
        } else {
          fail && fail(res.head.msg)
        }
      })
  }
}

const dItem = {
  cityId: 0,
  cityName: '',
  countyId: 0,
  countyName: '',
  jobId: 0,
  occList: [],
  provinceId: 0,
  provinceName: '',
  recruitType: 0,
  salary: '',
  title: '推荐',
  nName: '推荐',
}

/** @name 登录之后需要进行的操作
 * user: 登录之后的用户信息
 * silentLogin：是否是静默登录
 */
// 处理登录之后逻辑-更新和用户相关的数据
export const afterLogin = async (user, silentLogin = false) => {
  // 登录后清空简历职位信息
  dispatch(actions.storageActions.setItem({ key: 'resumeTabPosition', value: [] }))
  dispatch(actions.storageActions.setItem({ key: 'selectPositionTabId', value: {} }))
  dispatch(actions.storageActions.setItem({ key: 'searchPageSltedJob', value: {} }))
  dispatch(actions.storageActions.setItem({ key: 'searchHistory', value: {} }))
  // 清楚与上一个账号绑定的 兼职金刚区落地页的 筛选项缓存model
  dispatch(actions.recruitIndexActions.setState({ activeZoneSort: { label: '', value: '', userId: '' } }))

  // 登录之后的角色
  const userRole = user.role
  // 设置用户角色
  const role = store.getState().storage.userChooseRole
  storage.setItemSync('taskCompleteQueryTimes', {
    'new:collectMini': 0,
    'new:addMiniToDesktop': 0,
    'new:addMiniToMyMini': 0,
  })
  let nUser = user
  // 优先使用本地角色
  if (role && role != userRole) {
    wx.$.javafetch['POST/account/v1/role/changeRole']({ role }, { hideMsg: true })
    nUser = { ...user, role }
  }
  // 删除发布简历缓存
  storage.removeSync('pubishData')
  // 首页更新tab
  dispatch(actions.recruitIndexActions.setState({ classifyTabIdInit: true }))
  // 存储用户信息
  await dispatch(actions.storageActions.setItem({ key: 'userState', value: nUser }))
  nUser.userId
    && wx.$.collectEvent.config({
      user_unique_id: nUser.userId,
    })
  // 登录埋点
  const refId = storage.getItemSync('sourceCode')
  const track_seed = storage.getItemSync('track_seed_share')
  const invitation_event_name = store.getState().other.sighInRedPackTitle
  wx.$.collectEvent.event('signIn', {
    sign: user.newMember == 1 ? '新用户' : '老用户',
    sharer_id: refId || '-99999',
    track_seed,
    Inviter_user_id: refId || '-99999',
    invitation_event_name,
  })
  if (wx.canIUse('getLaunchOptionsSync')) {
    const options = wx.getLaunchOptionsSync()
    reportSnByScene(options)
  }
  // 保存用户信息
  setUserInfo()
  // 登录成功获取工种数据
  wx.$.l.getClassTreeData(true)
  // 获取找活名片相关的数据
  resume.getResumeDetails(true)

  // 更新是否有找活名片的数据
  // resume.fetchResumeExist()
  // 清除用户找活通用配置模板
  dispatch(actions.resumeActions.setState({ resumeTemplates: [] }))
  // 重新登录后，删除积分分享和口碑分享弹框的缓存
  storage.removeSync('integralShareType')
  // 重新登录后,删除首页引导完善的缓存
  storage.removeSync('recruitlabel')
  storage.removeSync('recruitscore')
  storage.removeSync('turntableTipsNum')
  storage.removeSync('turntableTipsShow')
  storage.removeSync('indexGetDialog')
  storage.removeSync('isRequestGuideToPublish')
  storage.removeSync('isEnterPublish')
  storage.removeSync('offlineMessageAvatar')
  storage.setItemSync('classifyRecommendTime', 0)
  storage.setItemSync('partclassifyRecommendTime', 0)
  storage.setItemSync('recRecommendclassifyRecommendTime', 0)
  storage.setItemSync('resRecommendclassifyRecommendTime', 0)
  storage.setItemSync('classifyTabClassify', [])// 登陆后或切换账号后清楚职位列表工种tab缓存
  dispatch(actions.storageActions.setCommonItem({ luckywheelpop: { time: 0, num: 0 } }))// 初始化大转盘返回按钮弹框缓存
  dispatch(actions.storageActions.setCommonItem({ guidingTipsNum: 0 }))// 会话详情引导提示语次数改为0
  getIsNewLogic()
  // 登录后刷新首页信息流数据
  dispatch(actions.recruitIndexActions.setState({ _isUpdateInfoFlow: true }))
  // 登录后更新tab红点数据
  dispatch(actions.messageActions.fetchTabbarMyMessageNumber())
  // 登陆后清除changeToolsPage（简历筛选项）
  dispatch(actions.storageActions.setItem({
    key: 'changeToolsPage', value: {},
  }))
  getBaseConfig({ isUpdate: true })
  // 更新会话配置信息
  dispatch(actions.messageActions.getGlobalSwitch())
  // 极验-设备验，提交gee_token
  !silentLogin && fetchDeviceValid()
  return mustRealName()
}

/** 强制实名 */
export async function mustRealName() {
  const res = await wx.$.javafetch['POST/account/v1/user/change/realName/mandatory/realNameVerify']()
  if (res.data.verifyResult) {
    wx.$.r.reLaunch({ path: '/subpackage/member/unbindRealname/mandatory_certification/index', query: { unbindType: 1 } })
  }
  return res.data.verifyResult
}

/** 存储账号登录信息，用于切换账号使用 */
export const saveAccount = (user?) => {
  const userState = user || store.getState().storage.userState
  const avatar = wx.$.u.getObjVal(store.getState().user.userInfo, 'userBaseObj.userHeadPortraitObj.headPortrait')
  let loginAccountList = wx.$.u.deepClone(storage.getItemSync('loginAccountList'))
  // 如果当前缓存的账号信息列表为空，将当前登录的账号信息缓存做第一个
  if (loginAccountList.length === 0 && userState?.login && !user) {
    const currAccount = { ...userState, avatar, lastLogintime: new Date().getTime() }
    loginAccountList = [currAccount]
  }

  if (userState && userState.token) {
    const index = loginAccountList.findIndex(item => item.userId === userState.userId)
    if (index >= 0) {
      // 如果重复登录当前账号，刷新一下当前账号缓存信息
      loginAccountList.splice(index, 1)
    } else if (loginAccountList.length === 3) {
      // 已经有3个账号则替换最近登录时间最久远且非当前登录的账号
      loginAccountList.sort((a, b) => a.lastLogintime - b.lastLogintime).shift()
    }
    loginAccountList.push(userState)
  }

  dispatch(actions.storageActions.setItem({ key: 'loginAccountList', value: loginAccountList }))
}

/**
 * 引导登录弹窗逻辑处理
 * @param key 缓存的位置
 * @param currPage 当前弹出pageCode
 * @param page 页码
 * @param id 信息id
 */
export const dealGuideLogin = async (key: string, pageCode: string, page?: number, id?: string) => {
  const { userState } = store.getState().storage
  const guideLoginTotal = wx.$.u.deepClone(storage.getItemSync('guideLoginTotal'))
  // eslint-disable-next-line prefer-const
  let { count, show, ids } = guideLoginTotal?.[key] || {}
  // 登录了或者弹出过
  if (userState.login || show) {
    return
  }
  const guideLoginConfig = await dispatch(actions.configActions.getLoginConfig())
  const loginConfig = guideLoginConfig.configDTOList.find((item) => item.bizIdentifier === key) || {}
  const showModal = async function () {
    // 弹窗逻辑处理
    const popup = await dealDialogByApi(loginConfig.popIdentifier, pageCode)
    if (popup) {
      wx.$.showModal({
        ...popup,
        pageCode,
      })
    }
  }
  // 详情
  if (id && count < loginConfig.times && !ids.includes(id)) {
    count += 1
    ids.push(id)
  }
  // 列表用页码判断是否弹出，详情用进入次数判断是否弹出
  if (loginConfig.times && (page === loginConfig.times || count === loginConfig.times)) {
    show = true
    await showModal()
  }
  storage.setItemSync('guideLoginTotal', { ...guideLoginTotal, ...{ [key]: { count, show, ids } } })
}

/** 登录返回的res异常处理 */
export const handlerLoginErr = async (res, token) => {
  // res.code = 10120003
  if (res.code == 10120003) {
    // 快捷登录失败
    return wx.$.alert({
      title: '快捷登录失败',
      content: res.message || '手机号已绑定其他鱼泡账号,请使用验证码登录!',
      confirmText: '前往登录',
    }).then(() => {
      const currentPage = wx.$.r.getCurrentPage()
      if (currentPage.route === 'subpackage/recruit/fast_issue/index/index') {
        wx.$.r.push({ path: '/subpackage/userauth/auth/index?auth_type=2&fromPage=fastIssue' })
        return {}
      }
      /** showTel: 代表是否显示手机号码登录 */
      return { ...res, showTel: true }
    }).catch(() => {
      return false
    })
  }
  if (res.code == 10110009) {
    // 拉黑用户弹窗
    wx.$.confirm({
      content: res.message || '登录的用户已被拉黑',
      cancelText: '知道了',
      confirmText: '联系客服',
    }).then(() => {
      wx.$.u.callPhone(SERVER_PHONE)
    })
    return false
  }
  if (res.code == 10160001) {
    // 拉黑用户弹窗
    return wx.$.confirm({
      content: res.message,
      cancelText: '取消',
      confirmText: '恢复账号',
    }).then(() => {
      // 恢复账号，并且得到用户信息请求
      return dealRecoveryCount(token)
    }).catch(() => {
      return false
    })
  }
  if (res.code != 0) { // 默认的提示
    wx.$.msg(res.message || '登录失败，请重新登录')
  }
  return res
}

/** 选择角色-获取招工、找活发布状态 */
export const getPublishStatus = async (role: number, isNewResume = false) => {
  /** 职位列表 */
  const recruitList = '/pages/index/index'
  /** 管理职位 */
  const manageRecruit = '/subpackage/recruit/published/index'
  let path = role === 1 ? manageRecruit : recruitList
  try {
    // B角色
    if (role === 1) {
      const jobRes = await wx.$.javafetch['POST/job/v2/manage/job/publish/check']({}, { hideMsg: true })
      // 终止简历完善流程
      wx.$.javafetch['POST/resume/v3/prepub/terminate']({}, { hideMsg: true }).catch(() => { })
      const data = wx.$.u.getObjVal(jobRes, 'data') || {}
      wx.$.r.reLaunch({ path: (data.published || data.draftedCount > 0) ? manageRecruit : '/subpackage/recruit/fast_issue/index/index?isFromRolePop=true' })
      return false
    }
    // C角色- 获取简历完善流程配置
    const resumeProcess = await dispatch(actions.resumeActions.getResumeConfig(true))
    // 下一步流程
    if (resumeProcess && resumeProcess.item) {
      path = getResumeProcessPath(resumeProcess.item, resumeProcess.module)
      // 是否进入了简历完善流程
      await dispatch(actions.storageActions.setCommonItem({ isResumeProcess: path !== recruitList }))
    }
    if (path === recruitList) {
      const currentPage = wx.$.r.getCurrentPage()
      if (currentPage && currentPage.route === 'pages/index/index') {
        return false
      }
      if (!isNewResume) {
        const { exist } = await fetchResumeExist().catch(() => ({ exist: true }))
        const query = !exist ? {
          type: 'jumpreclist',
          origin: 'complete',
          isFromRolePop: true,
        } : {}
        wx.$.r.reLaunch({
          path: exist ? recruitList : '/subpackage/resume/resume_publish/index',
          query,
        })
        return false
      }
    }
    wx.$.r.reLaunch({ path })
    return isNewResume && path !== recruitList
  } catch (error) {
    !isNewResume && wx.$.r.reLaunch({ path })
  }
  return false
}

.container {
  width: 100%;
  padding: 24rpx 32rpx;
  background-color: #f5f6fa;

  .card {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 16rpx;
    padding: 32rpx 24rpx;
    background-color: #fff;
    border-radius: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .cardImg {
      flex-shrink: 0;
      width: 160rpx;
      height: 120rpx;
      margin-right: 24rpx;
    }

    .cardContent {
      display: flex;
      flex-direction: column;

      .cardContentTitle {
        padding-bottom: 8rpx;
        color: @text-color;
        font-weight: 700;
        font-size: 34rpx;
      }

      .cardContentDesc {
        color: @text-secondary-color;
        font-weight: 400;
        font-size: 26rpx;
      }
    }
  }
}

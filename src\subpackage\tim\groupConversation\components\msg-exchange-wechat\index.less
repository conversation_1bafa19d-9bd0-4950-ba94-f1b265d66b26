.msg-main {
  display: flex;
  justify-content: flex-end;
  margin: 0rpx 24rpx 40rpx;
}

.main-start {
  justify-content: flex-start;
}

.main-center {
  justify-content: center;
}

.card-v {
  position: relative;
  padding: 32rpx 24rpx;
  width: 542rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 1);
  border: 1rpx solid rgba(153, 211, 255, 1);
}

.card-top-bg {
  border-radius: 24rpx;
  position: absolute;
  top: 0;
  left: 0;
  width: 542rpx;
  height: 160rpx;
  background: linear-gradient(
    180deg,
    rgba(0, 146, 255, 0.13) 0%,
    rgba(224, 243, 255, 0) 100%
  );
}

.card-head {
  display: flex;
}

.card-icon-v {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  background: rgba(0, 146, 255, 1);
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-img {
  width: 48rpx;
  height: 48rpx;
}

.card-txt {
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
}

.card-content {
  display: flex;
  flex-direction: column;
}

.card-title {
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
}

.card-desc {
  color: rgba(0, 0, 0, 0.65);
  font-size: 30rpx;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 32rpx;
}

.btn {
  width: 240rpx;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.65);
  font-size: 30rpx;
}

.refuse {
  background: rgba(245, 246, 250, 1);
}

.agree {
  background: rgba(224, 243, 255, 1);
}

.refuse-cl {
  background: rgba(245, 246, 250, 1);
  color: rgba(0, 0, 0, 0.25);
  width: 100%;
}

.agree-cl {
  background: rgba(245, 246, 250, 1);
  color: rgba(0, 0, 0, 0.25);
  width: 100%;
}

.btn-icon {
  margin-right: 12rpx;
}

.copy {
  background: rgba(224, 243, 255, 1);
  width: 100%;
}
import dayjs from '@/lib/dayjs/index'
import { store } from '@/store/index'
import { helper } from '@/utils/index'

import { RESUME_OCC_FILTER_MAX_SELECT_NUM } from '@/config/app'
import { getJobTopPriceInfoApi, getJobTopTimeListApi, getTopConfig, handleTopJobFail } from './api-server-new'
import { IDayPickerListItem, IJobTopInfo, ITopCityListItem, ITopTimeListItem } from './types'

export function getDayPickerRangeList(list: any[]) {
  try {
    return list.map((it) => {
      const day = it.days
      return { name: day < 4 ? `${24 * day}小时(${day}天)` : `${day}天`, value: day }
    })
  } catch (error) {
    return []
  }
}

/**
 * @name 初始化页面数据

 * @param jobTopInfo 当前招工置顶信息
 * @description 置顶操作类型：1=预约置顶；2=去置顶；3=修改置顶；4=继续置顶；
 */
export async function initRenewPageDataService(jobTopInfo: IJobTopInfo, query: any) {
  try {
    // 获取置顶时间配置信息
    const topConfigData = await getJobTopTimeListApi()
    // topType 置顶操作类型：1=预约置顶；2=去置顶；3=修改置顶；4=继续置顶；
    const { jobId } = jobTopInfo

    // 置顶状态 -1-未置顶 0-预约置顶 1-置顶中 2-暂停置顶中 3-置顶已结束
    const jobTopStatus = jobTopInfo.jobTopStatus?.code

    // 时间配置升序排列
    const topTimeList: ITopTimeListItem[] = topConfigData.topConfigList.sort((a, b) => a.days - b.days) || []
    const { dayPickerIdx, dayPickerList } = getDefaultSelectTimeConfig(jobTopInfo, topTimeList)
    const dayPickerActive = dayPickerList[dayPickerIdx]
    const topTimeActive = topTimeList[dayPickerIdx]

    // 获取计算价格
    const topDays = dayPickerActive.value
    const isTopping = jobTopStatus == 1 || jobTopStatus == 2

    // 获取置顶的价格
    const provinces = []
    const cities = []
    // 默认置顶城市列表
    const topCityListOld: ITopCityListItem[] = await getDefaultTopCityList(jobTopInfo, query, isTopping)
    topCityListOld.forEach((it) => (it.pid == 1 ? provinces.push(it.id) : cities.push(it.id)))

    let jobTopPriceInfo = null
    try {
      if (!jobTopStatus || jobTopStatus != 2) {
        jobTopPriceInfo = await getJobTopPriceInfoApi({ jobId: String(jobId), topType: getDefaultTopTypeCode(jobTopInfo), topDays, provinces, cities })
      }
    } catch (error) {
      handleTopJobFail.call(this, error, true)
    }

    // TODO zddq 招工置顶结束日期
    const jobTopEndTimeStr = getJobTopEndTimeStr(jobTopInfo, dayPickerActive)

    const topCityListNew = wx.$.u.deepClone(topCityListOld)
    return {
      dayPickerList,
      dayPickerIdx,
      dayPickerActive,

      topConfig: topConfigData,
      topTimeList,
      topTimeActive,

      jobTopInfo: { ...jobTopInfo, planEndDateTimeStr: dayjs(jobTopInfo.planEndDateTime).format('YYYY-MM-DD HH:mm') },
      jobTopPriceInfo,
      jobTopEndTimeStr,
      jobTopStatus,
      isTopping,

      topCityListOld,
      topCityListNew,
    }
  } catch (error) {
    console.error('初始化招工置顶页面数据失败 =>', error)
    return null
  }
}

/**
 * @name 初始化页面数据
 * @param jobTopInfo 当前招工置顶信息
 * @description 置顶操作类型：1=预约置顶；2=去置顶；3=修改置顶；4=继续置顶；
 */
export async function initPageDataService(jobTopInfo: IJobTopInfo, query: any) {
  try {
    // topType 置顶操作类型：1=预约置顶；2=去置顶；3=修改置顶；4=继续置顶；
    const { jobId } = jobTopInfo

    // 置顶状态 -1-未置顶 0-预约置顶 1-置顶中 2-暂停置顶中 3-置顶已结束
    const jobTopStatus = jobTopInfo.jobTopStatus?.code

    // // 时间配置升序排列
    // const topTimeList: ITopTimeListItem[] = topConfigData.topConfigList.sort((a, b) => a.days - b.days) || []
    // const { dayPickerIdx, dayPickerList } = getDefaultSelectTimeConfig(jobTopInfo, topTimeList)
    // const dayPickerActive = dayPickerList[dayPickerIdx]
    // const topTimeActive = topTimeList[dayPickerIdx]

    // 获取计算价格
    // const topDays = dayPickerActive.value
    const isTopping = jobTopStatus == 1 || jobTopStatus == 2

    // 获取置顶的价格
    const provinces = []
    const cities = []
    // 默认置顶城市列表
    const topCityListOld: ITopCityListItem[] = await getDefaultTopCityList(jobTopInfo, query, isTopping)
    topCityListOld.forEach((it) => (it.pid == 1 ? provinces.push(parseInt(it.id, 10)) : cities.push(parseInt(it.id, 10))))

    let jobTopMealInfo = null
    try {
      if (!isTopping) {
        jobTopMealInfo = await getTopConfig({ bizType: 0, targetId: String(jobId), provinces, cities })
      }
    } catch (error) {
      handleTopJobFail.call(this, error, true)
    }

    // TODO zddq 招工置顶结束日期
    // const jobTopEndTimeStr = getJobTopEndTimeStr(jobTopInfo, dayPickerActive)

    const topCityListNew = wx.$.u.deepClone(topCityListOld)

    return {
      // dayPickerList,
      // dayPickerIdx,
      // dayPickerActive,
      // topConfig: topConfigData,
      // topTimeList,
      // topTimeActive,
      // jobTopEndTimeStr,
      jobTopInfo,
      jobTopMealInfo,
      jobTopStatus,
      isTopping,
      topCityListOld,
      topCityListNew,
      activeTopMeal: jobTopMealInfo.configList.find((item) => item.defaultShow) || jobTopMealInfo.configList[0],
    }
  } catch (error) {
    console.error('初始化招工置顶页面数据失败 =>', error)
    return null
  }
}
/**
 * @name 获取默认选中的时间配置
 */
function getDefaultSelectTimeConfig(jobTopInfo: IJobTopInfo, topTimeList: ITopTimeListItem[]) {
  const dayPickerIdx = getDayPickerIdx(jobTopInfo)
  const dayPickerList: IDayPickerListItem[] = topTimeList.map((it) => {
    const day = it.days
    return { name: it.title, value: day }
  })

  return { dayPickerIdx, dayPickerList }

  function getDayPickerIdx(jobTopInfo: IJobTopInfo) {
    const { jobTopStatus, pinDays } = jobTopInfo

    const isTopping = jobTopStatus.code == 1 || jobTopStatus.code == 2
    if (isTopping) {
      let findIdx = topTimeList.findIndex((it) => it.days == pinDays)
      if (findIdx != -1) {
        return findIdx
      }

      findIdx = topTimeList.findIndex((it) => it.defaultShow)
      return findIdx === -1 ? 0 : findIdx
    }

    const findIdx = topTimeList.findIndex((it) => it.defaultShow)
    return findIdx === -1 ? 0 : findIdx
  }
}

/**
 * @name 获取默认选中的置顶城市列表
 */
async function getDefaultTopCityList(jobTopInfo: IJobTopInfo, query: any, isTopping: boolean) {
  const topCities = isTopping
    ? [...(jobTopInfo.topProvinceIds || []), ...(jobTopInfo.topCityIds || [])]
    : (jobTopInfo.defaultAreaId && [jobTopInfo.defaultAreaId]) || []
  // 使用 参数路径 或 置顶招工信息返回的地区
  const areaTree = await wx.$.l.getAreaTreeData()

  // 处理 存在置顶城市情况
  if (topCities.length > 0) {
    return topCities.map((id) => findIdInTree(id, areaTree)).flat(1)
  }

  const areaId = jobTopInfo.defaultAreaId || query.defaultTopArea
  if (!areaId) {
    return []
  }

  return findIdInTree(areaId, areaTree)
}

/**
 * @name 获取置顶结束时间
 */
export function getJobTopEndTimeStr(jobTopInfo: IJobTopInfo, dayPickerActive: IDayPickerListItem) {
  const addMs = (dayPickerActive.value || 0) * 24 * 60 * 60 * 1000 // 秒级
  const jobTopStatus = jobTopInfo.jobTopStatus.code
  // 非置顶中 或 暂停置顶中 情况
  if (jobTopStatus != 1 && jobTopStatus != 2) {
    const nowSS = dayjs().valueOf()
    return dayjs(nowSS + addMs).format('YYYY-MM-DD HH:mm')
  }

  return dayjs(dayjs(jobTopInfo.planEndDateTime).valueOf() + addMs).format('YYYY-MM-DD HH:mm')
}

/**
 * @name 获取置顶的省市信息
 */
export function getTopCityInfo(cityList: ITopCityListItem[]) {
  const provinces = []
  const cities = []

  cityList.forEach((it) => (it.pid == 1 ? provinces.push(it.id) : cities.push(it.id)))

  return { provinces, cities }
}

/**
 * @name 树查找算法，根据id查找对应的节点信息，递归
 */
function findIdInTree(id: number | string, tree: any[]): any[] {
  let chooseTree = []
  for (let i = 0; i < tree.length; i += 1) {
    if (id == tree[i].id) {
      chooseTree = [tree[i]]
      break
    }

    if (tree[i].children && tree[i].children.length > 0) {
      const singleTree = findIdInTree(id, tree[i].children)
      if (singleTree.length > 0) {
        chooseTree = singleTree
        break
      }
    }
  }
  chooseTree = chooseTree.map((row) => ({ id: row.id, name: row.name, pid: row.pid }))
  return chooseTree
}

/**
 * @name 后端 topType 兜底数据
 */
export function getDefaultTopTypeCode(jobTopInfo: IJobTopInfo) {
  const { topType, jobTopStatus } = jobTopInfo
  const jobTopStatusCode = jobTopStatus.code
  if (topType?.code) {
    return +topType.code
  }

  // 置顶状态类型：-1-未置顶 0-预约置顶中 1-置顶中 2-暂停置顶中 3-置顶已结束
  const tmpObj = { '-1': 1, 0: 2, 1: 3, 2: 2, 3: 2 }
  return tmpObj[jobTopStatusCode] || 1
}

/** 前往找活列表 */
export async function goToResumeList(jobTopInfo: IJobTopInfo, topCityListNew: ITopCityListItem[]) {
  const { classifyConfig } = store.getState().classify
  const maxSelectOcc = classifyConfig.projectSearchOccCnt || RESUME_OCC_FILTER_MAX_SELECT_NUM
  const tmpOccV2 = jobTopInfo.occV2?.map((it) => ({ ...it, occIds: it.occIds?.slice(0, maxSelectOcc) }))

  const classify_id = await wx.$.l.transformOccV2ToHidClsId(tmpOccV2)
  const area_id = topCityListNew.map((it) => it.id).join(',')

  wx.$.r.reLaunch({ path: '/pages/resume/index', params: { classify_id, area_id } })
}

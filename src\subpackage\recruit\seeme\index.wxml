<!-- -------- 页面导航栏 -->
<custom-header title="谁看过我的职位" border id="custom-header" />
<!-- -------- 已隐藏的信息 -->
<buoy-of-msg wx:if="{{hiddenNumber > 0}}" hiddenNumber="{{hiddenNumber}}" />
<!-- -------- 谁看过我的招工列表 -->
<!-- <card wx:for="{{pageInfo.list}}" data-item="{{item}}" data-index="{{index}}" class="seeme-usercard-indexer" wx:key="userId" item="{{item}}" jobId="{{query.jobId}}" sourceType='whoContactedMeToResume' /> -->
<block wx:for="{{pageInfo.list}}">
  <view class="seeme-usercard-indexer" wx:key="uuid" data-item="{{item}}">
    <resume-card-v4 sceneV2="9" bind:toResumeDetail="onToResumeDetail" bind:change="onLocalClick"   jobId="{{query.jobId}}" item="{{item}}" data-item="{{item}}" data-index="{{index}}" data-origin="{{origin}}" showDutyLabel nearbyWorkerListApiSource="whoContactedMeToResume" bind:itchange="onItChange" bind:ischat="onIsChat" origin="{{origin}}" custom-class="custom-card" bind:selectpostionback="onSelectPostionBack" />
  </view>
</block>
<!-- -------- loading效果 -->
<yp-load-more show="{{pageInfo.loading}}" state="loading" />
<view wx:if="{{pageInfo.list.length && !pageInfo.loading}}" class="noMore">
  {{"- 近30天暂时没有更多查看记录了 -"}}
</view>
<!-- -------- 列表空状态 -->
<yp-empty show="{{pageInfo.finish && !pageInfo.list.length}}" img="default" title="{{pageInfo.list.length?'近30天暂时没有更多查看记录了':'近30天暂时没有被查看记录'}}" tip="{{showGoToToppingBtn?'去置顶职位，让更多牛人看到！':''}}" my-class="my-empty">
  <m-button wx:if="{{showGoToToppingBtn}}" bind:tap="goToTheTop" width="300rpx" height="80rpx" borderRadius="8rpx" custom-class="top-btn">
    去置顶
  </m-button>
</yp-empty>
<!-- -------- 可能适合您的工人版块 -->
<recommend-workers show="{{pageInfo.finish}}" jobId="{{query.jobId}}" />
<!-- -------- 底部横条适配，不同机型有差异, 自动适配底部，不用多引 -->
<view class="m-stripes">
  <m-stripes backgroundColor="#fff"></m-stripes>
</view>
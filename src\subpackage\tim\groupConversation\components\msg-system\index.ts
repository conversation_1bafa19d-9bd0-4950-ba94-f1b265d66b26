/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 普通招工详情，底部tabbar
 */
// import { store } from '@/store/index'

import { H5_HYBRID_URL } from '@/config/app'
import { store } from '@/store/index'
import { toJSON } from '@/utils/tools/formatter/index'

Component({
  properties: {
    /** 招工详情，接口内容，及自定义状态 */
    msgInfo: {
      type: Object,
      value: {},
    },
  },
  data: {},
  methods: {
    onClick(e) {
      const { item } = e.currentTarget.dataset
      const { userId } = store.getState().storage.userState
      const { path, ctype } = item || {}
      if (ctype == 'gtry') {
        this.triggerEvent('sysclick', item)
        return
      }
      // 引导关注公众号
      if (path.indexOf('sceneCode') >= 0) {
        wx.$.r.push({ path: `${path}&userId=${userId}` })
        return
      }
      const { msgInfo } = this.data
      const url = `${H5_HYBRID_URL}/deposit/sign-in/worker/${msgInfo.customExts.id}`
      wx.$.r.push({
        path: '/subpackage/web-view/index',
        query: { url, isLogin: true, isLocation: true },
      })
    },
    onRenderClick(e) {
      const { item } = e.currentTarget.dataset
      const { data } = item || {}
      const { content } = data || {}
      const { mini } = content || {}
      // action 1：外部跳转 2：弹窗标识 3：内部跳转
      const { action, identification } = mini || {}
      if (action == 1) {
        let nUrl = decodeURIComponent(identification)
        let isChange = false
        if (nUrl.indexOf('?') >= 0) {
          const obj: any = wx.$.u.getUrlAllParams(nUrl)
          if (obj.resParams) {
            const resParamsObj = toJSON(obj.resParams)
            if (!wx.$.u.isEmptyObject(resParamsObj)) {
              resParamsObj.fileName = encodeURIComponent(resParamsObj.fileName)
              obj.resParams = JSON.stringify(resParamsObj)
              const urlArr = nUrl.split('?')
              const [o] = urlArr
              const params = Object.keys(obj).map(ky => `${ky}=${obj[ky]}`).join('&')
              const ep = `?${params}`
              nUrl = `${o}${encodeURIComponent(ep)}`
              isChange = true
            }
          }
        }
        wx.$.r.push({ path: '/subpackage/web-view/index', query: { isLogin: true, url: isChange ? nUrl : identification } })
      } else if (action == 3) {
        wx.$.r.push({ path: identification, query: { isLogin: true } })
      }
    },
    onReEdit() {
      const { msgInfo } = this.data
      const { payload } = msgInfo || {}
      const { otext } = payload || {}
      this.triggerEvent('reedit', { text: otext })
    },
  },
})

<view class="msg-txt-v">
  <longpress-tk msgInfo="{{msgInfo}}" wx:if="{{isShowTk}}" isCopy="{{false}}" tkWith="{{tkWith}}" />
  <msg-state wx:if="{{msgInfo.isSelf && msgInfo.status != 'sending'}}" style="height: 100%;" margin="right" msgInfo="{{msgInfo}}" status="{{msgInfo.status}}" bind:statusclcik="onStatusClcik"/>
  <view class="msg-txt" catch:tap="onClick" catch:longpress="onLongpressMsg" id="{{msgInfo.id}}_tk">
    <msg-state wx:if="{{msgInfo.isSelf && msgInfo.status == 'sending'}}" margin="right" msgInfo="{{msgInfo}}" status="sending" isMid bind:statusclcik="onStatusClcik"/>
    <image lazy-load="{{true}}" mode="aspectFill" class="image-o" bindload="bindload" src="{{msgInfo.payload.imageInfoArray[2].url?msgInfo.payload.imageInfoArray[2].url:msgInfo.payload.imageInfoArray[2].imageUrl}}"  />
  </view>
</view>
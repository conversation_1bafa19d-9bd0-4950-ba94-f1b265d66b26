<view class="v4-recommend-workers" style="display:{{show&&recommendWorkersInfo.list.length>0?'block':'none'}}">
  <view class="content">
    <!-- ------ 标题 -->
    <view class="title">
      <text>其他</text>
      <text class="highlight">相似经历</text>
      <text>的牛人</text>
    </view>
    <!-- ------ 推荐师傅列表 -->
    <view wx:for="{{recommendWorkersInfo.list}}" wx:key="index" class="recommend-worker-card" data-item="{{item}}" data-index="{{index}}">
      <resume-card-v4
        item="{{item}}"
        index="{{index}}"
        isRelatedFactory="{{false}}"
        isShowTopIcon="{{false}}"
        routerType="push"
        sourceId="4"
        specialArea="{{query.specialArea}}"
        showDutyLabel
        nearbyWorkerListApiSource="ResumeDetails"
        isActiveAb="{{isActiveAb}}"
      />
    </view>
    <!-- ------ 查看更多找活信息 -->
    <!-- <view wx:if="{{url}}" class="view-more" bind:tap="viewMoreResumeInfo">查看更多简历信息</view> -->
     <view class="not-have-more">- 没有更多了 -</view>
  </view>
</view>

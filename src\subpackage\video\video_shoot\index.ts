/*
 * @Author: 汤启民
 * @Date: 2022-03-03 15:30:31
 * @Description: 拍摄视频
 */
import { helper } from '@/utils/index'
import { MapStateToData, connectPage, dispatch, actions, store } from '@/store/index'
import { getHeaderHeight, getMenuButtonBoundingClientRect } from '@/utils/tools/common/index'
import { fetchResumeExist, refreshMyInfo } from '@/utils/helper/resume/index'

const { top: menuTop, width: rectWidth } = getMenuButtonBoundingClientRect()
const resolution = 'medium' // brandList.includes(brand) || modelList.includes(model) ? 'medium' : 'high'
/** 用于进度条的定时器 */
let progressTimer
/** 用于进度条计算进度的计数 */
let progressCount = 0
/** 用于拍摄时长的定时器 */
let shootingTimer
/** canvas的真实宽度 */
let canvasWidth = 0
/** 面试问题总体展示的时间 */
let questionDisplayTime = 0
/** 横屏提示 */
let showLandscapeModal = false
/** 是否正在处理视频（防抖） */
let isLoading = false
const mapStateToData: MapStateToData = (state) => {
  return {
    storageState: state.storage,
  }
}

Page(
  connectPage(mapStateToData)({
    data: {
      rectWidth,
      /** 拍摄类型： 1 - 面试视频 ， 2 - 项目视频 */
      videoType: '1',
      /** 页面来源，标记从哪一个页面跳转过来的 */
      source: '',
      /** 是否显示摄像机 */
      showCamera: false,
      /** 是否是从我的拍摄页进入 */
      isVideoMePage: '1',
      /** 视频可上传数 */
      uploadCount: 1,
      pagePaddingTop: menuTop,
      stickyTop: getHeaderHeight(`-${menuTop}px + 8rpx`),
      videoConfig: {
        timeLimit: 30,
        watermarkText: '本视频由鱼泡水印相机认证拍摄',
      },
      /** 面试问题列表 */
      questions: [],
      /** java接口上传必传项 入口场景id：默认100001，公开文件：100001，私有文件：100002 */
      entryId: 1004,
      /** 上传视频封面的入口场景id：为空时使用 entryId的值 */
      entryIdVideo: null,
      /** 媒体资源子模块 */
      appId: 102,
      /** 摄像头朝向：front-前置，back - 后置 */
      devicePosition: 'front',
      /** 分辨率，不支持动态修改，所以一开始就要设置 */
      resolution,
      /** 拍摄状态：default - 默认未开始，shooting - 拍摄中，complete - 拍摄完成 */
      shootState: 'default',
      /** canvas上下文 */
      canvasCtx: null,
      /** 摄像头上下文 */
      cameraCtx: null,
      /** 保存的视频临时地址（本地） */
      videoPath: '',
      /** 保存的封面临时地址（本地） */
      thumbPath: '',
      /** 当前录制时间 */
      currentShootingTime: 0,
      /** 拍摄中当前展示的是第几个问题 */
      currentQuestionIndex: 0,
      /** 当前屏幕方向：0 - 竖屏 ， 1 - 横屏 */
      screenDirect: 0,
      /** 示例视频 */
      videoUrl: '',
      /** 封面图 */
      poster: '',
    },
    /** 点击相册 */
    async onClickPhotoAlbum() {
      const res = await wx.chooseMedia({
        count: this.data.uploadCount,
        mediaType: ['video'],
        sizeType: ['origin', 'compressed'],
        sourceType: ['album'],
      })
      if (res.tempFiles && res.tempFiles.length) {
        const mediaFile = res.tempFiles.map((item) => {
          const { width, height, size, duration, tempFilePath, thumbTempFilePath } = item
          return {
            /**  文件类型： image - 图片 ， video - 视频 */
            fileType: 'video',
            /** 文件的本地临时路径 */
            tempFilePath,
            /** 视频封面的本地临时路径,如果文件类型是图片则此字段为空 */
            thumbTempFilePath,
            /** videoId */
            videoId: '',
            /** 水印图地址 */
            watermark: '',
            width,
            height,
            size,
            duration,
          }
        })
        dispatch(actions.videoActions.setMediaFile(mediaFile))
        // 修复视频未关闭的兼容问题
        await wx.$.u.wait(500)
        wx.stopAccelerometer()
        wx.offAccelerometerChange()
        this.data.cameraCtx?.stopRecord()
        wx.$.r.back()
      }
    },
    /** 点击开始拍摄 */
    async onClickStartShoot() {
      /** 是否正在启动录制（防抖） */
      if (this.isStartRecord) {
        return
      }
      this.isStartRecord = true
      /** 未授权，且非首次发起授权 */
      await wx.$.u.getAuthorize('scope.record', '麦克风')
      /** 更改拍摄状态 */
      this.setData({
        shootState: 'shooting',
      })
      questionDisplayTime = this.data.questions[0]?.mini_hold_time || 0
      // 获取canvas在屏幕上的真实宽度，用于确定绘画半径
      // canvasCtx和canvasWidth在重新录制时就不需要再获取了
      // 防止重新获取浪费性能
      if (!this.data.canvasCtx) {
        this.data.canvasCtx = wx.createCanvasContext('progress')
      }
      if (!canvasWidth) {
        wx.createSelectorQuery()
          .select('#progress')
          .boundingClientRect((rect) => {
            if (rect) {
              canvasWidth = rect.width
              this.startCamera()
            }
          })
          .exec()
      } else {
        this.startCamera()
      }
      setTimeout(() => {
        this.isStartRecord = false
      }, 1000)
    },
    /** 返回到上一页 */
    onClickToPrev() {
      wx.$.r.back()
    },
    /** 切换摄像头 */
    toggleCameraPosition() {
      this.setData({
        devicePosition: this.data.devicePosition === 'front' ? 'back' : 'front',
      })
    },
    /** 开始录制 */
    startCamera() {
      /** 停止监听设备方向变化 */
      wx.stopAccelerometer()
      /** 获取摄像机上下文，开始录像 */
      if (!this.data.cameraCtx) {
        this.setData({
          cameraCtx: wx.createCameraContext(),
        })
      }
      this.data.cameraCtx.startRecord({
        timeoutCallback: (res) => {
          // 录制结束：超过最大时间或手动点击
          this.setData({
            videoPath: res.tempVideoPath,
            thumbPath: res.tempThumbPath,
            shootState: 'complete',
          })
          this.handleShootComplete()
        },
        timeout: this.data.videoConfig.timeLimit,
        success: () => {
          /** 这个API某些情况下会报错，原因未知 */
          wx.setKeepScreenOn({
            keepScreenOn: true,
          }).finally(() => {
            this.recordShootingTime()
            this.handleCircleProgress()
          })
        },
        fail: (err) => {
          console.log(err)
          this.resetInfo()
        },
      })
    },
    /** 点击停止按钮，停止录制 */
    onStopShooting() {
      if (isLoading) {
        return
      }
      isLoading = true

      wx.showLoading({ title: '加载中', mask: true })
      setTimeout(() => {
        clearInterval(shootingTimer)
        clearInterval(progressTimer)
        const { currentShootingTime, videoConfig } = this.data
        if (currentShootingTime >= videoConfig.timeLimit - 1) {
          isLoading = false
          return
        }
        this.data?.cameraCtx?.stopRecord({
          success: (res) => {
            this.setData({
              videoPath: res.tempVideoPath,
              thumbPath: res.tempThumbPath,
              shootState: 'complete',
            })
            this.handleShootComplete()
          },
          fail: (err) => {
            wx.hideLoading({ noConflict: true })
            console.log(err)
            // wx.$.alert({
            //   content: '停止录制出现错误，请重试',
            // }).then(() => {
            // })
            this.resetInfo()
          },
          complete: () => {
            isLoading = false
          },
        })
      }, 1000)
    },
    /** 处理视频拍摄完毕之后的逻辑 */
    handleShootComplete() {
      const { videoType } = this.data
      if (this.data.currentShootingTime >= 10 || (videoType === '2' && this.data.currentShootingTime >= 5)) {
        wx.$.r.push({
          path: '/subpackage/video/video_upload/index',
          query: {
            videoType,
            direction: this.data.currentShootingTime,
            source: this.data.source,
            videoPath: encodeURIComponent(this.data.videoPath),
            thumbPath: this.data.thumbPath,
            entryId: this.data.entryId,
            entryIdVideo: this.data.entryIdVideo,
            appId: this.data.appId,
          },
        })
      } else {
        wx.$.msg(`拍摄视频不能低于${videoType === '2' ? '5' : '10'}秒`)
      }
      // noConflict主要为了不去隐藏wx.$.msg('拍摄视频不能低于10秒') 弹窗
      wx.hideLoading({ noConflict: true })
      this.resetInfo()
    },
    /** 记录当前的拍摄时间和根据时间来轮播第几个问题 */
    recordShootingTime() {
      clearInterval(shootingTimer)
      shootingTimer = setInterval(() => {
        this.setData({
          currentShootingTime: this.data.currentShootingTime + 1,
        })
        /** 面试问题的轮播 */
        // if (this.data.currentShootingTime > questionDisplayTime && this.data.questions[this.data.currentQuestionIndex + 1]) {
        //   questionDisplayTime += this.data.questions[this.data.currentQuestionIndex + 1]?.mini_hold_time || 0
        //   this.setData({
        //     currentQuestionIndex: this.data.currentQuestionIndex + 1,
        //   })
        // }
        /** 录制时间结束后出现loading，等待录制结束的回调触发 */
        if (this.data.currentShootingTime === this.data.videoConfig.timeLimit) {
          clearInterval(shootingTimer)
          clearInterval(progressTimer)
          wx.showLoading({ title: '加载中', mask: true })
        }
      }, 1000)
    },
    /** 用于处理进度条 */
    handleCircleProgress() {
      clearInterval(progressTimer)
      progressTimer = setInterval(() => {
        progressCount += 1
        const direction = (progressCount / (this.data.videoConfig.timeLimit * 10)) * 2
        this.data.canvasCtx.setLineWidth(4)
        this.data.canvasCtx.setStrokeStyle('#0092FF')
        this.data.canvasCtx.setLineCap('round')
        this.data.canvasCtx.arc(canvasWidth / 2, canvasWidth / 2, canvasWidth / 2 - 2, -Math.PI / 2, direction * Math.PI - Math.PI / 2, false)
        this.data.canvasCtx.stroke()
        this.data.canvasCtx.draw()
      }, 100)
    },
    /** 重置录制信息 */
    resetInfo() {
      clearInterval(shootingTimer)
      clearInterval(progressTimer)
      progressCount = 0
      questionDisplayTime = 0
      // 清空canvas
      if (this.data.canvasCtx) {
        this.data.canvasCtx.clearRect(0, 0, canvasWidth, canvasWidth)
        this.data.canvasCtx.draw()
      }
      this.setData({
        currentShootingTime: 0,
        currentQuestionIndex: 0,
        shootState: 'default',
      })
      // 关闭保持屏幕常亮
      wx.setKeepScreenOn({
        keepScreenOn: false,
      })
    },
    /** 获取问题配置信息 */
    initExampleConfig() {
      /** 默认请求的项目视频的问题和配置，如果是面试视频，则需要请求另外一个接口，并携带resume_id */
      if (this.data.videoType === '1') {
        wx.$.javafetch['POST/resume/v3/video/getBaseConfig']().then((res) => {
          const { config, questions } = res.data
          this.setData({
            videoConfig: {
              timeLimit: config.timeLimit > 30 ? 30 : config.timeLimit,
              watermarkText: config.watermarkText || '',
            },
            videoUrl: config.video,
            poster: config.cover,
            questions,
          })
        })
        return
      }
      wx.$.javafetch['POST/resume/v3/project/getBaseConfig']().then((res) => {
        const { config, questions } = res.data
        this.setData({
          videoConfig: {
            timeLimit: config.timeLimit > 30 ? 30 : config.timeLimit,
            watermarkText: config.watermarkText,
          },
          videoUrl: config.video,
          poster: config.cover,
          questions,
        })
      })
    },
    /** 校验用户是否拥有找活名片 */
    async validateResumeCard() {
      const data: any = await fetchResumeExist()
      if (!data || !data.exist) {
        wx.$.msg('请先发布简历')
        wx.$.r.replace({
          path: '/subpackage/resume/resume_publish/index?optype=back&resume=1',
        })
        return false
      }
      await refreshMyInfo('refresh', { isStore: true })
      const { myResumeDetails } = store.getState().storage
      const { videoResp } = myResumeDetails || {}

      const { uuid } = videoResp || {}
      const { source, isVideoMePage } = this.data
      if (uuid && source !== '1' && isVideoMePage !== '1') {
        /** 是否发布过面试视频 */
        wx.$.r.replace({
          path: '/subpackage/video/video_me/index',
          query: {
            source,
          },
        })
        return false
      }
      return true
    },
    /** 监听设备方向变化 */
    deviceMotion() {
      /** 0为竖屏，1为横屏 */
      let lastState = 0
      let lastTime = Date.now()
      wx.startAccelerometer()
      /** 监听设备方向变化事件 */
      wx.onAccelerometerChange((result) => {
        const now = Date.now()
        /** 500ms检测一次 */
        if (now - lastTime < 500) {
          return
        }
        lastTime = now
        const Roll = Math.atan2(-result.x, Math.sqrt(result.y * result.y + result.z * result.z)) * (180 / Math.PI)
        const nowState = Roll > 60 || Roll < -60 ? 1 : 0
        if (nowState !== lastState) {
          lastState = nowState
          /** 横屏 */
          if (nowState === 1) {
            this.setData({
              screenDirect: 1,
            })
            if (this.data.videoType === '1' && !showLandscapeModal) {
              showLandscapeModal = true
              wx.$.alert({
                content: '请保持竖屏拍摄，以达到最佳拍摄效果！',
                confirmText: '我知道了',
              }).finally(() => {
                showLandscapeModal = false
              })
            }
          } else {
            /** 竖屏 */
            this.setData({
              screenDirect: 0,
            })
          }
        }
      })
    },
    async onLoad(query) {
      // wx.$.javafetch['POST/resume/v1/resumeVideo/detail']()
      /** 每次需要重置数据，清理定义在Page()外部的变量，防止因为缓存导致的意外 */
      this.resetInfo()
      const data = {
        /** 拍摄类型： 1 - 面试视频 ， 2 - 项目视频 */
        videoType: query.videoType || '1',
        /** 页面来源 */
        source: query.source || '',
        devicePosition: 'front',
        isVideoMePage: query.isVideoMePage,
        entryId: query.entryId || 1004,
        entryIdVideo: query.entryIdVideo || null,
        appId: query.appId || 102,
        showCamera: true,
        uploadCount: Number(query.uploadCount || 1),
      }
      data.devicePosition = data.videoType === '1' ? 'front' : 'back'
      this.setData(data)
      /** 如果用户不是从我的面试视频页面进入，则需要校验该用户是否有找活名片 */
      /** 如果没有找活名片则需要先完善找活名片后才能拍摄视频 */
      let validateResumeCard = true
      if (data.videoType === '1') {
        validateResumeCard = await this.validateResumeCard()
      }
      if (!validateResumeCard) {
        return
      }
      /** 初始化视频配置 */
      this.initExampleConfig()
      /** 进入页面就需要开始监听横屏还是竖屏 */
      this.deviceMotion()
    },
    onHide() {
      /** 停止监听设备方向变化，防止push页面后，当前页面继续监听导致提示弹窗重复出现 */
      try {
        wx.stopAccelerometer()
        wx.offAccelerometerChange()
        this.data.cameraCtx?.stopRecord()
      } catch (e) {
        console.log(e)
      }
    },
    onUnload() {
      /** 停止监听设备方向变化，防止push页面后，当前页面继续监听导致提示弹窗重复出现 */
      try {
        wx.stopAccelerometer()
        wx.offAccelerometerChange()
        this.data.cameraCtx?.stopRecord()
      } catch (e) {
        console.log(e)
      }
    },
  }),
)

/** 业务逻辑复用相关函数 */
export * as helper from './helper/index'
/** 纯工具函数 */
export * as tools from './tools/index'
/** 页面列表通用处理工具函数 */
export { default as PLTools, IReqPLOptions } from './tools/page-list-tool/index'

/** 获取小程序登录的code */
export function getLoginCode(): Promise<any> {
  return new Promise((resolve) => {
    if (ENV_IS_WEAPP || ENV_IS_TT) {
      // 如果是微信和字节环境
      wx.login({
        success(res) {
          console.log('授权登录成功')
          resolve(res)
        },
        fail(err) {
          console.log('授权登录失败', err)
          resolve('')
        },
      })
    } else if (ENV_IS_SWAN) {
      // 如果是百度环境
      wx.getLoginCode({
        success(res) {
          resolve(res)
        },
        fail() {
          resolve('')
        },
      })
    }
  })
}

<view class="container">
  <custom-header />
  <!-- 登录标题 -->
  <view class="title">
    <image class="image" src="https://staticscdn.zgzpsjz.com/miniprogram/images/ykl/yp-mini_signin_dl_logo.png" mode="aspectFill" lazy-load="false" binderror="" bindload="" />
    登录/注册
  </view>
  <view class="tabs">
    <view class="tab {{active == 1 ? 'active' : ''}}" data-index="1" bind:tap="onChangeTab">
      验证码登录
    </view>
    <view class="tab {{active == 2 ? 'active' : ''}}" data-index="2" bind:tap="onChangeTab">
      密码登录
    </view>
  </view>
  <!-- 登录 -->
  <view class="body">
    <!-- 手机号 -->
    <view class="item first">
      <view class="{{phone ? 'phone-icon' : 'default-icon'}}">
        <icon-font type="yp-icon_signin_dl_phone" size="48rpx" />
      </view>
      <input class="item-input" data-type="phone" value="{{phone}}" bind:input="onChange" bind:blur="onBlurPhone" placeholder="请输入手机号码" maxlength="11" />
      <icon-font wx:if="{{phone}}" data-type="phone" bind:tap="onClear" type="yp-icon_close" size="48rpx" color="rgba(0, 0, 0, 0.45)" />
    </view>
    <!-- 验证码 -->
    <view class="item" wx:if="{{active == 1}}">
      <view class="{{code ? 'phone-icon' : 'default-icon'}}">
        <icon-font type="yp-icon_signin_dl_code" size="48rpx" />
      </view>
      <input class="item-input" data-type="code" type="number" value="{{code}}" bind:input="onChange" placeholder="请输入验证码" maxlength="4" />
      <icon-font wx:if="{{code}}" data-type="code" bind:tap="onClear" type="yp-icon_close" size="48rpx" color="rgba(0, 0, 0, 0.45)" />
      <verification-code biz="login" showMsg="{{false}}" bind:onShowTip="onShowErrorTip" class="get-code-btn" tel="{{phone}}" />
    </view>
    <!-- 密码 -->
    <view class="item" wx:if="{{active == 2}}">
      <view class="{{password ? 'phone-icon' : 'default-icon'}}">
        <icon-font type="yp-icon_signin_dl_mima" size="48rpx" />
      </view>
      <input class="item-input" data-type="password" value="{{password}}" bind:input="onChange" placeholder="请输入密码" maxlength="11" />
      <icon-font wx:if="{{password}}" data-type="password" bind:tap="onClear" type="yp-icon_close" size="48rpx" color="rgba(0, 0, 0, 0.45)" />
    </view>
    <!-- 错误提示 -->
    <view class="word-tips">{{errorTip}}</view>
    <button class="login-btn" style="opacity: {{phone ? '' : '0.6'}}" bind:tap="onLogin">登录</button>
    <view class="agreement">
      <view class="box" bind:tap="onCheckAgreement">
        <view class="icon {{isCheckAgreement ? 'color' : ''}}">
          <icon-font type="yp-icon_sign_xieyi_y" size="52rpx" />
        </view>
        <view>
          我已阅读并同意
          <text class="color" data-type="privacy" catch:tap="onToGreement">《隐私政策》</text>
          <text class="color" data-type="user" catch:tap="onToGreement">《服务协议》</text>
          并授权鱼泡网获得本机号码
        </view>
      </view>
    </view>
  </view>
</view>
<plugin-guard />

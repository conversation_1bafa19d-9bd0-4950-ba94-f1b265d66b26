/*
 * @Date: 2022-02-18 09:42:10
 * @Description: web-view
 * 路由参数
 *    url: h5路由地址，如果不带https 则默认为内部h5域名
 *    isLogin :  是否需要携带登录信息   true:需要  false:不需要  可以不传
 *    isLocation: 是否需要携带定位信息  true:需要  false:不需要  可以不传
 *    isPostLogin: 后置登录,跳转页面不进行登录,手动触发登录后,触发刷新当前页面 true:需要  false:不需要  可以不传
 */

import { storage, store } from '@/store/index'
import { getH5OfHeader, toastZhuliNewUser } from '@/utils/helper/common/index'
import { tryPromise } from '@/utils/tools/common/index'
import { H5_HYBRID_URL } from '@/config/app'
import { parseWebShortToken, removeUrlParameter, replaceUrlParameter, setShareInfo } from './utils'
import { toJSON } from '@/utils/tools/formatter/index'
import { SHARE_CHANNEL, SHARE_DEFAULT_PATH } from '@/config/share'
import { toLogin } from '@/utils/helper/common/toLogin'
import { getShareInfoByTypeV2 } from '@/utils/helper/share/index'

// h5需要调用微信支付页面，用到支付的h5的路由需要加上，用于支付状态回调判断
// eslint-disable-next-line max-len

Page({
  data: {
    src: '',
    osrc: '',
    options: {},
    initSrc: '',
    payBack: 0, // 是否是支付返回
    message: {},
    collectEvent: {},
    first: true,
    toLoginBack: false,
    /** 当前定位的经纬度 */
    locationStr: 'location=',
  },

  options: {},
  async onLoad(options) {
    const { shareAgg, scene } = options || {}
    console.log('options', options)
    if (shareAgg == 'm9ttyl8jJXSNTQO' || scene === 'shareAgg%3Dm9ttyl8jJXSNTQO') {
      const query = {
        track_seed: 'm9ttyl8jJXSNTQO',
        refid: '*********',
        url: '/company-authentication',
        isPostLogin: true,
      }
      this.getUrl(query)
      this.options = query
      return
    }
    if (scene) {
      const extOptions = await parseWebShortToken(options)
      if (!wx.$.u.isEmptyObject(extOptions)) {
        this.getUrl({ ...options, ...extOptions })
        this.options = { ...options, ...extOptions }
        return
      }
    }
    this.getUrl(options)
    this.options = options
  },
  onShow() {
    const { isLogin: _isLogin, miniIsLogin } = this.data.options || {}
    let { url = '', isPostLogin } = this.data.options
    let isLogin = _isLogin || miniIsLogin // 是否需要登录
    url = decodeURIComponent(url)
    const userState = storage.getItemSync('userState')
    const [, ...reset] = url.split('?')
    const p = wx.$.u.isArrayVal(reset) ? reset.join('?') : ''
    if (p) {
      const obj: any = wx.$.u.getUrlAllParams(url) || {}
      if (!isPostLogin) {
        isPostLogin = obj.isPostLogin || false
      }
      if (!isLogin) {
        isLogin = obj.miniIsLogin || false
      }
      if (obj.navigator) {
        const nav = toJSON(obj.navigator)
        if (!wx.$.u.isEmptyObject(nav)) {
          wx.setNavigationBarColor(nav)
        }
      }
    }
    if (url.indexOf('isLogin') < 0 && userState.login && !this.data.first && !isLogin && isPostLogin) {
      wx.$.r.replace({
        path: '/subpackage/web-view/index',
        query: {
          ...this.data.options,
        },
      })
      return
    }
    const webviewRefresh = storage.getItemSync('webviewRefresh')
    // 再次邀请成功后返回上一页，需要刷新web-view
    if (webviewRefresh == 'midInvite') {
      storage.removeSync('webviewRefresh')
      const { osrc } = this.data
      this.setData({
        src: '',
      })
      setTimeout(() => {
        this.setData({
          src: osrc,
        })
      }, 200)
    } else if (webviewRefresh == 'midLogin') {
      if (!wx.$.u.isEmptyObject(this.data.options)) {
        storage.removeSync('webviewRefresh')
        this.getUrl(this.data.options)
      }
    }

    const { payBack, initSrc } = this.data
    const num = new Date().getSeconds()
    // const timeNum = new Date().getTime()
    if (payBack == 1) {
      /** 支付成功 */
      this.setData({
        src: `${initSrc}success${num}`,
        // 支付回来后初始化时否支付返回
        payBack: 0,
      })
    } else if (payBack == 2) {
      /** 支付失败 */
      this.setData({
        src: `${initSrc}error${num}`,
        // 支付回来后初始化时否支付返回
        payBack: 0,
      })
    }
    toastZhuliNewUser()
  },
  async getUrl(options, isSave = true) {
    let { url, isLogin, isLocation, isPostLogin, isWebFirmAuth } = options
    const { refid, track_seed } = options
    url = decodeURIComponent(url)
    if (url.indexOf('http') === -1) {
      const nUrl = decodeURIComponent(url)
      let xg = '/'
      if (nUrl.indexOf('/') === 0) {
        xg = ''
      }
      url = `${H5_HYBRID_URL}${xg}${nUrl}`
    }

    const [, ...reset] = url.split('?')
    const p = wx.$.u.isArrayVal(reset) ? reset.join('?') : ''

    if (p) {
      const obj: any = wx.$.u.getUrlAllParams(url)
      if (!isLogin) {
        isLogin = obj?.isLogin || obj?.miniIsLogin || false
      }
      if (!isLocation) {
        isLocation = obj?.isLocation || false
      }
      if (!isPostLogin) {
        isPostLogin = obj?.isPostLogin || false
      }
      if (obj.navigator) {
        const nav = toJSON(obj.navigator)
        if (!wx.$.u.isEmptyObject(nav)) {
          wx.setNavigationBarColor(nav)
        }
      }
    }
    const userState = storage.getItemSync('userState')
    let params = ''
    if (isLogin || (isPostLogin && userState.login)) {
      if ((!userState.login || !userState.token) && options?.fromPage != 'news' && !isWebFirmAuth) {
        this.setData({ options })
        storage.setItemSync('webviewRefresh', 'midLogin')
        this.timer = setTimeout(() => {
          !this.data.toLoginBack
            && toLogin(true, { toLoginPage: true }).catch(() => {
              this.setData({ toLoginBack: true })
            })
          clearTimeout(this.timer)
        }, 50)
        return ''
      }
      /** H5常用参数 */
      params = await getH5OfHeader({ isHeader: 1, isSession: 1, isBottom: 1, isUid: 1, isOpenId: 1, refid, track_seed })
    } else {
      /** H5常用参数 */
      params = await getH5OfHeader({ isHeader: 1, isSession: 0, isBottom: 1, isUid: 1, isOpenId: 0, refid, track_seed })
    }
    if (params) {
      const idx = url.indexOf('?')
      url = `${url}${idx >= 0 ? '&' : '?'}${params}`
    }
    // 获取当前定位信息
    if (isLocation) {
      // 定位
      const { L_IS_GEO_AUTH } = store.getState().storage
      let locationData = null
      if (L_IS_GEO_AUTH !== 1) {
        const gpsInfo = await wx.$.l.handleGps(null, false)
        wx.showLoading({
          title: '正在获取中',
          mask: true,
        })
        locationData = await tryPromise(wx.$.l.getCurrentArea(gpsInfo), null)
      }
      // 获取高德cityId，provinceId
      if (!locationData) {
        wx.showLoading({
          title: '正在获取中',
          mask: true,
        })
        // 获取当前定位信息
        locationData = await tryPromise(wx.$.l.getCurrentArea(), null)
      }
      if (!locationData) {
        return ''
      }
      const idx = url.indexOf('?')
      const locationInfo = {
        cityId: locationData.cityId,
        provinceId: locationData.provinceId,
        areaId: locationData.countyId,
        detail: locationData.location.formattedAddress,
        address: locationData.location.address,
        lng: locationData.location.longitude,
        lat: locationData.location.latitude,
      }
      url = `${url}${idx >= 0 ? '&' : '?'}locationInfo=${encodeURIComponent(JSON.stringify(locationInfo))}`
      wx.hideLoading()
    }
    if (isPostLogin && userState.login) {
      isLogin = true
    }
    url = removeUrlParameter(url, 'navigator')

    /** 防骗指南适配BC分端 */
    if (url.indexOf('fraud-prevention-guide') > 0) {
      url = replaceUrlParameter(url, {
        /** activeKey=0 牛人找活防骗指南 activeKey=1 老板招聘防骗指南 */
        activeKey: store.getState().storage.userChooseRole == 2 ? 0 : 1,
      })
    }
    // 用户角色带给h5
    const { userChooseRole } = store.getState().storage
    const location = storage.getItemSync('userLocation')
    let locationStr = 'location='
    if (location) {
      locationStr = `location=${location}`
    }
    url = `${url}${url.indexOf('?') > -1 ? '&' : '?'}${locationStr}&userrole=${userChooseRole}`
    const sData: any = { osrc: url, options: { ...options, isPostLogin, isLogin } }
    if (isSave) {
      sData.src = url
    }
    // track_seed=m9ttyl8jJXSNTQO&refid=*********&url=/company-authentication
    this.setData(sData)
    setTimeout(() => {
      this.setData({ first: false })
    }, 100)
    return url
  },
  onUnload() {
    const { collectEvent } = this.data
    if (!wx.$.u.isEmptyObject(collectEvent)) {
      const nCollectEvent = { ...this.data.collectEvent }
      delete nCollectEvent.eventName
      wx.$.collectEvent.event(collectEvent.eventName, { ...nCollectEvent })
    }
  },
  onPostMessage(e) {
    const msg = e.detail.data[e.detail.data.length - 1] // data 是多次 postMessage 的参数组成的数组，所以我们获取最后一次

    // 竞招会员页面返回，走后续发布流程在本地存储标识
    // 竞招续费页面返回，不走发布流程，无需在本地保存标识
    if (['JOB_VIE_VIP', 'JOB_GENERAL_VIE_VIP', 'COMMON'].includes(msg.publishType) && this.options.vieJumpType != 'renew') {
      storage.setItemSync('jobVieCheckType', msg.publishType)
    }

    const sData: any = {}
    if (msg.data) {
      sData.message = msg.data
    }
    if (msg.collectEvent) {
      sData.collectEvent = msg.collectEvent
    }
    console.log('msg', msg)
    if (msg.isComplaintOk) {
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 2]
      if (prevPage && prevPage.complaintOk) {
        prevPage.complaintOk()
      }
    }
    this.setData(sData)
  },
  /** 设置当前页面的分享内容 */
  async onShareAppMessage(option) {
    const { message, options } = this.data
    console.log('message', message)
    // const setPath = message.sharePage == 'invite/friends_c_card/Page' ? SHARE_DEFAULT_PATH : ''
    // const res = await setShareInfo(message, option.from, setPath)
    const { sharePage, sharePath } = message
    const res = await getShareInfoByTypeV2({ type: SHARE_CHANNEL.SHARE_WECHAT_FRIEND, sharePage, sharePath, ext: {}, from: option.from })
    if (!wx.$.u.isEmptyObject(message)) {
      if (message.desc) {
        res.desc = message.desc
      }
      if (message.imgUrl) {
        res.imageUrl = message.imgUrl
      }
      if (message.title) {
        res.title = message.title
      }
      if (message.url) {
        const oParem: any = {}
        let param = ''
        if (message.active_id || message.active_code) {
          if (message.url.indexOf('active_id') < 0 && message.url.indexOf('active_id') < 0) {
            oParem.active_id = message.active_id
            oParem.active_code = message.active_code
          }
        }
        if (!wx.$.u.isEmptyObject(oParem)) {
          param = `${Object.keys(oParem)
            .map((ky) => `${ky}=${oParem[ky]}`)
            .join('&')}`
        }
        const idx = message.url.indexOf('?')
        res.path = `${res.path}&url=${encodeURIComponent(decodeURIComponent(message.url + (idx >= 0 ? '&' : '?') + param))}`
        if (options.isPostLogin) {
          if (message.url.indexOf('isPostLogin') < 0) {
            res.path = `${res.path}&isPostLogin=true`
          }
        }
      }
    }

    if (res.path.indexOf('&url=') < 0) {
      const params = Object.keys(options)
        .map((ky) => {
          if (options.isPostLogin && ky == 'isLogin') {
            return ''
          }
          return `${ky}=${options[ky]}`
        })
        .filter((item) => item)
        .join('&')
      const idx = res.path.indexOf('?')
      res.path = `${res.path}${idx >= 0 ? '&' : '?'}${params}`
    }

    console.log('----------res', res)
    // 如果是分享积分权益h5活动页，Title和图片固定
    res.promise && res.promise.then(async (data) => {
      data.path = res.path
      data.title = res.title
      data.imageUrl = res.imageUrl
    })
    return res
  },
})

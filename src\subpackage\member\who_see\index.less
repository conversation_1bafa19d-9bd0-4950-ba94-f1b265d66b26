.page-navbar {
  position: sticky;
  top: 0;
  z-index: 25;
  background-color: #fff;
}

.empty-btn {
  padding: 0 24rpx;
  height: 80rpx;
  background-color: #0092ff;
  border-radius: 8rpx;
  margin: 30rpx auto 0;

  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  letter-spacing: 5rpx;
  text-align: center;
  line-height: 80rpx;
  transition: 0.2s;

  &:active {
    opacity: 0.8;
  }
}

.white-btn {
  background-color: transparent;
  border: 2rpx solid #0092FF;
  color: #0092ff;
}

.advert-class {
  width: 100% !important;
  margin: 16rpx 0 !important;
  border-radius: 0 !important;
}

.img-class{
  width: 200rpx;
  height: 200rpx;
}

.mybrowsercard{
  width: 100%;
  padding: 0 24rpx;
}

.hide-resume {
  color: #ff8904;
  font-size: 26rpx;
  padding: 12rpx 24rpx;
  background: #ffefde;
}

.card{
  border-radius: 24rpx;
  margin: 16rpx 0rpx;
  background-color: #fff;
  overflow: hidden;
}

.custom-card{
  border-radius: 0 !important;
  margin: 0 !important;
}


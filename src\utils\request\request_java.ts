/*
 * @Date: 2022-04-14 21:02:12
 * @Description: 接口请求
 */

import { app } from '@/config/index'
import { codeMessage, codeMessageWhite } from './utils'
import { beforeRequestPoint, afterRequestPoint, responseErrPoint } from './buryingPoint'
import miniConfig from '@/miniConfig/index'
import { getHeaders } from './headers_java'
import {
  handleErrorCode,
  java_errcode,
  handleErrorPopup,
  commonErrCodes,
  commonErrDialogs,
} from './errcode_java'
import { dealDialogApi, getDialogData } from '../helper/dialog/index'
import { REQUEST_VERSION } from '@/config/app'
import { store } from '@/store/index'

/** 获取需要弹出弹框的code码集合 */

function getShowErrCodes(extra): Array<string | number> {
  let showErrCodes = [...commonErrCodes]
  /** 发布招工存草稿箱点位，不需要全局拦截10001的风控弹窗，此处手动过滤 */
  extra && Array.isArray(extra.hideErrCodes) && (showErrCodes = showErrCodes.filter(str => !extra.hideErrCodes.includes(str)))
  if (extra && wx.$.u.isArrayVal(extra.showErrCodes)) {
    return showErrCodes.concat(extra.showErrCodes)
  }
  return showErrCodes
}

/** 获取需要弹出弹框的弹框标识集合 */
function getErrDialogs(extra): Array<string> {
  const showErrDialog = [...commonErrDialogs]
  if (extra && wx.$.u.isArrayVal(extra.showErrDialog)) {
    return showErrDialog.concat(extra.showErrDialog)
  }
  return showErrDialog
}

export const request = async <T>({ type, url, head: customHead = {}, extra, data }): Promise<T> => {
  /** 新老接口选择器 */
  const URL = `${app.REQUEST_JAVA_URL}${url}`.trim()
  const requestData: any = { ...JSON.parse(JSON.stringify(data || {})) }
  if (extra && extra.isWeToken) {
    requestData.wechatToken = miniConfig.token
  } else if (!extra || !extra.isNoToken) {
    requestData.wechat_token = miniConfig.appid
  }
  const customHeads = extra?.headers ? { ...customHead, ...extra.headers } : customHead
  const headers = await getHeaders(requestData, customHeads)
  /** 首屏加载优化-defer(true：使用该属性的请求被视为对页面首屏渲染无影响的请求，会被暂存至延迟队列中等待页面首屏渲染完成后进行间隔发送；false或不携带此参数，均为正常优先级，即时发送) */
  const defer = ENV_IS_SWAN && extra && extra.defer ? { defer: extra.defer } : {}
  /** 请求接口前的埋点（部分接口) */
  beforeRequestPoint({ url: URL, requestData, headers })
  return new Promise((resolve, reject) => {
    wx.request({
      url: ENV_MODE === 'dev' && extra?.mock ? `http://yapi.3pvr.com/mock/26${url}` : URL,
      // 新增自定义header
      header: headers, // extra?.headers ? { ...headers, ...extra.headers } : headers,
      data: requestData,
      method: type,
      timeout: extra?.timeout || +ENV_REQUEST_TIMEOUT,
      ...defer,
      success(response) {
        success(URL, response, resolve, reject, extra)
      },
      fail(err) {
        ENV_DEVELOPMENT !== 'PRO' && devNotify({ URL, requestData })
        responseErrPoint({ url: URL, err, requestType: 'JAVA' })
        // handleSystemMaintenance(500)
        reject(err)
      },
    })
  })
}

/** 接口请求成功 */
async function success(url, response, resolve, reject, extra) {
  if (!response?.statusCode) {
    sentryReport(url, 0, '请求失败', '', { num: 1 })
    reject(response)
    return
  }
  const { data } = response
  const statusCode = Number(response.statusCode)
  const showErrCodes = getShowErrCodes(extra)
  const showErrDialog = getErrDialogs(extra)
  const dialogData = getDialogData(data) // 整理弹框数据

  let errcode = data.code
  //! 注意：code=0 时，data必然是一个封装对象(请求成功)；code !=0 时 data 为null
  if (!errcode) {
    errcode = data.data ? data.data.error_code : data.error_code
  }

  // 是否匹配到了code中的状态码
  const isShowErrCodes = showErrCodes.includes(`${errcode}`)
  // 是否匹配到了弹框标识
  const isShowErrDialog = showErrDialog.includes(`${dialogData.dialogIdentify}`)
  /** 请求接口之后的埋点（部分接口) */
  afterRequestPoint({ url, responseData: data })
  // 返回状态码不为200情况
  if (statusCode != 200) {
    const errMsg: string = codeMessage[statusCode]
    sentryReport(url, statusCode, errMsg, (data && data.askId), { num: 2 })
    // const isOk = await handleSystemMaintenance(statusCode)
    // if (!isOk) {
    !codeMessageWhite.includes(statusCode) && !(extra && extra.hideHTTPMsg) && wx.$.msg(`请求错误：${url} - ${errMsg}`, 1000)
    // }
    reject(data)
    return
  }
  // 判断系统错误码
  data.dialogData = ''
  // if (data?.code && data.code != 0) {
  //   const isOk = await handleSystemMaintenance(data?.code)
  //   if (isOk) {
  //     sentryReport(url, data.code, data.message, data.askId)
  //     reject(data)
  //     return
  //   }
  // }
  if (java_errcode?.includes(data?.code)) {
    data.dialogType = 'java_errcode'
    ![401, 601].includes(data.code) && sentryReport(url, data.code, data.message, data.askId, { num: 3 })
    // hideErrCodes，接口自己处理的状态码，不做handleErroCode处理
    const showMsg = await handleErrorCode(data, url, extra)
    if (!showMsg) {
      reject(data)
      return
    }
  } else if (isShowErrCodes) {
    // isFkReject:为true则返回code 为10001时，满足弹框弹出并reject
    const { isFkReject } = extra || {}
    // 处理请求时接口传入的需要处理的状态码
    const showMsg = await handleErrorPopup(dialogData)
    data.dialogType = 'showErrCodes'
    if (!showMsg) {
      if (!isFkReject && data && data.code === 10001) {
        return
      }
      reject(data)
      return
    }
  } else if (isShowErrDialog) {
    data.dialogType = 'showErrDialog'
    // 处理请求时接口传入的需要处理的弹框标识
    const showMsg = await handleErrorPopup(dialogData)
    if (!showMsg) {
      reject(data)
      return
    }
  } else if (dialogData.dialogIdentify) {
    // 通用弹框的数据
    data.popup = await dealDialogApi(dialogData)
    /** 有这个字段对象 代表的是未匹配到isShowErrCodes，isShowErrDialog 这两个字段时才会返回，否则为undefined */
    data.dialogData = dialogData
  }
  if (data && data.code >= 400 && data.code < 600) {
    sentryReport(url, data.code, data.message, data.askId, { num: 4 })
  }
  resolve(data)
}

function sentryReport(url, statusCode, message, askId, ext?) {
  const { num } = ext || {}
  console.log('sentryReport', url, statusCode, message)
  wx.$.collectEvent.event('InterfaceResponseErr', {
    /** 接口名 */
    interface_name: url,
    requestType: 'SERVER',
    statusCode,
    message,
    askId,
    num: num || 0,
  })
}

/** 测试环境通知 */
function devNotify({ URL, requestData }) {
  const { user } = store.getState()
  wx.request({
    url: 'https://open.feishu.cn/open-apis/bot/v2/hook/710b3a1c-4640-411b-8419-b46e03e6e871',
    method: 'POST',
    header: {
      'Content-Type': 'application/json',
    },
    data: {
      msg_type: 'interactive',
      card: {
        config: {
          wide_screen_mode: true,
        },
        i18n_elements: {
          zh_cn: [
            {
              tag: 'markdown',
              content: `**接口异常**
登录用户：${user.userInfo.username} | ${user.userInfo.uuid}
小程序名：${miniConfig.name}
页面路径：${wx.$.u.getCurrentPage().route}
当前环境：${ENV_DEVELOPMENT}
系统平台：${wx.getSystemInfoSync().platform}
接口版本：${REQUEST_VERSION}
接口地址：${URL}
请求参数：${JSON.stringify(requestData)}
            `,
            },
          ],
        },
      },
    },
  })
}

/*
 * @Date: 2022-06-22 18:01:49
 * @Description: header头部
 */
import { app } from '@/config/index'
import { storage } from '@/store/index'
import miniConfig from '@/miniConfig/index'
import CryptoJS from '@/lib/crypto/index'
import { common } from '@/utils/tools/index'
import { getHeaderSeries } from './utils'

const platformList = {
  /** 微信小程序 */
  weapp: 'wx_mini',
  /** 百度小程序 */
  swan: 'baidu_mini',
  /** 字节小程序 */
  tt: 'bd_mini',
}

/** 服务器的签名秘钥 */
const SECRET_SIGN = '*js1(Uc_m12j%hsn#1o%cn1'

/** json对象key排序 */
// eslint-disable-next-line consistent-return
function sortObj(obj) {
  if (obj === null) {
    return obj
  }
  if (Array.isArray(obj)) {
    return [...obj].sort().reduce((acc, item) => {
      if (typeof item === 'object') {
        acc.push(sortObj(item))
      } else {
        acc.push(item)
      }
      return acc
    }, [])
  }
  if (typeof obj === 'object') {
    return Object.keys(obj)
      .sort()
      .reduce((acc, key) => {
        if (typeof obj[key] === 'object') {
          // eslint-disable-next-line no-param-reassign
          acc[key] = sortObj(obj[key])
        } else {
          // eslint-disable-next-line no-param-reassign
          acc[key] = obj[key]
        }
        return acc
      }, {})
  }
}

/** json对象转url的params */
// eslint-disable-next-line consistent-return
function stringifyObj(t) {
  if (t == null) {
    return t
  }
  // 数组
  if (Array.isArray(t)) {
    const list = [...t].sort().reduce((acc, item) => {
      if (typeof item === 'object') {
        acc.push(sortObj(item))
      } else {
        // eslint-disable-next-line no-param-reassign
        acc += item
      }
      return acc
    }, [])
    return JSON.stringify(list)
  }
  // 是对象 非数据
  if (typeof t === 'object') {
    return Object.keys(t)
      .sort()
      .reduce((acc, key) => {
        let newAcc = `${acc}${key}=${t[key]}&`
        if (typeof t[key] === 'object') {
          newAcc += `${acc}${key}=${JSON.stringify(sortObj(t[key]))}&`
        }
        return newAcc
      }, '')
  }
}

/** 参数加密 */
async function getSign(params, timestamp, nonce) {
  const newParams = sortObj({ ...params, timestamp, nonce })
  const signString = stringifyObj(newParams) + SECRET_SIGN
  const c = await CryptoJS()
  return c.SHA256(signString).toString()
}
/**
 * 获取header头
 * @param customHead 自定义header
 * @param requestData 请求参数
 * @returns headers
 */
export async function getHeaders(requestData = {}, customHead = {}) {
  const trackSeed = storage.getItemSync('track_seed_share')
  const refid = storage.getItemSync('sourceCode')
  const userInfo = storage.getItemSync('userState')
  const nonce = Math.round(Math.random() * 999999)
  const timestamp = `${Math.floor(new Date().getTime() / 1000)}`
  const sign = await getSign(requestData, timestamp, nonce)
  const systemInfo = common.getSystemName()
  /* 参数请求头 */
  return {
    /** 工种版本号 */
    occversion: 2,
    /** 分享小程序的trackSeed */
    trackSeed,
    /** 用户分享的refid存入本地 */
    refid,
    /** 请求参数传输类型 */
    requestType: 'form',
    /** 来源端 1-鱼泡网 */
    business: '1',
    /** content-type */
    'content-type': 'application/json',
    /** 版本号 */
    version: app.REQUEST_VERSION,
    /** 版本号 */
    versionmini: app.versionmini,
    /** 版本号 */
    wechat_token: miniConfig.token,
    /** 终端 */
    system_type: getHeaderSeries(),
    /** 令牌时间 */
    token_time: userInfo.tokenTime || '',
    /** 用户id */
    user_id: userInfo.userId || '',
    /** 老令牌 */
    // token: userInfo.token || '',
    /** 新令牌 */
    singletoken: userInfo.token || '',
    /** 用户uuid */
    uuid: userInfo.uuid || '',
    /** 跳系统升级页面用 */
    uid: userInfo.userId || '',
    /** 版本号 */
    mid: userInfo.userId || '',
    /** 请求时间 */
    time: timestamp,
    /** 签名时间戳 */
    timestamp,
    /** 应用版本，应用版本。 */
    appVersion: app.REQUEST_VERSION,
    /** 操作系统 ios, android, windows, macos, linux */
    system: systemInfo.system,
    /** 系统版本 */
    systemVersion: systemInfo.systemVersion,
    /** 随机数，[1, 999999]之间的整数 */
    nonce,
    /** wx_mini: 微信小程序 bd_mini: 字节小程序 baidu_mini: 百度小程序 qq_mini: QQ小程序 */
    platform: platformList[ENV_MINI_TYPE],
    /** 用户登录态token */
    token: userInfo.token || '',
    /** 用户数据签名 */
    sign,
    /** 业务区分 */
    reqsource: 'YPZP',
    packageName: miniConfig.appid,
    ...customHead,
  }
}

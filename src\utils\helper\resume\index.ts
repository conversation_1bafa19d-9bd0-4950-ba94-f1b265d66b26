/*
 * @Author: ji<PERSON><PERSON>
 * @Date: 2022-02-10 11:11:42 fetchResumeExist
 * @Description: 找活相关业务可复用代码
 */
import { getResumeInfoMe } from './utils'
import { dispatch, actions, store, storage } from '@/store/index'

import { common } from '@/utils/tools/index'
import { tryPromise } from '@/utils/tools/common/index'
import { MyResumeDetails, resumeDetailsDef } from '@/store/model/storage/defData'
import { dealDialogApi } from '../dialog/index'

function getOccupationText(data) {
  const { positionType, occupation } = data
  let text = ''
  if (occupation && occupation.occName && occupation.occName.length) {
    text = occupation.occName
    // 默认按订单类处理
    if (positionType == 2 && occupation.mode == 2) {
      text = `兼职・${occupation.occName}`
    }
  }
  return text || ''
}

// 显示数据拼接
const userDescTextAppend = (data) => {
  const sex = data.userInfo?.sex == 2 ? '女' : '男'
  const age = data.userInfo?.age || ''
  const nation = data.userInfo?.nation || ''
  const { workingYears } = data
  let eduBackground = ''
  if (Array.isArray(data.eduExp)) {
    if (data.eduExp?.length > 0) {
      eduBackground = data?.eduExp?.[0]?.eduBackgroundName || ''
    }
  } else {
    eduBackground = data.eduExp?.eduBackgroundName || ''
  }
  // 性别必定显示
  let resultString = sex
  // 年龄
  if (age) {
    resultString = `${resultString}・${age}岁`
  }
  // 民族
  if (nation) {
    resultString = `${resultString}・${nation}`
  }
  // 工龄
  if (workingYears) {
    resultString = `${resultString}・${workingYears}`
  }
  // 学历
  if (eduBackground) {
    resultString = `${resultString}・${eduBackground}`
  }
  if (resultString.startsWith('・')) {
    return resultString.replace('・', '')
  }
  return resultString
}

/**
 * @description 4.0.0 找活卡片数据清洗
 */
export const resumeCardFormatDataV4 = (data) => {
  return data.map((item) => {
    return {
      ...item,

      // rightInfo: { ...(item.rightInfo || {}), showTelChatButton: false, showImChatButton: true },
      /** 名片ID (简历大列表用到了 resumeId) */
      id: item?.resumeId || item?.id || item?.resumeSubUuid,
      guid: common.guid(),
      sex: item.userInfo?.sex == 2 ? '女' : '男',
      /** 经纬度 */
      location: item.longitude && item.latitude ? `${item.longitude},${item.latitude}` : '',
      /** 名片uuid */
      uuid: item.resumeSubUuid,
      dial: item.rightInfo && item.rightInfo.hasTelRight,
      /** 头像 */
      headPortrait: item.userInfo?.headPortrait || 'https://cdn.yupaowang.com/yupao_mini/yp_mini_zp_head_photo.png',
      /** 期望工资 */
      forwardSalary: item.expectSalary || '',
      /** 工种 */
      occupation: item.occupation,
      occupationStr: getOccupationText({ positionType: item.positionType, occupation: item.occupation }),
      /** 名片用户名称 */
      name: item.userInfo?.userName || '先生',
      userDesc: userDescTextAppend(item),
      /** 实名状态 2为已实名 */
      realNameStatus: item.userInfo?.realNameStatus || 0,
      /** 已加急 */
      isTop: item.recommendFlag && (item.recommendFlag == 1 || item.recommendFlag == 2),
      online: item.userInfo?.isOnline,
      activeStatusText: item.userInfo?.activeStatusText || '',
      recallExtendTagList: item.recallExtendTagList || [],
      applyPreference: item.applyPreference || [],
      introduce: item.personalAdvantage || '',
    }
  })
}

/**
 * @description 收藏的牛人-名片数据清洗
 */
export const collectedResumeWorkerCardFormatData = (data) => {
  return data.map((item, index) => {
    return {
      ...item,
      /** 数据ID */
      id: item?.id,
      guid: common.guid(),
      /** 头像 */
      headPortrait: item.userInfoResp?.headPortrait || 'https://cdn.yupaowang.com/yp_family_business_mini/yp_mini_ypzp_default.png',
      /** 子名片uuid */
      resumeSubUuid: item?.mappingSubInfo?.uuid,
      /** 期望工资 */
      forwardSalary: item?.mappingSubInfo?.expectSalary || '',
      /** 求职偏好 */
      occupationStr: item?.mappingSubInfo?.occupation?.occupationName || '',
      /** 名片用户名称 */
      name: item?.userInfoResp?.userName || '先生',
      userDesc: userDescTextAppend({ ...item, userInfo: item?.userInfoResp }),
      /** 实名状态 2为已实名 */
      isRealNameStatus: item?.userInfoResp?.realNameStatus == 2,
      /** 个人介绍 */
      introduce: item?.introduce || '',
      /** 工种标签 */
      positionPreference: item?.mappingSubInfo?.positionPreference || [],
    }
  })
}

/** ========================== v4.0.0 ========================== */
let refreshResumeTime = 0 // 刷新找活名片时间
/** @description 获取找活名片信息 并更新本地数据
 * @param resumeUuid 找活名片的UUID-【虽然是可选,最好每次都带上】 - 如果resumeUuid===true,则会去请求fetchResumeExist接口获取
 * @param deepTime 刷新时间间隔单位分钟
 */
export function getResumeDetails(resumeUuid?: string | true, deepTime = 0): Promise<MyResumeDetails> {
  let resumeUuidDiy = ''
  if (resumeUuid !== true) {
    resumeUuidDiy = resumeUuid || wx.$.u.getObjVal(store.getState().resume.resumeExist, 'resumeUuid')
    const now = new Date().getTime()
    if (deepTime && deepTime > 0) { // 指定多少分钟内不重复请求
      const tempTime = Math.abs(refreshResumeTime - now)
      if (tempTime < deepTime * 60 * 1000) {
        refreshResumeTime = now
        const myResumeDetails = wx.$.u.deepClone(store.getState().storage.myResumeDetails)
        return Promise.resolve(myResumeDetails)
      }
      refreshResumeTime = now
    }
  }
  return new Promise(async (resolve) => {
    const { userState } = store.getState().storage
    const { resumeExist } = store.getState().resume
    if (userState.login) {
      // 这里的resumeUuid没有值的时候，就会去请求接口获取
      if (!resumeUuidDiy) {
        resumeUuidDiy = (await dispatch(actions.resumeActions.fetchResumeExist(true))).resumeUuid
      }
      if (!resumeUuidDiy) {
        dispatch(actions.storageActions.removeItem('myResumeDetails'))
        resolve({ ...resumeDetailsDef } as any)
        return
      }
      const details = await getResumeInfoMe(resumeUuidDiy)
      dispatch(actions.storageActions.setItem({
        key: 'myResumeDetails',
        value: details,
      }))
      if (details && details.basicResp && details.basicResp.resumeUuid) {
        const { basicResp } = details
        // 简历存在的时候
        dispatch(actions.resumeActions.setState({
          resumeExist: {
            ...resumeExist,
            exist: true,
            workStatus: basicResp.workStatus,
            checkStatus: basicResp.checkStatus,
            resumeId: basicResp.resumeId,
            resumeUuid: basicResp.resumeUuid,
            hideStatus: details.hideStatus,
          },
        }))
      }
      //   .setItem({
      //   key: 'myResumeDetails',
      //   value: details,
      // }))
      resolve({ ...details } as any)
    } else {
      dispatch(actions.storageActions.removeItem('myResumeDetails'))
      resolve({ ...resumeDetailsDef } as any)
    }
  })
}

type refreshMyInfoData = {
  showElem?: boolean
  isStore?: boolean
}
/** 刷新我的在线简历数据
 * @param type 刷新类型
 * @description 用于刷新找活名片数据
 *  - refreshOne 刷新第一屏
 *  - refreshTwo 刷新第二屏
 *  - refresh 全部刷新
 * @param data 数据
 *  - showElem 是否显示逻辑
 */
export async function refreshMyInfo(type: 'refreshOne' | 'refreshTwo' | 'refresh' = 'refresh', data: refreshMyInfoData = { showElem: false, isStore: false }) {
  const { page, prePage, thatPage } = wx.$.r.getPage('subpackage/resume/publish/index')

  if (data.isStore) {
    await getResumeDetails()
  }
  if (thatPage && thatPage.onOtherPage) {
    // 当前页面刷新
    thatPage.onOtherPage(type, data)
  }
  if (prePage && prePage.onOtherPage) {
    // 刷新上一个页面
    prePage.onOtherPage(type, data)
    // return
  }
  if (page && page.onOtherPage) {
    // 回到页面刷新
    page.onOtherPage(type, data)
  }
}

/** @description 更新和获取名片全局配置接口 */
export async function getGlobalDefaultConfig(isUpdate = false) {
  let { globalConfig = {} } = store.getState().resume
  globalConfig = await dispatch(actions.resumeActions.fetchGlobalConfig(isUpdate))
  return globalConfig
}
/** @description 是否含有找活名片
 * @param isUpdate 是否更新model缓存
 */
export async function fetchResumeExist(isUpdate = false) {
  return dispatch(actions.resumeActions.fetchResumeExist(isUpdate))
}

interface VerifyUpgradeResumeCardStatus {
  /** 是否需要验证找活名片是否存在 */
  isVerifyExist: boolean
  /** 是否验证找活名片状态为审核中 */
  isVerifyAudit: boolean
  /** 是否验证找活名片状态为审核失败 */
  isVerifyAuditFail: boolean
}
/**
 * @name 判断找活名片信息状态并进行对应弹窗
 * @param isAuditResumeInfoExists 是否需要验证找活信息不存在情况(并进行弹窗提示)
 * @param options.isVerifyExist 是否需要验证找活名片是否存在
 * @param options.isVerifyAudit 是否验证找活名片状态为审核中
 * @param options.isVerifyAuditFail 是否验证找活名片状态为审核失败
 * @returns true 验证失败停止后续操作 false 继续执行后续操作
 */
export async function isVerifyResumeCardStatusService(options: Partial<VerifyUpgradeResumeCardStatus>) {
  const { isVerifyExist = false, isVerifyAudit = false, isVerifyAuditFail = false } = options || {}
  try {
    // 判断名片是否存在( v4.0.0 提示文案前端写死->具体看需求)
    const resumeCardStatusInfo = await fetchResumeExist(true)
    // 验证找活信息审核中
    if (isVerifyAudit && resumeCardStatusInfo.checkStatus == 1) {
      wx.$.msg('你的简历正在审核中，审核通过后才可加急！')
      return false
    }
    // 验证找活名片审核失败
    if (isVerifyAuditFail && resumeCardStatusInfo.checkStatus == 3) {
      await statusPopErrJump(resumeCardStatusInfo.checkStatus)
      return false
    }
    // 找活信息不存在
    if (isVerifyExist && !resumeCardStatusInfo.exist) {
      showNoResumeCardModel()
      return false
    }
    return true
  } catch (error) {
    return false
  }

  async function showNoResumeCardModel() {
    try {
      await wx.$.confirm({ content: '您的简历不存在，发布名片获得老板主动联系！', cancelText: '知道了', confirmText: '去发布' })
      wx.$.r.replace({ path: '/subpackage/resume/resume_publish/index?origin=complete' })
      return false
    } catch (error) {
      console.error(error)
      wx.$.r.back()
      return false
    }
  }
}

/**
 * 如果名片审核失败则跳转到我的找活名片页，执行页面的pageEventOther方法
 * 如果当前页是找活名片页，则执行publishResumeTop方法
 *  */
export async function statusPopErrJump(checkStatus, scrollTop = 0) {
  if (checkStatus == 3) { // 审核失败->前端写死弹窗
    // 获取上一个页面的route
    const { page } = wx.$.r.getPrevPage(2)
    await wx.$.confirm({ content: '您的找活名片未通过审核，请及时修改，审核通过后可继续操作！', confirmText: '立即修改', cancelText: '知道了' }).catch(() => { })
    if (page && page.route === 'subpackage/resume/publish/index') {
      page.pageEventOther && page.pageEventOther()
      wx.$.r.back()
      return false
    }
    wx.$.r.replace({
      path: '/subpackage/resume/publish/index',
      query: { source: 'refreshAndTopEditAlert' },
    })
    return false
  }
  return true
}

/** 我的简历页，简历审核未通过弹框 */
export async function statusErrPopInfo(isTop = true) {
  const popup = await dealDialogApi({
    dialogIdentify: 'zp_shuaxinshenheweitongguojianli',
  })
  if (popup) {
    await wx.$.showModal(popup).then(async (res) => {
      const { btnIndex } = res || { btnIndex: 0 }
      if (btnIndex == 1) {
        isTop && wx.pageScrollTo({ scrollTop: 0, duration: 150 })
      }
      return res
    })
    return
  }
  await wx.$.confirm({
    content: '您的简历未通过审核，请及时修改，审核通过后可继续操作！',
    confirmText: '立即修改',
    cancelText: '知道了',
  }).then((res) => {
    isTop && wx.pageScrollTo({ scrollTop: 0, duration: 150 })
    return res
  })
}

/** 获取列表筛选的城市和工种
 * @param {Object} detail 接口返回的请求数据
 */
export const getFilterData = async (): Promise<{
  /** 筛选的城市id数据 */
  area_id: number[]
  /** 筛选的工种id数据 */
  classify_id: number[]
}> => {
  const pages = getCurrentPages()
  const arr = [pages[pages.length - 1].route, pages.length >= 2 && pages[pages.length - 2].route]
  const isSearch = arr.includes('subpackage/resume/listSearchResultsPage/index')// 是否搜索结果页
  const { selectPositionTabId, searchPageSltedJob } = store.getState().storage
  const { selectItem } = selectPositionTabId
  const filterData = {
    area_id: [],
    classify_id: [],
  }
  if (isSearch) {
    const { userLocationCity } = store.getState().storage
    const { resumeCityObj, id, resumeSearchCityObj } = userLocationCity || {}
    const { id: resId, citys } = resumeSearchCityObj || resumeCityObj || {}
    const { selectOccList } = searchPageSltedJob || {}
    if (wx.$.u.isArrayVal(citys)) {
      filterData.area_id = citys.map(item => Number(item.id))
    } else if (resId || id) { // 地址
      filterData.area_id = [Number(resId || id)]
    }
    if (wx.$.u.isArrayVal(selectOccList)) {
      filterData.classify_id = selectOccList
    }
  } else {
    const { selectOccList, selectAreas } = selectItem || {} as any
    if (wx.$.u.isArrayVal(selectOccList)) {
      filterData.classify_id = selectOccList
    }
    if (wx.$.u.isArrayVal(selectAreas)) {
      filterData.area_id = selectAreas
    }
  }

  return filterData
}

/**
 * @description 自动生成简历弹窗
 * @param callback 回调函数(打开弹窗返回模板数据)
 * @param isOnShow 是否是onShow或搜索页触发
 */
export const autoResumePop = async (callback, isOnShow = false) => {
  const { login } = store.getState().storage.userState
  const isRecruitClassify = storage.getItemSync('isRecruitClassify')
  const { isAutoResumePop } = store.getState().index
  if (!login || isAutoResumePop || (!isRecruitClassify && !isOnShow)) {
    return false
  }
  const { data } = await tryPromise(wx.$.javafetch['POST/resume/v1/resumePerfect/autoGenerateResumePopup']())
  dispatch(actions.recruitIndexActions.setState({ recruitCallFrom: '' }))
  if (data && data.template) {
    callback && callback(data.template)
    return true
  }
  return false
}

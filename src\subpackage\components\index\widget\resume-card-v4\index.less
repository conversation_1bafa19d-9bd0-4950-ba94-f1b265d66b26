.resume-card-component {
  position: relative;
  margin: 16rpx 24rpx;
}

.no_margin_bottom {
  margin-bottom: 0rpx;
}

.discount-call-flag {
  width: 114rpx;
  height: 36rpx;
  margin-right: 8rpx;
}

.viewed {
  .resume-header .header-left .header-left-top {
    > .name {
      color: rgba(0, 0, 0, 0.45) !important;
    }
  }

  .resume-mid-info {
    color: rgba(0, 0, 0, 0.45) !important;
  }
}

.resume-container {
  border-radius: 16rpx;
  background-color: #fff;
  color: rgba(0, 0, 0, 0.65);
  font-size: 30rpx;
  padding: 24rpx;

  .resume-header {
    width: 100%;
    display: flex;

    .head-tag-box {
      flex: 1;
      margin: -4rpx;
      padding: 0 8rpx;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      height: 46rpx;
      overflow: hidden;
    }
    .head-tag {
      margin: 4rpx;
      height: 36rpx;
      min-width: 36rpx;
      display: inline-flex;
      align-items: center;
      padding: 0 8rpx;
      border-radius: 8rpx;
      color: @primary-color;
      border: 2rpx solid @primary-color;
      background: #e0f3ff;
      font-size: 20rpx;
      line-height: 28rpx;
      font-weight: bold;
      .tag-dot {
        white-space: nowrap;
        width: 8rpx;
        height: 8rpx;
        background: #06b578;
        border-radius: 100%;
        margin-right: 4rpx;
      }
      &.tag-red {
        color: @error-color;
        border: 2rpx solid @error-color;
        background: #ffebec;
      }
      &.tag-gray {
        color: rgba(0, 0, 0, 0.25);
        border: 2rpx solid rgba(0, 0, 0, 0.25);
        background: #f5f6fa;
      }
      &.tag-green {
        color: #06b578;
        border: 2rpx solid #06b578;
        background: #dff2ec;
      }
    }

    .header-left {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: calc(100% - 120rpx);

      .header-left-top {
        display: flex;
        align-items: center;

        > .name {
          color: rgba(0, 0, 0, 0.85);
          font-weight: 500;
          font-size: 34rpx;
          padding-right: 8rpx;
          // max-width: 200rpx;
          .ellip();
        }

        > .real-name-tag {
          white-space: nowrap;
          color: rgba(0, 146, 255, 1);
          font-weight: 500;
          font-size: 26rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 1);
          padding: 0 8rpx;
          border-radius: 8rpx;
          height: 44rpx;
          border: 2rpx solid rgba(224, 243, 255, 1);
          margin-right: 8rpx;
        }

        > .top-tag {
          white-space: nowrap;
          color: rgba(255, 137, 4, 1);
          font-weight: 500;
          font-size: 26rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgb(255, 239, 222);
          padding: 0 8rpx;
          border-radius: 8rpx;
          height: 44rpx;
          margin-right: 8rpx;
        }

        > .dot {
          white-space: nowrap;
          width: 12rpx;
          height: 12rpx;
          background: rgba(6, 181, 120, 1);
          border-radius: 6rpx;
          margin: 0 8px;
          margin-right: 8rpx;
        }

        > .text {
          color: rgba(6, 181, 120, 1);
          font-size: 26rpx;
        }

        .not-online {
          color: rgba(0, 0, 0, 0.45);
        }
      }

      .header-left-bottom {
        width: 100%;
        overflow: hidden;
        color: rgba(0, 0, 0, 0.45);
        font-size: 26rpx;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .header-right {
      margin-left: 24rpx;
      flex-shrink: 0;
      height: 96rpx;
      position: relative;
      border-radius: 48rpx;
      overflow: hidden;
      > .image {
        width: 96rpx;
        height: 100%;
        border-radius: 48rpx;
        overflow: hidden;
        flex-shrink: 0;
      }
    }
  }

  .vague {
    position: absolute;
    top: 0;
    left: 0;
    width: 96rpx;
    height: 96rpx;
    border-radius: 48rpx;
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(12rpx);
  }


  .resume-mid-info {
    width: 100%;
    display: flex;
    padding-top: 16rpx;
    color: rgba(0, 0, 0, 0.85);

    font-size: 28rpx;

    .resume-mid-info-left {
      width: 100%;
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
    }

    .occupation-str {
      width: auto;
      .ellip();
    }

    .salary-text {
      white-space: nowrap;
    }

    .resume-mid-info-right {
      flex-shrink: 0;
    }
  }

  .resume-tags {
    width: 100%;
    display: flex;
    margin-top: 16rpx;
    flex-wrap: wrap;
    height: 44rpx;
    overflow: hidden;

    > .primary-tag {
      flex-shrink: 0;
      padding: 0 12rpx;
      border-radius: 8rpx;
      background: rgba(224, 243, 255, 1);
      margin-right: 12rpx;
      display: flex;
      height: 44rpx;
      align-items: center;
      justify-content: center;
      color: rgba(0, 146, 255, 1);
      font-size: 26rpx;
    }

    > .default-tag {
      flex-shrink: 0;
      padding: 0 12rpx;
      border-radius: 8rpx;
      background: rgba(245, 246, 250, 1);
      margin-right: 12rpx;
      display: flex;
      height: 44rpx;
      align-items: center;
      justify-content: center;
      color: rgba(0, 0, 0, 0.65);
      font-size: 26rpx;
    }
  }

  .resume-bottom {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 16rpx;
    font-size: 26rpx;
    color: rgba(0, 0, 0, 0.45);

    .resume-bottom-button {
      flex-shrink: 0;
      padding: 0px 12rpx;
      border-radius: 8rpx;
      background: rgba(224, 243, 255, 1);
      display: inline-flex;
      justify-content: center;
      height: 48rpx;
      color: rgba(0, 146, 255, 1);
      font-weight: 500;
      font-size: 24rpx;
      margin-left: 16rpx;
      align-items: baseline;

      > .icon {
        margin-right: 5rpx;
        line-height: 48rpx;
      }
      .text {
        line-height: 48rpx;
      }
    }

    .introduce-content {
      display: -webkit-box;
      overflow: hidden;
      word-break: break-all;
      line-clamp: 2;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }
}

.filter {
  filter: blur(12rpx);
  -webkit-filter: blur(12rpx);
}


.hasCooperationTag {
  position: relative;
  margin-left: 8rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  background-color: rgba(255, 238, 222, 1);
  color: rgba(255, 137, 3, 1);
  font-size: 24rpx;
  font-weight: bold;
}

.hasCooperationTagIcon {
  width: 30rpx;
  height: 24rpx;
  margin-right: 6rpx;
}


.hasComplainTag {
  margin-left: 8rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  background-color: rgba(245, 246, 250, 1);
  color: rgba(0, 0, 0, 0.25);
  border: 1rpx solid rgba(0, 0, 0, 0.25);
  font-size: 20rpx;
}

.card-bottom{
  width: 100%;
  padding: 32rpx 20rpx;
  display: flex;
  justify-content: space-between;
  border-top:  1rpx solid rgba(233, 237, 243, 1);
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.45);
}

.card-bottom-left{
  display: flex;
  max-width: 508rpx;
}

.text{
  // white-space: nowrap;
  flex-shrink: 0;
}

.job-title{
  flex-shrink: 1;
  color: #0092ff;
  .ellip(1);
}

.card-bottom-right{
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.45);
  margin-left: 32rpx;
}
/*
 * @Date: 2022-02-18 11:35:58
 * @Description: 分享相关函数
 */

import { SHARE_CHANNEL, SHARE_DEFAULT_PATH, SHARE_BTN_LOCATION } from '@/config/share'
import { store, storage, dispatch, actions, messageQueue } from '@/store/index'
import miniConfig from '@/miniConfig/index'
import { MiniShareInfoParams, MiniShareInfoResult, MiniShareTimeLine } from './index.d'
import { urlEncodeParams } from '@/utils/tools/common/index'
import { transFromRuntime } from '@/utils/request/utils'

type ICodeLoginParams = YModels['POST/account/v1/login/codeLogin']['Req']

/**
 * @params: null
 * @return: 小程序分享参数
 * @description: 获取小程序被分享时默认携带的信息(数据优先级--json配置>页面传参>默认参数)
 * @tips: https://developers.weixin.qq.com/miniprogram/dev/reference/api/Page.html#onShareAppMessage-Object-object
 */
export const getShareInfo = async (options: MiniShareInfoParams): Promise<MiniShareInfoResult> => {
  const { title = '' } = options || {}
  let { path = '', params = {} } = options || {}
  const { imageUrl, isCurPageImg = false, canvasShareImg = '', from, needTrackSeed = true } = options || {}

  // ENV_IS_TT字节

  // 如果不存在path， 则取当前path
  if (!path) {
    const thePage = wx.$.r.getCurrentPage()
    params = { ...params, ...thePage.options }
    path = `/${thePage.route}`
  }

  const shareInfo = getShareConfigInfo()
  // 如果存在用户信息，则自动加上必要信息
  const { userId } = store.getState().storage.userState
  if (userId && path.indexOf('&refid=') < 0) {
    params.refid = userId
  }

  path += urlEncodeParams(params, path.indexOf('?') >= 0)

  // 返回默认分享信息
  let data: any = {
    title,
    path,
  }
  if (imageUrl) {
    data = {
      title,
      path,
      imageUrl,
    }
  }

  // 自定义的canvasShareImg优先：
  if (canvasShareImg) {
    data = {
      title,
      imageUrl: canvasShareImg,
      path,
    }
  }
  const shareCardData = storage.getItemSync('shareCardData')
  // 分享弹窗（inviteWorkers）弹出点击弹窗按钮分享时，用分享弹窗获取的分享信息
  if (from == 'button' && shareCardData.isShareModal) {
    data = {
      title: shareCardData.title,
      imageUrl: shareCardData.img,
      path: shareCardData.path,
    }
  } else if (needTrackSeed && store.getState().storage.userState.login) {
    await messageQueue((state) => {
      return state.storage.shareTrackSeed
    }, true, 2000).catch(() => { })
    const shareTrackSeed = wx.$.u.deepClone(storage.getItemSync('shareTrackSeed'))
    if (shareTrackSeed) {
      params.track_seed = shareTrackSeed
      dispatch(actions.storageActions.setItem({ key: 'shareTrackSeed', value: '' }))
    }
    data.path += urlEncodeParams(params, path.indexOf('?') >= 0)
  }

  let shareData = {
    path: data.path,
    title: '找工作，上鱼泡  |  海量高薪职位等你来！',
    imageUrl: 'https://cdn.yupaowang.com/other/share-img.jpg',
  }

  !data.title && delete data.title
  !data.imageUrl && delete data.imageUrl
  !shareInfo.title && delete shareInfo.title
  !shareInfo.imageUrl && delete shareInfo.imageUrl
  const res = await wx.$.javafetch['GET/share/v1/setting/queryShareMiniCommon']({ appId: miniConfig.appid }).catch((err) => err)
  if (res.code != 0) {
    wx.$.collectEvent.event('InterfaceResponseErr', {
      /** 接口名 */
      interface_name: '/share/v1/setting/queryShareMiniCommon',
      requestType: 'JAVA',
      /** 返回数据 */
      data: JSON.stringify(res || {}),
    })
  }
  if (res.code == 0) {
    shareData = {
      path: data.path,
      title: res.data.title || shareData.title,
      imageUrl: res.data.img || shareData.imageUrl,
    }
  }
  const img = shareInfo.imageUrl || data.imageUrl || shareData.imageUrl

  return {
    ...shareData,
    ...data,
    ...shareInfo,
    imageUrl: isCurPageImg ? undefined : img,
  }
}

/**
 * @params: null
 * @return: 小程序分享到朋友圈数据
 * @description: 获取小程序被分享到朋友圈时默认携带的信息
 * @tips: https://developers.weixin.qq.com/miniprogram/dev/reference/api/Page.html#onShareTimeline
 */
export const getShareTimeLineInfo = (options?: Partial<MiniShareTimeLine>): MiniShareTimeLine => {
  let { query = '' } = options || {}
  const { title = '每天发布数十万条全国各地工地好活、找人简历！高薪、现结好活等你挑！',
    imageUrl = 'https://staticscdn.zgzpsjz.com/miniprogram/images/share/mini-share-time-line.png' } = options || {}
  // 如果存在用户信息，则自动加上必要信息
  const { userId } = store.getState().storage.userState
  if (userId) {
    let params = ''
    if (query.indexOf('refid') < 0) {
      params = `refid=${userId}`
    }
    if (query.indexOf('source') < 0) {
      if (params) {
        params += '&'
      }
      params += miniConfig.shareSource ? `source=${miniConfig.shareSource}` : ''
    }

    query += query ? `&${params}` : `${params}`
  }
  const data: MiniShareTimeLine = {
    title,
    query,
    imageUrl,
  }
  return data
}

/**
 * 组装分享sharePage、sharePath、track_seed信息
 * @param sharePage 分享页面
 * @param path 分享路径
 * @param userId 用户ID
 * @param seoParam 用于组装seo城市、工种、人员构成的参数(格式:a1c2c3s1)
 * @param track_seed trackId 后端用于记录分享用户
 */
export const getSharePathInfo = (payload, userId, path?, sharePage?, track_seed?, seoParam = '') => {
  const currentPage = wx.$.r.getCurrentPage()
  const page = payload.btnId
  const data = {
    path: path || currentPage.route,
    track_seed: track_seed || createTrackId(),
    sharePage: sharePage || currentPage.route,
  }
  const params = `${seoParam}${seoParam ? '&' : ''}source=${miniConfig.shareSource}&refid=${userId}${payload?.key ? `&${payload.key}=${payload.val}` : ''}${payload.userAcq ? `&userAcq=${payload.userAcq}` : ''}${payload.uuid ? `&uuid=${payload.uuid}` : ''}`
  /** 生成最终分享路径path */
  // eslint-disable-next-line max-len
  if (path.indexOf('?url=') != -1) {
    data.path = `${data.path}&${params}`
  } else {
    data.path = `${data.path}?${params}`
  }
  return { sharePage: data.sharePage, sharePath: page, track_seed: data.track_seed, path: data.path, source: miniConfig.shareSource, refid: userId }
}

/**
 * @description 通过shareChannnel获取生成分享信息shareInfo
 * @param SHARE_CHANNEL 分享类型
 * @param sharePath 需要分享的路径
 */
export const getShareInfoByType = (SHARE_CHANNEL: SHARE_CHANNEL, path = SHARE_DEFAULT_PATH, ext: any = {}) => {
  const { userId } = store.getState().storage.userState
  const payload = { btnId: SHARE_BTN_LOCATION.WX_CG_CAP_MENU_PATH }
  const shareInfo: any = getSharePathInfo(payload, userId, path, SHARE_BTN_LOCATION.WXCGCAPMENU_PAGE)

  completeShare(SHARE_CHANNEL, shareInfo, 1, { active_id: ext?.active_id || null })
  return shareInfo
}

/**
 * @description 通过shareChannnel获取生成分享信息shareInfo
 * @param SHARE_CHANNEL 分享类型
 * @param sharePath 需要分享的路径
 */
export const getShareInfoByTypeV1 = (SHARE_CHANNEL: SHARE_CHANNEL, sharePath = SHARE_DEFAULT_PATH, sharePage = SHARE_BTN_LOCATION.WXCGCAPMENU_PAGE, path = SHARE_DEFAULT_PATH) => {
  const { userId } = store.getState().storage.userState
  const payload = { btnId: sharePath }
  const shareInfo: any = getSharePathInfo(payload, userId, path, sharePage)
  completeShare(SHARE_CHANNEL, shareInfo) || {}
  return shareInfo
}

/**
 * @description 小程序一步分享流程
 * @param SHARE_CHANNEL 分享渠道支持的类型
 * @param shareInfo 自定义分享的内容
 * @param landing 落地端：0是m端，1是小程序
 */
export const oldcompleteShare = (SHARE_CHANNEL: SHARE_CHANNEL, shareInfo: ReturnType<typeof getSharePathInfo>, landing = 1, oParam: any = {}) => {
  let c_type = oParam?.type || ''
  if (oParam?.active_id) {
    c_type = 'active'
  }
  let detail_id = oParam?.detail_id || null
  if (oParam?.active_id) {
    detail_id = oParam?.active_id
  }
  /** 请求body参数 */
  const params = {
    trackSeed: shareInfo.track_seed,
    shareChannel: SHARE_CHANNEL,
    landing,
    sharePage: shareInfo.sharePage,
    sharePath: shareInfo.sharePath,
    ...oParam,
    type: c_type,
    detailId: detail_id,
    runtime: transFromRuntime(),
    packageName: miniConfig.appid,
  } as any
  if (store.getState().storage.userState.login) {
    delete params.active_id
    return wx.$.javafetch['POST/share/v1/config/completeShare'](params).then((res) => {
      return Promise.resolve(res)
    }).catch((err) => {
      return Promise.resolve(err)
    })
  }
  return { code: 0 }
}

/**
 * @description 小程序3步分享流程
 * @param SHARE_CHANNEL 分享渠道支持的类型
 * @param shareInfo 自定义分享的内容
 * @param landing 落地端：0是m端，1是小程序
 */
export const completeShare = (SHARE_CHANNEL: SHARE_CHANNEL, shareInfo: ReturnType<typeof getSharePathInfo>, landing = 1, oParam: any = {}) => {
  let c_type = oParam?.type || ''
  if (oParam?.active_id) {
    c_type = 'active'
  }
  let detail_id = oParam?.detail_id || ''
  if (oParam?.active_id) {
    detail_id = oParam?.active_id
  }
  const shareCardData = storage.getItemSync('shareCardData')

  const isReturn = (c_type == 'resume' || c_type == 'job') && !detail_id
  if (SHARE_CHANNEL == 2 && store.getState().storage.userState.login && !isReturn) {
    /** 一步分享 */
    /** 请求body参数 */
    const params = {
      trackSeed: shareInfo.track_seed,
      shareChannel: SHARE_CHANNEL,
      landing,
      sharePage: shareInfo.sharePage,
      sharePath: shareInfo.sharePath,
      ...oParam,
      type: c_type,
      detailId: detail_id,
      runtime: transFromRuntime(),
      packageName: miniConfig.appid,
    } as any
    delete params.active_id
    return wx.$.javafetch['POST/share/v1/config/completeShare'](params).then((res) => {
      return Promise.resolve(res)
    }).catch((err) => {
      return Promise.resolve(err)
    })
  }
  if (store.getState().storage.userState.login && !isReturn && !shareCardData.isShareModal) {
    /** 第一步（获取分享信息）请求body参数 */
    const params = {
      page: shareInfo.sharePage,
      path: shareInfo.sharePath,
      type: c_type,
      detailId: detail_id,
      // runtime: transFromRuntime(),
      // packageName: miniConfig.appid,
    } as any
    delete params.active_id
    return wx.$.javafetch['POST/share/v2/config/info'](params).then((res) => {
      dispatch(actions.storageActions.setItem({ key: 'shareTrackSeed', value: res.data.trackSeed }))
      choseChannel({ trackSeed: res.data.trackSeed, channel: SHARE_CHANNEL, landing })
      return Promise.resolve(res)
    }).catch((err) => {
      return Promise.resolve(err)
    })
  }

  return { code: 0 }
}

/** 分享流程第二步（选择渠道） */
export const choseChannel = (params) => {
  wx.$.javafetch['POST/share/v1/config/choseChannel'](params).then((res) => {
    if (res.code == 0) {
      /** 分享成功 */
      shareBack({ trackSeed: params.trackSeed })
    }
  })
}

/** 分享流程第三步（分享回调）（小程序无回调，选择完渠道即成功） */
const shareBack = (params) => {
  wx.$.javafetch['POST/share/v1/config/shareBack'](params)
}

/**
 * @Description: 生成分享trackId
 */
export const createTrackId = () => {
  let now = new Date().getTime() // now = 1625724296995;  毫秒时间戳
  now = Math.round(now / 1000) // now = 1625724296;  秒时间戳
  let rand = Math.random() // rand = 0.6620131172876098;  随机值
  rand = Math.round(rand * 10000 * 100) // rand = 662013;
  rand = Math.round(rand / 1000) * 10000 * 100 // 662
    + (now % 1000) * 1000 //  296
    + (rand % 1000) // 013

  // seed = 秒时间戳（now）的 36 进制 + 亿级随机数（rand）的 36 进制。
  // seed = "qvwww8ay58q8"
  return now.toString(36) + rand.toString(36)
}

/**
 * @description 小程序分享成功回调
 * @param payload {share_channel:分享途径 success 为0 时必传,success:app发起分享为0 分享成功跳回app 为1,track_seed,landing:落地端 【0=>m端，1=>小程序端】}
 *  */
export const shareSuccessCallBack = ({ share_channel, success, track_seed, landing }, extra = {}) => {
  wx.$.javafetch['POST/share/v1/config/choseChannel']({ channel: share_channel, landing, trackSeed: track_seed }, extra)
}

// ! 以下为分享裂变需求方法
/**
 * @description 获取招工找活详情的分享标题
 * @param  bizType job 招工  resume 找活
 */
export const getRecruitOrResumeShareTitle = async (bizType: 'job' | 'resume') => {
  const { data } = await wx.$.javafetch['POST/share/v1/setting/cardTitle']({ bizType })
  return { shareTitle: data?.title }
}

/**
 * 分享裂变3.0设置sharePath和sharePage
 */
export const setSharePath = () => {
  const pages = getCurrentPages()
  const prevPage = pages[pages.length - 1]
  let sharePath = 'invite_qrcode'
  const sharePage = 'subpackage/static/invite/index'
  if (prevPage.route == 'pages/ucenter/index' || prevPage.route == 'subpackage/member/integral/officialPoints/index') {
    sharePath = 'ucenter_invite_qrcode'
  } else if (prevPage.route == 'subpackage/member/getintegral/index') {
    sharePath = 'getintegral_invite_qrcode'
    // } else if (prevPage.route == 'subpackage/member/taskcenter/index') {
    //   sharePath = 'taskCenter_path'
    //   // eslint-disable-next-line @typescript-eslint/no-unused-vars
    //   sharePage = 'taskCenter_page'
  } else if (prevPage.route == 'pages/index/index') {
    sharePath = 'homeicon_invite_btn_friend'
  }
  return { sharePath, sharePage }
}

/** 获取分享配置数据 */
const getShareConfigInfo = () => {
  try {
    const { shareConfig } = store.getState().config
    const currentPage = wx.$.r.getCurrentPage()
    const currentWechatShare = shareConfig[`${miniConfig.appid}`] || {}
    return currentWechatShare[currentPage.route] || {}
  } catch (error) {
    return {}
  }
}

/** 获取分享的参数 */
export function getShareReq(): ICodeLoginParams['shareReq'] {
  const shareSource = storage.getItemSync('source_share') // 分享来源
  const refTenantId = storage.getItemSync('sourceCode') // 分享者的id
  const trackSeed = storage.getItemSync('track_seed_share') // 分享的shareTicket
  return {
    refTenantId,
    shareSource,
    trackSeed,
  }
}

/**
 * @description 获取分享的 内容（卡片类型 积分/次卡)
 */
export const getShareCopyWriting = async () => {
  const res = await wx.$.javafetch['POST/share/v1/config/getShareCopyWriting']().catch(() => { })
  if (res.code == 0) {
    return { data: res.data }
  }
  return { data: {} }
}

/**
 * @description 通过shareChannnel获取生成分享信息shareInfo
 * @param type 分享类型 2朋友圈 4好友（默认）
 * @param sharePage 后台配置的sharePage
 * @param sharePath 后台配置的sharePath
 * @param ext 自定义参数（可自定义设置imageUrl title path,优先级最高）
 * @param from onShareAppMessage方法参数（options）的from
 */
export const getShareInfoByTypeV2 = async ({ type = 4, sharePage = SHARE_BTN_LOCATION.WXCGCAPMENU_PAGE, sharePath = SHARE_BTN_LOCATION.WX_CG_CAP_MENU_PATH, ext = {}, from }) => {
  // 截图分享(用截图的路径,页面分享按钮除外)
  const pages = wx.$.r.getCurrentPage()
  if (pages.data.screenshotShareData && pages.data.screenshotShareData.sharePage && from != 'button') {
    sharePage = pages.data.screenshotShareData.sharePage
    sharePath = pages.data.screenshotShareData.sharePath
  }
  const { data, code } = await completeShareV2(type, sharePage, sharePath, ext)
  let shareInfo: any = { from, ...ext }
  if (code == 0 && data.mini) {
    shareInfo = {
      ...shareInfo,
      title: data.mini.title,
      imageUrl: data.mini.img,
      path: data.mini.path,
      needTrackSeed: false,
      ...ext,
    }
  }
  return getShareInfo(shareInfo)
}

/**
 * @description 小程序3步分享流程
 * @param type 分享渠道支持的类型
 * @param shareInfo 自定义分享的内容
 * @param landing 落地端：0是m端，1是小程序
 */
export const completeShareV2 = (type, sharePage, sharePath, oParam: any = {}) => {
  let c_type = oParam?.type || ''
  if (oParam?.active_id) {
    c_type = 'active'
  }
  let detail_id = oParam?.detail_id || ''
  if (oParam?.active_id) {
    detail_id = oParam?.active_id
  }
  const shareCardData = storage.getItemSync('shareCardData')

  const isReturn = (c_type == 'resume' || c_type == 'job') && !detail_id
  if (type == 2 && store.getState().storage.userState.login && !isReturn) {
    /** 一步分享 */
    /** 请求body参数 */
    const params = {
      trackSeed: createTrackId(),
      shareChannel: type,
      landing: 1,
      sharePage,
      sharePath,
      ...oParam,
      type: c_type,
      detailId: detail_id,
      runtime: transFromRuntime(),
      packageName: miniConfig.appid,
    } as any
    delete params.active_id
    return wx.$.javafetch['POST/share/v1/config/completeShare'](params).then((res) => {
      return Promise.resolve(res)
    }).catch((err) => {
      return Promise.resolve(err)
    })
  }
  if (store.getState().storage.userState.login && !isReturn && !shareCardData.isShareModal) {
    /** 第一步（获取分享信息）请求body参数 */
    const params = {
      sharePage,
      sharePath,
      type: c_type,
      detailId: detail_id,
      runtime: transFromRuntime(),
      packageName: miniConfig.appid,
    } as any
    delete params.active_id
    return wx.$.javafetch['POST/share/v1/config/info'](params).then((res) => {
      dispatch(actions.storageActions.setItem({ key: 'shareTrackSeed', value: res.data.trackSeed }))
      choseChannel({ trackSeed: res.data.trackSeed, channel: type, landing: 1 })
      return Promise.resolve(res)
    }).catch((err) => {
      return Promise.resolve(err)
    })
  }

  return Promise.resolve({ code: 100 })
}

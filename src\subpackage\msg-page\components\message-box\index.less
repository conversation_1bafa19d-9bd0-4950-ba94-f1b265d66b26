.msg-container {
  display: flex;
  flex-direction: row;
  :active {
    background-color: #eff1f6;
  }
}

.msg-item {
  width: 750rpx;
  height: 144rpx;
  background-color: #fff;
  border: 0;
  box-sizing: border-box;
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;



  .topStatus {
    z-index: 99;
    position: absolute;
    right: 0rpx;
    top: 0rpx;
    width: 40rpx;
    height: 40rpx;
  }

  .head-img {
    position: relative;
    display: inline-flex;
    width: 96rpx;
    height: 96rpx;
  }

  .vip-img {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40rpx;
    height: 40rpx;
  }

  .left-img {
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 48rpx;
    object-fit: cover;
  }
}

.top-con {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title-v {
    display: flex;
    align-items: center;
    overflow: hidden;
    width: 100%;
  }

  .title {
    white-space: nowrap;
    font-size: 32rpx;
    color: rgba(0, 0, 0, 0.85);
  }

  .title-max {
    .ellip();
    max-width: 180rpx;
    min-width: 100rpx;
  }

  .conver-suff {
    margin-left: 8rpx;
    color: rgba(0, 0, 0, 0.45);
    font-size: 28rpx;
    .ellip();
  }

  .tt-label {
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8rpx;
    padding: 3rpx 8rpx;
    border: 1rpx solid rgba(233, 237, 243, 1);
    color: rgba(0, 0, 0, 0.25);
    font-size: 22rpx;
    margin-left: 8rpx;
  }

  .time {
    white-space: nowrap;
    color: rgba(0, 0, 0, 0.25);
    font-size: 28rpx;
    text-align: right;
    margin-left: 48rpx;
  }
}

.right-con {
  flex: 1;
  width: 566rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  margin-left: 24rpx;

  .down-con {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12rpx;

    .desc-label {
      color: rgba(0, 0, 0, 0.25);
      font-size: 28rpx;
      margin-right: 8rpx;
      white-space: nowrap;
    }

    .dtop {
      width: 100%;
      color: rgba(0, 0, 0, 0.65);
      font-size: 28rpx;
      .ellip();
    }

    .discardImg-v {
      width: 32rpx;
      height: 32rpx;
    }

    .discardImg {
      width: 32rpx;
      height: 32rpx;
    }
  }
}

.ddown {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  min-width: 28rpx;
  height: 28rpx;
  font-size: 20rpx;
  font-weight: bold;
  background: rgba(232, 54, 46, 1);
  color: rgba(255, 255, 255, 1);
  border-radius: 14rpx;
  border: 1rpx solid rgba(255, 255, 255, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
}

.ddown-isnotice {
  background: rgba(188, 191, 196, 1);
}

.isMove {
  max-width: 288rpx;
  height: 144rpx;
  display: flex;
  justify-content: flex-start;
  :active {
    background-color: transparent;
  }
  .commonBanner {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 144rpx;
    height: 100%;
    background-color: #0092ff;
    color: rgba(255, 255, 255, 0.95);

    .contentImg {
      width: 48rpx;
      height: 48rpx;
    }

    .contentText {
      text-align: center;
      font-weight: bold;
      font-size: 26rpx;
      margin-top: 8rpx;
    }
  }

  .operateTop {
    background-color: #0092ff;
  }

  .operatehaveTop {
    background-color: #8796a2;
  }

  .operateDel {
    background-color: #f74742;
  }
}

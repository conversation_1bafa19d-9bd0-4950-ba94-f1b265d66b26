import { publishPage } from '@/lib/mini-component-page/index'
import { PLTools } from '@/utils/index'

import { getWhoSeeMeRecruitWorkerListApi } from './api-server'
import { isReportEvent, listExposure, reportFindWorkList } from '@/utils/helper/list/index'

/**
 * @name 浏览记录->谁看过我的招工
 * // 用于获取页面 谁看过我的招工
 * @param query.jobId  招工 id (作为获取页面列表的请求参数)
 * @param query.provinceId 省 id (用于 去加急)
 * @param query.showGoToToppingBtn 用于控制列表 去置顶按钮 是否展示 1 展示
 */
Page(publishPage(['onShow', 'onReachBottom'])({
  data: {
    query: {},
    pageInfo: PLTools.getDefaultPLData({ pageSize: 15 }),
    // 是否是第一次加载
    isFirstLoad: true,
    // 是否展示去置顶按钮
    showGoToToppingBtn: false,
    /** 隐藏数量 */
    hiddenNumber: 0,
  },

  observers: {
    query() {
      console.error('#query', this.data.query)
    },
    pageInfo() {
      console.error('#pageInfo', this.data.pageInfo)
    },
  },

  onUnload() {
    const pageInfoList = this.data.pageInfo.list || []
    isReportEvent.call(this, pageInfoList, (res) => this.handleReportCallBack(res))
  },
  async handleReportCallBack(res) {
    const nRes: any = { ...res }
    reportFindWorkList(nRes, { source_id: '25', is_button_external: '0' })
  },
  // 监听列表滚动上报埋点
  async onListScroll(page) {
    // let customNavbarHeight = this.data.customNavbarHeight || 0
    // if (!customNavbarHeight) {
    //   const customHeaderInfo = await getDom('#custom-header')
    //   customNavbarHeight = customHeaderInfo?.height || 0
    //   this.setData({ customNavbarHeight })
    // }
    const callback = (res) => {
      this.handleReportCallBack(res)
    }
    listExposure.call(this, { page, elementId: '.seeme-usercard-indexer', top: 0, callback })
  },
  async onLoad(query) {
    this.setData({ query, showGoToToppingBtn: query.showGoToToppingBtn == '1' })
    await getWhoSeeMeRecruitWorkerListApi.call(this, true)
    const { pageInfo } = this.data
    this.onListScroll(pageInfo.page)
  },
  async onReachBottom() {
    if (PLTools.isLoadingOrFinishState(this.data.pageInfo)) {
      return
    }
    await getWhoSeeMeRecruitWorkerListApi.call(this, false)
    this.onListScroll(this.data.pageInfo.page)
  },
  // 空状态-去置顶
  goToTheTop() {
    const { jobId: job_id } = this.data.query
    wx.$.l.existJobTopSet(job_id, {
      pageFrom: 'jobManage',
      jumpMethod: 'push',
      showTab: 1,
    })
  },
}))

const os = require('os')
const fs = require('fs')
const https = require('https')
const inquirer = require('inquirer')
/** 打印系统信息 */
function printSystemInfo() {
  const dealMem = (mem) => {
    let G = 0
    let M = 0
    let KB = 0
    mem > 1 << 30 && (G = parseInt((mem / (1 << 30)).toFixed(2)))
    mem > 1 << 20 && mem < 1 << 30 && (M = parseInt((mem / (1 << 20)).toFixed(2)))
    mem > 1 << 10 && mem > 1 << 20 && (KB = parseInt((mem / (1 << 10)).toFixed(2)))
    return G > 0 ? G + 'G' : M > 0 ? M + 'M' : KB > 0 ? KB + 'KB' : mem + 'B'
  }

  console.log('------------------小程序信息---------------')

  // cpu架构
  const arch = os.arch()
  console.log('cpu架构：          ' + arch + '*' + os.cpus().length)

  // 操作系统内核
  const kernel = os.type()
  console.log('操作系统内核：     ' + kernel)

  // 操作系统平台
  const pf = os.platform()
  console.log('平台：             ' + pf)

  // 系统开机时间
  const uptime = os.uptime()
  console.log('当前时间：         ' + Date())
  console.log('系统已启动：       ' + (uptime / 3600).toFixed(2) + '小时')

  // 主机名
  const hn = os.hostname()
  console.log('主机名：           ' + hn)

  // 主目录
  const hdir = os.homedir()
  console.log('主目录：           ' + hdir)

  // 内存
  const totalMem = os.totalmem()
  const freeMem = os.freemem()
  console.log('内存大小：         ' + dealMem(totalMem) + ' 空闲内存：' + dealMem(freeMem) + '\n')
  console.log('------------------控制面板---------------')
}

/** 确认用户的选项 */
function checkList(config) {
  const promptList = [
    {
      type: 'list',
      message: '请选择运行模式：',
      name: 'mode',
      choices: [
        {
          name: '调试',
          value: 'dev',
        },
        {
          name: '打包',
          value: 'build',
        },
      ],
    },
    {
      type: 'list',
      message: '请选择运行环境：',
      name: 'development',
      choices: (answers) => {
        const miniType = answers
        const list = [
          {
            name: '测试站',
            value: 'DEV',
          },
          {
            name: '测试站2',
            value: 'DEV2',
          },
          {
            name: '预发布',
            value: 'PRE',
          },
          {
            name: '预发布2',
            value: 'PRE2',
          },
          {
            name: '预发布正式站',
            value: 'REL',
          },
          {
            name: '正式站',
            value: 'PRO',
          },
        ]
        if (answers.mode !== 'build') {
          list.push({
            name: '开发站',
            value: 'DEVELOP',
          })
        }
        return list
      },
    },
    {
      type: 'list',
      message: '请选择编译平台：',
      name: 'miniType',
      choices: [
        {
          name: '微信',
          value: 'weapp',
        },
        {
          name: '百度',
          value: 'swan',
        },
        {
          name: '抖音',
          value: 'tt',
        },
      ],
    },
    {
      type: 'rawlist',
      message: '请选择编译标识：',
      name: 'sub',
      pageSize: 100,
      choices: (answers) => {
        const miniType = answers.miniType
        return Object.keys(config[miniType]).map((item) => {
          return {
            name: config[miniType][item].name,
            value: item,
          }
        })
      },
    },
    {
      type: 'list',
      message: '是否提审',
      name: 'audit',
      choices: [
        {
          name: '是',
          value: true,
        },
        {
          name: '否',
          value: false,
        },
      ],
      when: (answers) => {
        const { mode, development, miniType, sub } = answers
        if (mode == 'build' && development == 'PRO' && miniType == 'weapp' && sub == 'yupao') {
          return true
        }
        return false
      },
    },
  ]
  return inquirer.prompt(promptList)
}

/** 时间格式转换 */
function formatTime(date, format = 'yyyy-MM-dd hh:mm:ss') {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  const second = String(date.getSeconds()).padStart(2, '0')

  const formatMap = {
    yyyy: year,
    MM: month,
    dd: day,
    hh: hour,
    mm: minute,
    ss: second,
  }

  const formattedTime = format.replace(/yyyy|MM|dd|hh|mm|ss/g, (match) => formatMap[match])
  return formattedTime
}

/** 获取环境说明 */
function getDevelopmentStr(development) {
  let developmentStr = ''
  switch (development) {
    case 'DEVELOP':
      developmentStr = '开发环境'
      break
    case 'PRE':
      developmentStr = '预发布环境'
      break
    case 'REL':
      developmentStr = '预正环境'
      break
    case 'PRO':
      developmentStr = '正式站'
      break
    default:
      developmentStr = '测试站'
      break
  }
  return developmentStr
}

/** 提审时关闭触达开关(cicd.js时执行，start.js打包正式站鱼泡网提审时执行) */
function closeReachRequest(appids) {
  return new Promise((resolve, reject) => {
    /** 读取配置文件 */
    const configData = fs.readFileSync('src/config/app.ts', 'utf-8')
    /** 获取数据 */
    const match = configData.match(/export const REQUEST_VERSION = '(.+?)'/)
    /** 获取版本号 */
    const version = match && match[1]

    const request = https.request(
      {
        // hostname: 'yupao-test-backend.yupaowang.com',
        hostname: 'yupao-prod-backend.yupaowang.com',
        port: 443,
        path: '/trade/v1/order/updateLimitFlagInfoListByQuery',
        method: 'POST',
        headers: { 'Content-Type': 'application/json;' },
        rejectUnauthorized: false,
      },
      (res) => {
        let responseData = ''
        res.on('data', (chunk) => {
          responseData += chunk
        })
        res.on('end', () => {
          const data = JSON.parse(responseData)
          if (data.error) {
            reject(data)
          } else {
            resolve(JSON.parse(responseData))
          }
        })
      },
    )
    request.on('error', (error) => {
      reject(error.message)
    })

    request.write(
      JSON.stringify({
        operator: '小程序提审',
        miniProgramCodeList: appids,
        minVersion: version,
        maxVersion: '9.9.9',
      }),
    )
    request.end()
  })
}

/** 小程序打包上报版本号(版本库v1.0--3003708064) */
function reportVersion(miniType) {
  /** 读取配置文件 */
  const configData = fs.readFileSync('src/config/app.ts', 'utf-8')
  /** 获取数据 */
  const match = configData.match(/export const REQUEST_VERSION = '(.+?)'/)
  /** 获取版本号 */
  const version = match && match[1]
  const runtime = {
    weapp: 'WX_MINI',
    swan: 'BAIDU_MINI',
    tt: 'BD_MINI',
  }
  const request = https.request(
    {
      hostname: 'https://yupao-prod-backend.yupaowang.com',
      // port: 443,
      path: '/base/v1/library/add',
      method: 'POST',
      headers: { 'Content-Type': 'application/json;' },
      rejectUnauthorized: false,
    },
    (res) => {
      let responseData = ''
      res.on('data', (chunk) => {
        responseData += chunk
      })
      res.on('end', () => {
        console.log(responseData)
      })
    },
  )
  request.on('error', (error) => {
    console.log('error.message',error.message)
  })
  request.write(
    JSON.stringify({
      runtime: runtime[miniType],
      business: 'YPZP',
      packageVersion: version,
    }),
  )
  request.end()
}

module.exports = {
  getDevelopmentStr,
  printSystemInfo,
  checkList,
  formatTime,
  closeReachRequest,
  reportVersion,
}

.msg-txt-v {
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;
}

.msg-txt {
  position: relative;
  // max-width: 474rpx;
  max-width: 542rpx;
  min-width: 96rpx;
  padding: 24rpx;
  background: #0092ff;
  border-radius: 24rpx 24rpx 4rpx 24rpx;
  color: rgba(255, 255, 255, 0.95);
  font-size: 34rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 48rpx;
  min-height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.msg-other {
  background: #ffffff;
  color: rgba(0, 0, 0, 0.85);
  border-radius: 24rpx 24rpx 24rpx 4rpx;
}

.arrow {
  position: absolute;
  top: 19px;
  width: 0;
  height: 0;
  font-size: 0;
  border: solid 8rpx;
}

.arrow-r {
  right: -8px;
  border-color: transparent transparent transparent #0092ff;
}

.arrow-l {
  left: -8px;
  border-color: transparent #ffffff transparent transparent;
}

.emoji {
  width: 48rpx;
  height: 48rpx;
  margin: 4rpx 6rpx;
  vertical-align: middle;
}

.dom_em {
  display: inline-block;
  align-items: center;
  flex-wrap: wrap;
  margin: -4rpx -6rpx;
}

.dom_em_v {
  height: 100%;
}

.dom_txt {
  vertical-align: middle;
  word-break: break-word;
}

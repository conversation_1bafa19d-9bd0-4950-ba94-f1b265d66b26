/*
 * @Date: 2022-02-15 10:36:28
 * @Description: 选择城市
 */
import { MapStateToData, connectPage } from '@/store/index'
import { tools } from '@/utils/index'
import { getRecruitOrResumeHotAreas, setCityTop } from './utils'

/**
 * 获取排除指定的地址数据-不会改变地址原数据
 * @param excludeArea: 1.排除直辖市(北京，上海，天津，重庆), 2.排除id为1的地址, 3.排除直辖市和id为1的地址
 * @param removeChildren: 1.去掉省下边的children,2去掉市下边的children
 */
export const getExcludeAreaTreeData = ($areaTree: Array<any>, excludeArea, removeChildren = 0) => {
  const areaTree = wx.$.u.deepClone($areaTree) // 深拷贝传入的地址数组
  let ids = []
  switch (excludeArea) {
    case 1:
      ids = ['2', '25', '27', '32'] // 直辖市的id
      break
    case 2:
      ids = ['1']
      break
    case 3:
      ids = ['1', '2', '25', '27', '32']
      break
    default:
      break
  }
  if (removeChildren === 1 || removeChildren === 2) {
    areaTree?.forEach((province) => {
      if (removeChildren === 1) {
        // eslint-disable-next-line no-param-reassign
        province.children = []
      } else if (province?.children?.length) {
        province.children.forEach((city) => {
          // eslint-disable-next-line no-param-reassign
          city.children = []
        })
      }
    })
  }
  if (ids.length > 0) {
    return areaTree?.filter((item) => ids.indexOf(`${item.id}`) == -1)
  }
  return areaTree
}

const mapStateToData: MapStateToData = (state) => {
  const { topCity } = state
  return {
    // 置顶城市的配置
    topCity,
  }
}

Page(connectPage(mapStateToData)({
  /** 页面的初始数据 */
  data: {
    // 城市列表总数据
    areaTree: [],
    // 选中的城市
    selectCities: [],
    // 热门城市
    hotAreas: [],
    // 顶部高度
    headerHeight: '220rpx',
    // 搜索关键字
    keywords: '',
    // 搜索的数据列表
    searchList: [],
    // 是否显示搜索列表
    searchShow: false,
    // 来源: 招工置顶, 找活加急
    originText: '置顶',
  },

  /** 生命周期函数--监听页面加载 options */
  async onLoad() {
    const { topCity } = this.data
    wx.showLoading({ title: '数据加载中...', mask: true })
    // 获取城市列表总数据-并初始化选中状态
    await this.getAreaTreeAreas()
    // 获取热门城市-并初始化选中状态
    await this.getHotAreas()
    // 城市
    this.setData({
      selectCities: topCity.selectCities,
      originText: topCity.origin === 'resume' ? '加急' : '置顶',
    })
    // 处理页面滚动位置
    this.headerPageScrollTo(topCity.selectCities[0])
    wx.hideLoading()
  },

  /** 生命周期回调—监听页面初次渲染完成 */
  onReady() {
    tools.common.getDom('#header-content').then(res => {
      this.setData({
        headerHeight: `${res.height}px`,
      })
    })
  },

  /** 获取城市列表总数据-并初始化选中状态 */
  async getAreaTreeAreas() {
    const { topCity: { selectCities } } = this.data
    let areaTree = await wx.$.l.getAreaTreeData()
    areaTree = getExcludeAreaTreeData(areaTree, 3, 2)
    if (selectCities) {
      const ids = selectCities.map(item => `${item.id}`)
      areaTree?.forEach(item => {
        item.checked = ids.includes(`${item.id}`)
        item.children?.forEach(city => {
          // eslint-disable-next-line no-param-reassign
          city.checked = ids.includes(`${city.id}`)
        })
      })
    }

    this.setData({ areaTree })
  },

  /** 获取热门城市-并初始化选中状态 */
  async getHotAreas() {
    const { topCity } = this.data
    const hotAreas = await getRecruitOrResumeHotAreas()
    if (topCity && topCity.selectCities && Array.isArray(hotAreas)) {
      const ids = topCity.selectCities.map(item => `${item.id}`)
      hotAreas?.forEach(item => {
        item.checked = ids.includes(`${item.id}`)
      })
    }
    this.setData({
      hotAreas,
    })
  },

  /** 确认选择 */
  onConfirmSelect() {
    const { selectCities } = this.data
    if (selectCities.length > 0) {
      setCityTop(selectCities)
    } else {
      setCityTop([])
    }
    wx.$.r.back()
  },

  /** 点击城市名称 */
  async onSelectCity(e) {
    await wx.$.u.waitAsync(this, this.onSelectCity, [e])
    const { children, ...area } = e.currentTarget.dataset.item
    await this.verificationCity(area)
  },

  /**
   * 判断是否可以选中或者切换
   * @param area 城市对象
   * @param isScroll 可选，布尔值，是否需要滚动到城市所在位置
   */
  async verificationCity(area, isScroll?: boolean) {
    const { topCity, selectCities } = this.data
    // 存储已选择的城市id集合
    const ids = []
    // 当前点击的地址是否是城市
    const isCity = area.pid != '0' && area.pid != '1'
    if (topCity.disabledCities && topCity.disabledCities.length > 0 && topCity.disabledCities.includes(`${area.id}`)) {
      wx.$.alert({ content: '此地区暂未提供服务' })
      return
    } if (selectCities.length > 0) {
      let maxCity = 0 // 选择的城市数量
      let maxProvince = 0 // 选择的省份或者直辖市数量
      selectCities?.forEach(item => {
        ids.push(`${item.id}`)
        if (item.pid == '0' || item.pid == '1') {
          maxProvince += 1
        } else {
          maxCity += 1
        }
      })
      // 当选中的不是全国并且点击的城市不在已选中的城市中
      if (area.id != '1' && !ids.includes(`${area.id}`)) {
        if (maxProvince >= topCity.maxProvince && !isCity) {
          wx.$.alert({ content: topCity.maxNumberTips })
          return
        } if (maxCity >= topCity.maxCity && isCity) {
          wx.$.alert({ content: topCity.maxNumberTips })
          return
        }
      }
    }
    let newSelectCities = [...selectCities]
    // 点击的城市在已选择的城市中的索引
    const areaIndex = ids.indexOf(`${area.id}`)
    // 点击的城市的父级地址在选中城市的索引
    const pidIndex = ids.indexOf(`${area.pid}`)

    if (area.id == '1') {
      // 点击的地址是全国时, 过滤掉其他选中的地区
      newSelectCities = newSelectCities.filter(item => item.id == area.id)
    } else {
      // 过滤点击的省份时所属当前省份的城市包括全国地址
      newSelectCities = newSelectCities.filter(item => item.pid != area.id && item.id != '1')
    }

    // 当点击的城市属于已选中的某个省时,则删除当前城市选中的省
    if (area.pid != '0' && area.pid != '1' && pidIndex > -1) {
      newSelectCities.splice(pidIndex, 1)
    }

    // 切换城市选中状态
    if (areaIndex > -1) {
      // 如果点击的地址在已选中的地址当中则删除
      newSelectCities.splice(areaIndex, 1)
    } else {
      // 添加城市
      newSelectCities.push(area)
    }
    this.headerSelectChecked(newSelectCities)
    this.setData({ selectCities: newSelectCities })
    isScroll && this.headerPageScrollTo(area)
  },

  /** 处理选中状态 */
  headerSelectChecked(selectCities) {
    const { hotAreas, areaTree } = this.data
    const ids = selectCities.map(item => `${item.id}`)
    hotAreas?.forEach(item => {
      item.checked = ids.includes(`${item.id}`)
    })
    areaTree?.forEach(item => {
      item.checked = ids.includes(`${item.id}`)
      item.children?.forEach(city => {
        // eslint-disable-next-line no-param-reassign
        city.checked = ids.includes(`${city.id}`)
      })
    })

    this.setData({
      hotAreas,
      areaTree,
    })
  },

  /** 处理页面滚动的位置 */
  headerPageScrollTo(city) {
    const provinceArea = this.getProvince(city)
    if (provinceArea && provinceArea.id) {
      setTimeout(() => {
        const query = wx.createSelectorQuery()
        query.select('#header-content').boundingClientRect() // 头部搜索组件元素信息
        query.select(`#top-city-${provinceArea.id}`).boundingClientRect() // 列表地址元素信息
        query.selectViewport().scrollOffset()
        query.exec((rects) => {
          const [headerSearch, cityList, scrollOffset] = rects
          if (cityList) {
            wx.pageScrollTo({
              scrollTop: scrollOffset.scrollTop + cityList.top - headerSearch.height,
              duration: ENV_IS_WEAPP ? 1300 : 300,
            })
          }
        })
      }, 400)
    }
  },

  /** 得到当前城市的省份 */
  getProvince(city) {
    const { selectCities, areaTree } = this.data
    const selectCity = city || selectCities[0]

    if (selectCity && selectCity.pid != '1') {
      return areaTree.find(item => selectCity.pid == item.id)
    } if (selectCity && selectCity.pid == '1') {
      return selectCity
    }
    return null
  },

  /** 搜索框聚焦事件 */
  onFocus() {
    this.setData({ searchShow: true })
  },

  /** 取消按钮点击事件 */
  onCancel() {
    this.setData({ searchShow: false })
  },

  /** 搜索框内容变化 */
  onChange({ detail }) {
    this.setData({ keywords: detail })
  },

  /** 搜索列表，城市选中事件 */
  async onSearchCity({ detail }) {
    // await wx.$.u.waitAsync(this, this.onSearchCity, [{ detail }])
    this.setData({ searchShow: false, keywords: '' })
    const { selectCities } = this.data
    const index = selectCities.findIndex((city) => city.id == detail.id)
    if (index === -1) {
      this.verificationCity(detail, true)
    } else {
      this.headerPageScrollTo(detail)
    }
  },
}))

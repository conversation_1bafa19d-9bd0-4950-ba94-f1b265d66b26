import { pageCodeArr } from './pageCode'

export type PageCodeType = typeof pageCodeArr[number]['pageCode'];
/**
 * description: 获取当前页面pageCode
 */
export function getPageCode(): PageCodeType | '' {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const page = pageCodeArr.find(item => item.path.indexOf(currentPage?.route) !== -1)
  return page ? page.pageCode : ''
}

/**
 * description: 点击资源埋点
 * @param {object} resourceBit 资源位数据
 * @param {string} resource_code 资源code
 * @param {string} link_url 资源链接
*/
export function clickPoint(resourceBit, resource_code, link_url) {
  if (!resourceBit || !resource_code) {
    return
  }
  const page_code = getPageCode()
  wx.$.collectEvent.event('resourceBitClick', {
    page_code,
    location_code: resourceBit.locationCode || (resourceBit.items && resourceBit.items[0].locationCode) || '',
    resource_type: resourceBit.type ? resourceBit.type.toString() : '',
    link_url,
    resource_code: [resource_code.toString()],
  })
  wx.$.collectEvent.event('universal_click', {
    bis: 'ypzp',
    s_t: 'RESOURCE',
    code: resource_code.toString(),
  })
}

/**
 * description: 关闭资源埋点
 * @param {object} resourceBit 资源位数据
 * @param {string} resource_code 资源code
 */
export function closePoint(resourceBit, resource_code, pageCode?) {
  if (!resourceBit || !resource_code) {
    return
  }
  const page_code = pageCode || getPageCode()
  wx.$.collectEvent.event('resourceBitClose', {
    page_code,
    location_code: resourceBit.locationCode || (resourceBit.items && resourceBit.items[0].locationCode) || '',
    resource_type: resourceBit.type ? resourceBit.type.toString() : '',
    resource_code: [resource_code.toString()],
  })
}

/**
 * description: 轮播&金刚区资源曝光埋点
 * @param {object} resourceBit 资源位数据
 * @param {string} resource_code 资源code
*/
export function swiperExposurePoint(resourceBit, resource_code, pageCode?) {
  if (!resourceBit || !resource_code) {
    return
  }
  const page_code = pageCode || getPageCode()
  if (!page_code) {
    return
  }
  wx.$.collectEvent.event('resourceBitExposure', {
    page_code,
    location_code: resourceBit.locationCode || (resourceBit.items && resourceBit.items[0].locationCode) || '',
    resource_type: resourceBit.type ? resourceBit.type.toString() : '',
    resource_code: wx.$.u.typeOf(resource_code) === 'array' ? [...resource_code] : [resource_code.toString()],
  })
}

/**
 * description: 轮播&金刚区资源曝光埋点
 * @param {Array} resourceList 该资源位所有数据
 * @param {Array} isExposureArr 已曝光资源位数组
 * @param {string} viewId 监听区域
 * @param {string} itemId 监听的节点
 * @param {Number} initialRatio 初始的相交比例，如果调用时检测到的相交比例与这个值不相等且达到阈值，则会触发一次监听器的回调函数。
 * @param {Array} thresholds 一个数值数组，包含所有阈值。
 * @param {Boolean} isStop 停止监听
*/
export function scrollExposurePoint({ resourceList, viewId, itemId, initialRatio, thresholds, isStop = false }) {
  if (!resourceList) {
    return
  }
  if (this.observer) {
    this.observer.disconnect()
  }

  this.observer = this.createIntersectionObserver({ thresholds, initialRatio, observeAll: true })
  this.observer.relativeTo(viewId)
  this.observer.observe(itemId, ({ dataset }) => {
    const { item } = dataset
    const { isExposureArr } = this.data
    if (!isExposureArr.includes(item.resourceCode) && !isStop) {
      isExposureArr.push(item.resourceCode)
      this.setData({ isExposureArr })
      swiperExposurePoint(resourceList, item.resourceCode)
    }
  })
}

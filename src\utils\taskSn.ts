import { getState, storage } from '@/store/index'

// 任务一般场景值 收藏夹，桌面
const taskSense = [1010, 1223, 1023]
// 任务兼容场景值 我的小程序
const compatibleTaskSense = [1103, 1104, 1257]
/**
 * 基于通知渠道建设需求任务编号上报场景值
 * @param {string} sn 任务中心的任务编号
 */
const reportSn = (sn) => {
  wx.$.javafetch['POST/marketingcenter/v1/taskMemberRecord/completeTheTask']({ sn, terminal: 4 })
    .then(({ data }) => {
      if (data && data.isTrue) {
        storage.setItemSync('taskCompleteQueryTimes', {
          ...storage.getItemSync('taskCompleteQueryTimes'),
          [sn]: 1,
        })
      }
    })
    .catch(() => {
      /** noop */
    })
}
// 上报添加关注收藏任务完成
export const reportSnByScene = (optionParams) => {
  if (getState().storage.userState.login) {
    // 用户已经登录
    const queryTimes = storage.getItemSync('taskCompleteQueryTimes')
    if (taskSense.includes(optionParams.scene)) {
      // 获取场景值
      if (optionParams.scene == 1010 && !queryTimes['new:collectMini']) {
        // 收藏夹
        reportSn('new:collectMini')
      }
      if ((optionParams.scene == 1223 || optionParams.scene == 1023) && !queryTimes['new:addMiniToDesktop']) {
        // android 桌面
        reportSn('new:addMiniToDesktop')
      }
    }
    const s = 'new:addMiniToMyMini'
    if (compatibleTaskSense.includes(optionParams.scene)) {
      if (optionParams.scene == 1257 && !queryTimes[s]) {
        reportSn(s)
      } else {
        wx.getSystemInfo({
          success: (res) => {
            const SDKVersion = res ? res.SDKVersion : '0'
            if (SDKVersion > '2.29.0' && [1103, 1104].includes(optionParams.scene) && !queryTimes[s]) {
              reportSn(s)
            }
          },
        })
      }
    }
  }
}

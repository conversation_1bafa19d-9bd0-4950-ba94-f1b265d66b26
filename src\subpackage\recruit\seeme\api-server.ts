import { APIJavaService } from '@/utils/request/index'
import { PLTools } from '@/utils/index'
import { resumeCardFormatDataV4 } from '@/utils/helper/resume/index'

/** @name 获取谁看过我的好活师傅👷🏻‍♂️列表 */
export async function getWhoSeeMeRecruitWorkerListApi(isRefresh: boolean) {
  const { query, pageInfo, hiddenNumber } = this.data
  let newHideNumber = hiddenNumber || 0
  try {
    const tempReqParams = PLTools.getReqPLStatus(pageInfo, { isRefresh })
    this.setData({ pageInfo: tempReqParams })

    const { page: currentPage, pageSize } = tempReqParams
    const res = await APIJavaService('POST/clues/v2/browse/browseMeJob', { jobId: query.jobId, currentPage, pageSize })
    const newList = (resumeCardFormatDataV4(res.data?.data) || []).map(item => ({ ...item }))
    const paginator = res.data?.paginator || { hiddenNumber: 0 }
    const tempResParams = PLTools.getResPLStatus(tempReqParams, { newList, fmtListItem, isRefresh, customFinishPageSize: 0, totalPage: paginator.totalPage })
    if (currentPage < 2) {
      newHideNumber = 0
    }
    this.setData({
      pageInfo: tempResParams,
      isFirstLoad: false,
      hiddenNumber: newHideNumber + paginator.hiddenNumber,
    })
  } catch (error) {
    this.setData({
      pageInfo: PLTools.getInitPLStatus(pageInfo),
    })
  }

  function fmtListItem(it) {
    const userName = !it.userName || it.userName === 'null' ? '' : it.userName || ''
    return { ...it, userName }
  }
}

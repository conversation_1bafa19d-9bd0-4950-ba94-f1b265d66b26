import { actions, dispatch, storage, store } from '@/store/index'
import { isIos } from '@/utils/tools/validator/index'
import { publishPop, refreshCord, setIndexModal, userPointGuide } from './popup'
import { uploadDialog } from '@/utils/helper/dialog/index'
import { tryPromise } from '@/utils/tools/common/index'

/**
 * 特殊弹窗
 * @param currDialog 当前弹窗
 * @param currDialogConfig 当前弹窗配置
 */
export function dealSpecialDialog(currDialog, ext?) {
  // 特殊弹窗用data里返回的数据
  const { popCode: dialog_identify } = currDialog || {}
  // 处理结果
  const result: any = {
    currDialog,
    showPublishModal: false,
    // showRecommendPop: false,
    showResumePop: false,
    showResumeImprovement: false,
  }
  const { logicTransferData: data = {} } = currDialog || {}
  // 是否上报
  let report = true
  const { shouYeToPublishResume } = store.getState().storage
  switch (dialog_identify) {
    // 刷新置顶弹窗
    case 'refresh_resume':
    case 'refresh_resume_new1':
    case 'refresh_resume_new2':
      // 物流不弹窗
      if (data.popup && data.popup?.special_type === 3) {
        report = false
      } else {
        // 首页名片刷新加急引导弹窗当天弹出次数(v3.6.3 只弹一次)
        const indexResumeRefreshNum = storage.getItemSync('indexResumeRefreshNum')
        !indexResumeRefreshNum && refreshCord(currDialog)
      }
      break
    // 获取积分弹窗
    case 'integral_too_few':
      userPointGuide(dialog_identify)
      break
    // 发布找活弹窗
    case 'post_resume':
      publishPop(dialog_identify)
      break
    case 'changeCity': // 城市切换温馨提示弹框
      result.showCitySelectPop = true
      break
    case 'resumeUp':
      result.showResumePop = true
      break
    case 'resume_Improvement':
      result.showResumeImprovement = true
      break
    case 'sdmsyytc':
      wx.$.model.interviewInvite({ info: currDialog.logicTransferData, isHome: true })
      break
    case 'market_giftToAccount': {
      const { logicTransferData } = currDialog || {}
      wx.$.model.streamerModel({ isQueue: true, dialogKey: 'market_giftToAccount', logicTransferData })
      break
    }
    case 'coupon_arrived': {
      const { logicTransferData } = currDialog || {}
      wx.$.model.streamerModel({ isQueue: true, dialogKey: 'coupon_arrived', logicTransferData })
      break
    }
    case 'share_first':
      wx.$.model.inviteWorkers({
        sharePath: 'findWorkerList/tanKuangSharepath',
        sharePage: 'mainFindWorkerList',
        shareName: '找工作列表',
        dialog_identify: 'share_first',
      })
      report = false
      break
    default:
      report = false
  }
  if (!shouYeToPublishResume) {
    dispatch(actions.storageActions.removeItem('shouYeToPublishResume'))
  }
  dealSpecialDialogMd(report, dialog_identify)
  return result
}

// 临时拆除的方法
const dealSpecialDialogMd = (report, dialog_identify) => {
  if (report) {
    // 缓存设置当前弹出的弹窗
    setIndexModal(true)
    // 缓存是否请求过
    storage.setItemSync('indexGetDialog', new Date().getTime())
    // 上报弹窗 (智能推荐岗位弹窗内部上报 , 信息流完善简历弹窗内部上报不走这里)
    dialog_identify !== 'zhinengtuijiangangwei' && dialog_identify !== 'resumeUp' && uploadDialog({ popCode: dialog_identify, action: 1 })
  }
}

/**
 * 关闭时用户行为上报
 * @param currConfig 当前弹窗
 * @param action 具体关闭弹窗触发行为
 * @param text 文案
 */
export function closePop(currConfig, action = 2, text = '') {
  const { dialogData, popCode: dialog_identify = '' } = currConfig
  const { dialogIdentify = '' } = dialogData || {}
  const newDialogIdentify = dialogIdentify || dialog_identify
  let currVisible = ''
  switch (newDialogIdentify) {
    case 'changeCity':
      currVisible = 'showCitySelectPop'
      break
    case 'resume_Improvement':
      currVisible = 'showResumeImprovement'
      break
    case 'resumeUp':
      currVisible = 'showResumePop'
      break
    default:
      console.log('匹配弹窗失败', currConfig)
  }
  /** 上报关闭事件 */
  action && uploadDialog({ popCode: newDialogIdentify, action, text })
  /** 重置弹窗状态 */
  setIndexModal(false)
  return currVisible
}

/** 弹框是否需要被过滤掉 */
export const isFilterDialog = function (result): boolean {
  // 如果是卡券弹框-iso环境需过滤掉，不虚要再弹出
  if (isIos() && result && `${result.dialog_identify}`.indexOf('IssueCoupon') !== -1) {
    return true
  }
  return false
}

/**
 * 牛人用户启动时，自动跳转快速发布简历页
 */
export const dealResumePublishPop = async function () {
  const { userState } = store.getState().storage
  const isRequestGuideToPublish = storage.getItemSync('isRequestGuideToPublish')
  const isEnterPublish = storage.getItemSync('isEnterPublish')
  const { login } = userState || {}
  if (!login || isEnterPublish || isRequestGuideToPublish) {
    return false
  }
  const res = await tryPromise(wx.$.javafetch['POST/resume/v3/guide/guideToPublish'](), {})
  const { data } = res || {}
  const { hasRecruitModeLeafOccLabel, resumeExist } = data || {}
  let status = false
  if (data && hasRecruitModeLeafOccLabel && !resumeExist) {
    status = true
    storage.setItemSync('isRequestGuideToPublish', true)
    wx.$.r.push({ path: '/subpackage/resume/resume_publish/index?origin=complete' })
  } else if (resumeExist) {
    storage.setItemSync('isRequestGuideToPublish', true)
  }

  return status
}

/** 横幅弹窗数组 */
export const streamerModelArr = [
  'market_giftToAccount',
  'coupon_arrived',
]

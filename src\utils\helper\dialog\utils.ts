import dayjs from '@/lib/dayjs/index'
import { storage } from '@/store/index'
import { IRuleDialog, IRule } from './index.d'

type IDialogRuleShow = {
  /** 弹框的唯一标识 */
  dialogIdentify: string,
  /** 是否触发出现规则 */
  isRule: boolean,
  /** 是否自动增加弹出频次 */
  isFrequency: boolean,
  /** 弹框的弹出规则类型 */
  rule: IRuleDialog
}

/**
 * 传入的时间戳是否大约当前时间+minutes
 * @params time 毫秒时间戳
 * @params minutes 分钟
 */
function isTimeOut(time: number, minutes: number) {
  const now = new Date().getTime()
  const diff = (now - time) / 1000
  return diff >= minutes * 60
}

/** 获取弹框的缓存 */
function getStoreRuleDialog(dialogIdentify: string) {
  const dialogRules = storage.getItemSync('dialogRule') || {}
  if (dialogRules && dialogRules[dialogIdentify]) {
    let dialogRule = dialogRules[dialogIdentify]
    if (typeof dialogRule !== 'object') {
      return null
    }
    dialogRule = wx.$.u.deepClone(dialogRule) // 深拷贝
    const { timestamp, showNum } = dialogRule
    if (timestamp && showNum) {
      const thatFormat = dayjs().format('YYYY-MM-DD')
      const timestampFormat = dayjs(timestamp).format('YYYY-MM-DD')
      /** 如果当前时间和弹出的日期不同 设置为0 */
      if (thatFormat != timestampFormat) {
        dialogRule.showNum = 0
      }
    }
    return dialogRule
  }
  return null
}

/** 设置弹框的缓存 */
export function setStoreRuleDialog(dialogIdentify: string, rule: IRule) {
  const timestamp = new Date().getTime()
  let dialogRules = storage.getItemSync('dialogRule')
  if (!dialogRules || typeof dialogRules !== 'object') {
    dialogRules = {}
  } else {
    dialogRules = wx.$.u.deepClone(dialogRules)
  }

  const showNum = wx.$.u.getObjVal(dialogRules, `${dialogIdentify}.showNum`, 0)
  const saveData = {
    timestamp,
    showNum: showNum ? +showNum + 1 : 1,
    rule,
  }
  dialogRules[dialogIdentify] = saveData
  storage.setItemSync('dialogRule', dialogRules)
  return dialogRules
}

/**
 * 判断是否需要处理弹框的弹出规则
 * @param {IDialogRuleShow} options 参数
 * @param isRule 是否触发出现规则
 * @param rule 弹框的弹出规则类型
 * @param isFrequency 是否需要增加弹出频次
 * @returns 是否弹出通用弹框
 */
export function dialogRuleShow(options: IDialogRuleShow): boolean {
  try {
    const { dialogIdentify, isRule, isFrequency, rule } = options
    if (!isRule || !rule) {
      return true
    }
    if (typeof rule !== 'object') {
      return true
    }
    const { showLimit, showInterval, closeInterval, clickInterval } = rule
    // const clickInterval
    const storeRule = getStoreRuleDialog(dialogIdentify)
    if (!storeRule) {
      isFrequency && setStoreRuleDialog(dialogIdentify, rule)
      return true
    }
    const timestamp = storeRule.timestamp || 0 // 最后一次弹出的毫秒时间戳
    const showNum = storeRule.showNum || 0 // 已弹出的次数
    if (showLimit && showLimit <= showNum) {
    // 当天弹出的次数大于目前弹出的总次数
      return false
    }
    if (showInterval && !isTimeOut(timestamp, showInterval)) {
    // 弹出的时间间隔大于当前时间间隔
      return false
    }
    if (closeInterval && !isTimeOut(timestamp, closeInterval)) {
    // 关闭的时间间隔大于当前时间间隔
      return false
    }
    if (clickInterval && !isTimeOut(timestamp, clickInterval)) {
    // 点击的时间间隔大于当前时间间隔
      return false
    }
    isFrequency && setStoreRuleDialog(dialogIdentify, rule)
  } catch (e) {
    return true
  }

  return true
}

import { actions, dispatch, store } from '@/store/index'
import { dealDialogRepByApi, dialogTemplateHtml } from '@/utils/helper/dialog/index'
import { APIJavaService } from '@/utils/request/index'

import { buryingPointOfResumeTop } from './burying-point'
import { sixEquityImg, threeEquityImg } from './config'
import { getFmtDaysArr } from './utils'
import { changeUserRole } from '@/utils/helper/member/communicate'
import { isIos } from '@/utils/tools/validator/index'

/** @name 更新期望找活工作地 */
async function updateHopeAreaApi(hopeAreaIds: any[]) {
  try {
    await APIJavaService('POST/resume/v1/resumeBasic/updateHopeArea', { hopeAreaIds })
    return true
  } catch (error) {
    console.error(error)
    const msg = error?.message
    if (msg) {
      wx.$.msg(msg)
    }
    return false
  }
}

/**
 * @name 获取加急找活权益图片
 * @returns boolean true 6 六大权益 false 3 三大权益
 */
async function getUpgradeResumeImgsApi() {
  try {
    const res = await APIJavaService('POST/resume/v1/resumeBasic/rightsUrgentLogo')
    return res.data.config == 6
  } catch (error) {
    return false
  }
}

/**
 * @name 获取找活置顶配置信息
 */
export async function getResumeTopConfigInfoApi() {
  try {
    const isTopSix = await getUpgradeResumeImgsApi()
    const { data } = await wx.$.javafetch['POST/resume/v1/resumeBasic/globalDefaultConfig']({})
    const urgentImgs = isTopSix ? sixEquityImg : threeEquityImg
    return { topConfig: { ...data.topUrgent, topPrivilege: isTopSix, daysArr: getFmtDaysArr(data.topUrgent.days || []) }, urgentImgs }
  } catch (err) {
    const content = err?.message || '网络异常，请稍后重试'
    wx.$.alert({ content, confirmText: '返回' }).then(() => wx.$.r.back())
    return null
  }
}

/**
 * @name 获取加急订单信息
 */
export async function getUpgradeOrderInfoApi() {
  try {
    const { data } = await APIJavaService('POST/resume/v1/resumeBasic/queryUrgent')
    return data || {}
  } catch (err) {
    return {}
  }
}

/**
 * @name 确定加急功能
 */
export async function toppingResumeInfoApi(rechargeData = {}) {
  const { upgradeOrderInfo } = this.data
  wx.showLoading({ title: '正在加急...', mask: true })
  try {
    // 找活名片流氓弹窗时间和状态设置
    const { firstSetTopTimeBack } = store.getState().storage
    if (firstSetTopTimeBack.status == 1) {
      const immer = (state) => {
        state.firstSetTopTimeBack.status = 2
        state.firstSetTopTimeBack.time = new Date().getTime()
      }
      dispatch(actions.storageActions.setItem({ key: 'firstSetTopTimeBack', immer }))
    }

    // 2 置顶中 调用修改接口
    if (upgradeOrderInfo.topStatus == 2 || upgradeOrderInfo.topStatus == 4) {
      await modResumeTopInfoApi.call(this, rechargeData)
      return
    }
    await createResumeTopInfoApi.call(this, rechargeData)
  } catch (error) {
    console.error(error)
    wx.hideLoading()
  }

  /** @name 修改加急找活信息 */
  async function modResumeTopInfoApi(rechargeData = {}) {
    const { form, upgradeOrderInfo } = this.data
    try {
      const extendDays = Number(form.time || 0)
      const areaIds = form.city.map((it) => it.id || '').filter(Boolean) || []
      const reqParams = { ...rechargeData, preOrderId: upgradeOrderInfo.orderId, extendDays, areaIds }
      const res = await APIJavaService('POST/resume/v1/resumeBasic/confirmUrgentUpdate', reqParams, { hideMsg: true })
      handleRechargeSuccess.call(this, res.message)
    } catch (res) {
      handleRechargeFail.call(this, res)
    }
  }

  /** @name 创建加急找活信息 */
  async function createResumeTopInfoApi(rechargeData = {}) {
    const { form } = this.data
    try {
      const day = Number(form.time || 0)
      const areaIds = form.city?.map((it) => it.id || '').filter(Boolean) || []
      const reqParams = { ...rechargeData, areaIds, day }
      const res = await APIJavaService('POST/resume/v1/resumeBasic/confirmUrgent', reqParams, { hideMsg: true })
      handleRechargeSuccess.call(this, res.message)
    } catch (res) {
      handleRechargeFail.call(this, res)
    }
  }

  /** @name 加急成功(置顶成功)情况 */
  function handleRechargeSuccess(message) {
    const { form, myResumeDetails } = this.data
    buryingPointOfResumeTop(form, '成功')
    wx.hideLoading()
    wx.$.msg(message).then(() => goToRecruitListPage(myResumeDetails))
  }

  /** @name 加急失败(置顶失败) */
  async function handleRechargeFail(res: any) {
    const { form } = this.data
    buryingPointOfResumeTop(form, '失败')
    wx.hideLoading()

    const resData = res?.data || {}
    if (res?.code == 651) {
      // 积分不足
      showRechargePopup.call(this, res)
      return
    }

    const dialogIdentify = resData.dialogData?.dialogIdentify
    const dialogData = dialogIdentify === 'xiugaitishi' ? { city: form.city[0].name } : null
    const popupInfo = await dealDialogRepByApi(dialogIdentify, dialogData)
    if (popupInfo) {
      const dialogStrategy = {
        GQCXJJ: (btnRes) => {
          // 加急过期 重新加急弹窗
          if (btnRes.jumpEventType == 4) {
            // 重新加急->展示修改加急 UI
            // this.setData({ showModUpgradeUI: true })
            this.initUpgradeResumePageInfo()
          }
        },
        YYJJCG: (btnRes) => {
          // 预约加急成功弹窗
          if (btnRes.jumpEventType == 4) {
            goToRecruitListPage(this.data.myResumeDetails)
          }
        },
        CZCGJJ: (btnRes) => {
          // 充值成功(且已加急成功)弹窗
          // 更新当前页面数据
          btnRes.jumpEventType == 4 && this.initUpgradeResumePageInfo()
        },
        xiugaitishi: async (btnRes) => {
          // 一键替换置顶城市为期望工作地 弹窗
          const myResumeDetails = this.data.myResumeDetails || {}
          if (btnRes.jumpEventType != 4) {
            return
          }
          if (btnRes.routePath == 'MyResumeDetail') {
            // 按钮-一键替换
            const modHopeAreaId = form.city[0].id
            const updateResult = await updateHopeAreaApi([modHopeAreaId])
            if (updateResult) {
              wx.$.msg('修改成功', null, true).then(() => goToRecruitListPage(myResumeDetails, modHopeAreaId))
              return
            }
            return 'noCloseShowModulePopup'
          }
          if (btnRes.routePath == 'recruitList') {
            // 按钮-不用了
            wx.$.msg('加急成功!', null, true).then(() => {
              // 按钮-不用了
              goToRecruitListPage(myResumeDetails)
            })
          }
        },
        YJJCXJJ: (btnRes) => {
          // 已加急 修改加急弹窗 (貌似未找活页面弹窗 单击 去修改 按钮跳转到该页面)
          if (btnRes.jumpEventType == 4) {
            // wx.$.router.replace({ path: '/subpackage/topset/topmset/index' })
            const urlTop = encodeURIComponent('/urgent-resume?epc=active_jiajijianlih5')
            wx.$.r.push({
              path: `/subpackage/web-view/index?url=${urlTop}&isLogin=true`,
            })
          }
        },
      }
      wx.$.showModal({ ...popupInfo, success: dialogStrategy[dialogIdentify] })
      return
    }

    // 找活名片审核中 toast 提示 审核通过后才可加急
    const message = res?.message || '网络异常，请稍后重试'
    wx.$.msg(message)

    function showRechargePopup(res) {
      if (isIos()) {
        const richContent = dialogTemplateHtml(res.data)
        wx.$.confirm({ title: '温馨提示', richContent, confirmText: '获取积分', cancelText: '取消' }).then(() => wx.$.toGetIntegral())
        return
      }

      // 非苹果端 - 充值逻辑
      const resData = res?.data || {}
      this.setData({ rechargeNum: resData.integral, showRechargePopup: true, rechargePopupHeaderNodes: dialogTemplateHtml(resData) })
    }
  }

  /**
   * @name 跳转到招工大列表
   * @description 点击【查看招工信息】并跳转招工大列表（附带工种、地区信息；多个期望地默认选择第一个期望地，多个工种：仅工程或工程+餐饮 跳首页；工程+工厂+物流+餐饮 跳首页；工厂+物流+餐饮 跳物流专区；工厂+餐饮 跳工厂专区）；
   * @link https://axure.vrtbbs.com/app/project/rfovt3/preview/kwycud
   */
  async function goToRecruitListPage(myResumeDetails, modHopeAreaId = '') {
    const { basicResp, occIds } = myResumeDetails || {}
    const { hopeAreaCity } = basicResp || {}
    let area_id = ''
    try {
      area_id = modHopeAreaId || (wx.$.u.isArrayVal(hopeAreaCity) ? hopeAreaCity[0].id : '')
    } catch (error) {
      console.error(error)
      area_id = ''
    }

    await changeUserRole(2)
    try {
      const mainIds = occIds?.join(',') || ''
      if (mainIds) {
        wx.$.r.reLaunch({
          path: '/pages/index/index',
          params: {
            area_id,
            classify_id: mainIds,
          },
        })
        return
      }
    } catch (error) {
      console.error(error)
      wx.$.r.reLaunch({
        path: '/pages/index/index',
        params: {
          area_id,
        },
      })
      return
    }
    wx.$.r.reLaunch({
      path: '/pages/index/index',
      params: {
        area_id,
      },
    })
  }
}

type preSettlementData = YModels['POST/resume/v1/resumeBasic/preSettlement']['Res']['data']
interface getExpendIntegralServiceParams {
  day: number
  areaIds: number[]
  orderId: any
}

/**
 * @name 获取加急找活消耗积分信息服务
 * @param params.day 天数
 * @param params.areaIds 区域
 * @param isMod 是否为预修改确定加急查询
 */
export async function getExpendIntegralService(params: getExpendIntegralServiceParams, isMod: boolean) {
  const { day, areaIds, orderId: preOrderId } = params
  const initData: preSettlementData = {
    userId: 0,
    originPrice: 0,
    discountPrice: 0,
    /** 是否无正式积分 */
    noNormalIntegral: true,
    /** 所需积分 */
    needIntegral: 0,
  }
  if ((!isMod && day <= 0) || areaIds.length <= 0) {
    return initData
  }

  try {
    const res = isMod
      ? await APIJavaService('POST/resume/v1/resumeBasic/preSettlementUpdate', { extendDays: day, areaIds, preOrderId })
      : await APIJavaService('POST/resume/v1/resumeBasic/preSettlement', { day, areaIds })
    return res.data || initData
  } catch (err) {
    console.error(err)
    return initData
  }
}

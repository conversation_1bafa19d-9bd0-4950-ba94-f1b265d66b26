/**
 * 单例化请求，通过闭包实现同接口同参数的请求只发起一次,主要应对与配置化接口，对时效性较高的接口请勿使用本方式
 * 特点：
 * 1 缓存对应参数请求的Promise，下次请求参数如果相同会直接返回Promise
 * 2 如果报错会自动删除本次缓存，下次请求参数如果相同会重新发起请求
 * 3 函数带有缓存上限配置默认5个
 * 4 函数带有detach方法，用于清除缓存，下次请求参数如果相同会重新发起请求
 * @param fn 请求函数
 * @param limit 缓存上限
 *
*/
const handleRequestSingleton = function <T extends RequestFunction> (fn: T, limit = 5) {
  let map: Map<string, any>
  const _handler = function _(...args) {
    map = map || new Map()
    const keyPath = JSON.stringify(args)
    const promise = (map.get(keyPath) || fn(...args))
    /** 处理缓存抵达上限 */
    if (map.size >= limit) {
      map.delete(map.keys().next().value)
    }

    if (!map.has(keyPath)) {
      map.set(keyPath, promise)
    }
    /**
     * 此处拦截Promise<Error>，如果报错清理掉Promise，避免下次请求无法发起新的请求
     */
    return promise.catch(err => {
      if (map && map.has(keyPath)) map.delete(keyPath)
      throw err
    })
  }

  _handler.detach = () => {
    map && map.clear()
    map = undefined
  }
  return _handler as unknown as T & { detach: () => void }
}

type RequestFunction<P extends any[] = any, R extends Promise<any> = Promise<any> > = (...args: P)=> R

/**
 * @see {@link handleRequestSingleton} 单例化接口请求
 */
export const hrs = handleRequestSingleton

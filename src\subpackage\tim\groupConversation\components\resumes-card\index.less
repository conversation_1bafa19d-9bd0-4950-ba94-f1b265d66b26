.out-v {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  margin: 0 24rpx 40rpx;
}

.out-start {
  justify-content: flex-start;
}

.resumes-content {
  position: relative;
  background: rgba(255, 255, 255, 1);
  border-radius: 24rpx 24rpx 4rpx 24rpx;
  padding: 24rpx;
  width: 542rpx;
}

.yzd-icon {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 80rpx;
  height: 80rpx;
}

.user-info {
  display: flex;
}

.user-info-avater {
  width: 96rpx;
  height: 96rpx;
  margin-right: 12rpx;
}

.user-info-o {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
}

.yzd-icon {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 80rpx;
  height: 80rpx;
}

.user-info-name {
  display: flex;
  font-size: 34rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
}

.user-info-auth {
  height: 44rpx;
  margin-left: 8rpx;
  border-radius: 8rpx;
  border: 2rpx solid rgba(224, 243, 255, 1);
  color: rgba(0, 146, 255, 1);
  font-weight: bold;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

.user-info-hometown {
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.85);
  margin-top: 16rpx;
  .ellip()
}

.classly-p {
  display: flex;
  margin-top: 16rpx;
}

.classly-content {
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  margin: -4rpx;
}

.classly-txt {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44rpx;
  color: rgba(0, 0, 0, 0.65);
  font-size: 26rpx;
  background: rgba(245, 246, 250, 1);
  border-radius: 8rpx;
  padding: 0 12rpx;
  margin: 4rpx;
}

.detail-txt {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.25);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
  margin-top: 16rpx;
}

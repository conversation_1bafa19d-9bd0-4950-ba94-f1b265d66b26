import { GUARD_APP_ID } from '@/config/app'
import { actions, dispatch, messageQueue, store } from '@/store/index'
import { getShareReq } from '@/utils/helper/share/index'

type IFetchLoginUserInfo = YModels['POST/account/v1/userBase/getLoginUserInfo']
type IFetchTicketLogin = YModels['POST/account/v1/login/ticketLogin']
type IFetchSnsCode2Session = YModels['POST/account/v1/sns/getSnsCode2Session']
type IFetchSnsCode2SessionRes = YModels['POST/account/v1/sns/getSnsCode2Session']['Res'] & {
  data: {
    /** 授权用户openid */
    openid: string
    /** 授权用户unionid */
    unionid: string
  }
}

/** 获取微信小程序三方信息: /login/mobile/smUserInfo,login/mobile/ticketLogin --> /account/v1/sns/getSnsCode2Session */
export function fetchSnsCode2Session(params: IFetchSnsCode2Session['Req']): Promise<IFetchSnsCode2SessionRes> {
  return wx.$.javafetch['POST/account/v1/sns/getSnsCode2Session']({
    appId: wx.$.miniConfig.appid,
    grantType: 'authorization_code',
    ...params,
  }).then((res: any) => {
    if (res.code == 0) {
      const resData = res.data
      // eslint-disable-next-line no-param-reassign
      res.data = {
        ...resData,
        openid: resData.openId,
        unionid: resData.unionId,
      }
      wx.$.collectEvent.config({ mini_openid: resData.openId, mini_unionid: resData.unionId })
    }
    return res
  }).catch((err) => {
    return err
  })
}

/** 获取登录用户信息: job/member/init --> /account/v1/userBase/getLoginUserInfo  */
export function fetchLoginUserInfo(headers = {}, params: IFetchLoginUserInfo['Req'] = {}): Promise<IFetchLoginUserInfo['Res'] & { data: { token: string } }> {
  return wx.$.javafetch['POST/account/v1/userBase/getLoginUserInfo']({
    ...params,
  }, {
    headers,
  }).then((res) => {
    const { data } = res || {}
    const { entangledUserInfo } = data || {} as any
    dispatch(actions.storageActions.setItem({ key: 'entangledUserInfo', value: entangledUserInfo || {} }))
    return res
  }).catch((err) => {
    return err
  })
}

/** 自动登录:(不做任何提示) login/mobile/ticketLogin --> /account/v1/login/ticketLogin  */
export function fetchTicketLogin(params: IFetchTicketLogin['Req'] = {}): Promise<IFetchTicketLogin['Res']> {
  const { loginAuthData } = store.getState().storage
  const shareReq = getShareReq()
  const ticket = loginAuthData.loginTicket
  const newParams = {
    /** 自动登录票据。接口/account/v1/sns/getSnsCode2Session接口返回的值 */
    ticket,
    shareReq,
    ...params,
  }
  return wx.$.javafetch['POST/account/v1/login/ticketLogin'](newParams, { hideMsg: true })
    .catch((err) => {
      return err
    })
}

/** 极验-设备验，提交gee_token */
export async function fetchDeviceValid() {
  const guard = await getGeeToken()
  await messageQueue(store => store.storage.loginAuthData)
  const loginAuthData = store.getState().storage.loginAuthData || {}
  wx.$.javafetch['POST/griffin/v1/gee/deviceValid']({
    appId: GUARD_APP_ID,
    openId: loginAuthData?.openId,
    geeToken: guard ? guard.gee_token : '',
    scene: 1,
  }, { hideMsg: true })
}

// 极验-设备验
const getGeeToken = (): any => {
  return new Promise((resolve, reject) => {
    try {
      const guard = requirePlugin('guard')
      guard.load({ appId: GUARD_APP_ID })
        .then((data) => {
          resolve(data.data)
        })
        .catch((e) => {
          reject(e)
        })
    } catch (e) {
      reject('')
    }
  })
}

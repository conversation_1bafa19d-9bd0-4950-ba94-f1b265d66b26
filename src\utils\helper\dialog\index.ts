import { storage, store } from '@/store/index'
import { post_resume1Def } from '@/store/model/storage/defData'
import { getPageCode } from '../resourceBit/index'
import type { IRuleDialog } from './index.d'
import { dialogRuleShow } from './utils'
import { transFromRuntime } from '@/utils/request/utils'
import { REQUEST_VERSION } from '@/config/app'

type ITemplateData = {
  /** 高亮替换的字符颜色 */
  'color': string,
  /** 替换的字符串 */
  'text': string,
  /** 替换的key */
  [key: string]: string | number
}

/** 特殊弹框的内容替换，返回一个富文本字符串 */
export function dialogTemplateHtml(templateData: ITemplateData): string {
  const { text, color } = templateData
  const html = text.replace(/{(.*?)}/g, (match, p1) => {
    if (templateData[p1] != null) {
      return `<span style="color:${color}">${templateData[p1]}</span>`
    }
    return match
  })
  return `<div>${html}</div>`
}

// 中间号弹框有我已知晓的code
const wyzxPopCodeList = ['zhenshihao_C', 'call_zhenshihao_B', 'call_zhenshihao_C', 'market_giftToAccount']

/**
 * 弹窗行为上报
 * @param popCode 当前弹窗code
 * @param action 用户行为，接受三个参数 1-弹出 2-关闭 3-确认
 * @param pageCode 当前页面code
 * @param ext 上报火山埋点所需数据
 * @returns
 */
export async function actionReport(options) {
  const { popCode, action, ext = {}, pageCode, isWyzx } = options || {}
  const { login } = store.getState().storage.userState
  const params: any = {
    action,
  }
  if (wyzxPopCodeList.includes(popCode) && isWyzx) {
    params.action = 2
  }
  let extDt = { ...ext }
  if (popCode) {
    params.popCode = popCode
  }
  const page_code = pageCode || getPageCode()
  params.pageCode = page_code
  if (Object.keys(ext).length == 0 || !ext.resource_code) {
    const list = await getDialogConfig()
    if (!list || list.length === 0) {
      return
    }
    /** 当前弹窗模版配置 */
    const currDialogConfig = list.find(itme => itme.popCode == popCode) || {}
    if (currDialogConfig) {
      extDt = {
        resource_code: [currDialogConfig.resourceCode],
        resource_sub_type: String(currDialogConfig.resourceSubType),
        resource_type: String(currDialogConfig.resourceType),
        page_code,
        location_code: '-99999',
        ...ext,
      }
      /** 活动弹窗数据 */
      const { activityDetail } = currDialogConfig
      /** 活动弹窗设置资源位标识 */
      if (activityDetail) {
        extDt.location_code = activityDetail.locationCode
      }
    }
  }
  try {
    /** 业务弹窗上报 */
    if (!(wyzxPopCodeList.includes(popCode) && !isWyzx && action == 2)) {
      login && wx.$.javafetch['POST/cms/popup/v1/showReport'](params, { hideMsg: true })
    }
    if (ext.event) {
      const { event, ...eventDt } = extDt
      wx.$.collectEvent.event(event, eventDt)
    }
    if (action == 3) {
      wx.$.collectEvent.event('universal_click', {
        bis: 'ypzp',
        s_t: 'POPUP',
        code: popCode,
      })
    }
  } catch (err) {
    console.error('err', err)
  }
}

/**
* 获取弹窗合集，每隔一天更新一次弹窗配置，7天更新一次弹窗模版
* @param refesh 是否手动更新
*/
export async function getDialogConfig(refesh?: boolean) {
  try {
    const { list, version, appVersion } = storage.getItemSync('dialogConfigList')
    if (refesh || !wx.$.u.isArrayVal(list)) {
      const res = await wx.$.javafetch['POST/cms/popup/v1/list']({ dataVersion: appVersion !== REQUEST_VERSION ? null : version }, { hideMsg: true, defer: true })
      const { data }: any = res || {}
      if (data && wx.$.u.isArrayVal(data.list)) {
        getDialogTemplate(true)
        storage.setItemSync('dialogConfigList', { list: data.list, version: data.dataVersion, appVersion: REQUEST_VERSION })
        return data.list
      }
    }
    return wx.$.u.isArrayVal(list) ? list : []
  } catch (err) {
    return []
  }
}

/** 获取弹窗模版 */
export async function getDialogTemplate(refesh?) {
  try {
    const template = storage.getItemSync('dialogTemplate')
    if (refesh || !wx.$.u.isArrayVal(template)) {
      const res = await wx.$.javafetch['POST/cms/popup/v1/listAllTemplate']({}, { hideMsg: true, defer: true })
      const data: any = res?.data
      if (data && data.list) {
        storage.setItemSync('dialogTemplate', data.list)
        return data.list
      }
      return null
    }
    return template
  } catch (err) {
    return null
  }
}
type TDealDialogP = RequiredProp<YModels['POST/cms/popup/v1/listByLocationWithAgg']['Req'], 'pageCodes'>
/**
 * 处理页面级弹窗请求逻辑
 * @param options 请求位置(string) | 请求参数{Object}
 * @param pageName 弹窗展示页面
 * @param report 弹窗曝光时上报的额外数据
 * @returns
 */
export async function dealDialog(options: string | TDealDialogP, pageCode?: string, report?: any) {
  const isStr = typeof options === 'string'
  const pageCodes = isStr ? [options] : options.pageCodes

  const params = {
    runtime: transFromRuntime(),
    /** 来源端 1-鱼泡网 */
    business: 'YPZP',
    ...(isStr ? {} : options.params || {}),
  }
  const newParams = isStr ? {
    pageCodes,
    params,
  } : {
    ...options,
    params,
    pageCodes,
  }
  const isLogin = store.getState().storage.userState.login
  // 未登录，不弹窗
  if (!isLogin || !wx.$.u.isArrayVal(pageCodes) || !pageCodes[0]) {
    return null
  }

  // const res = { code: 0, message: '请求成功', askId: '20b8f5727b5cb2f1', data: { list: [{ popCode: 'zhinengtuijiangangwei', popRank: 2, popType: 1, logicTransferData: { dialog_identify: 'zhinengtuijiangangwei' } }] }, error: false }
  const res = await wx.$.javafetch['POST/cms/popup/v1/listByLocationWithAgg'](newParams, { hideMsg: true })
  // console.log('res', res)

  if (res && res.data && res.data.list && res.data.list.length > 0) {
    const currDialog = res.data.list[0]
    const template = (currDialog && currDialog.logicTransferData) ? currDialog.logicTransferData.template || {} : {}
    /** 弹窗唯一key */
    const { popCode } = currDialog || {}
    return dealResult(popCode, currDialog, pageCode, report, template)
      .catch(() => null)
  }
  return null
}

/**
 * 处理非页面级单独业务弹窗逻辑
 * @param tkKey 弹框的key值，来自业务接口返回
 * @param pageCode 当前页面pageCode
 * @param report 弹窗曝光时上报的额外数据
 * @returns
 */
export async function dealDialogByApi(tkKey: string, pageCode?: string, report?: any) {
  // 未匹配不请求
  if (!tkKey) {
    return null
  }
  try {
    return await dealResult(tkKey, {}, pageCode, report)
  } catch (err) {
    console.log(err)
  }
  return null
}

/**
 * 处理非页面级单独业务弹窗逻辑,标题和内容有可替换内容,格式:{xxxx}...{xxxx}
 * @param tkKey 弹框的key值，来自业务接口返回
 * @param pageCode 弹窗展示页面pageCode
 * @param report 弹窗曝光时上报的额外数据
 * @param contentRep 可替换内容
 * @returns
 */
export async function dealDialogRepByApi(tkKey: string, contentRep?: any, pageCode?: string, report?: any, ext?: any) {
  // 未匹配不请求
  if (!tkKey) {
    return null
  }
  try {
    return await dealResult(tkKey, {}, pageCode, report, contentRep, ext)
  } catch (err) {
    console.log(err)
  }
  return null
}

/** 弹框参数选项配置 */
type IDealDialogOptions = {
  /** 弹框的key值，弹框标识，来自业务接口返回 */
  dialogIdentify: string,
  /** 弹框内容所要替换的变量对象 template */
  template?: { [key: string]: any } | null,
  /** 未匹配到弹框的toast消息提示 */
  msg?: string,
  /** 后台返回的弹框内容 */
  currDialog?: any,
  /** @deprecated 已不再需要 */
  pageCode?: '',
  /** @deprecated 已不再需要 */
  report?: '',
  /** 是否触发出现规则 */
  isRule?: boolean,
  /** 是否需要增加弹出频次-(部分特殊弹窗需要手动添加，确保数据准确性---resume_add2) */
  isFrequency?: boolean,
}

/**
 * 处理非页面级单独业务弹窗逻辑,标题和内容有可替换内容,格式:{xxxx}...{xxxx}
 * @param {Object} options 弹窗配置
 * @returns Promise<any>
 */
export async function dealDialogApi(options: IDealDialogOptions) {
  const { dialogIdentify,
    template = {},
    currDialog = {},
    msg = '',
    isRule = false,
    isFrequency = true,
    pageCode = '',
    report = '' } = options
  // 未匹配不请求
  if (!dialogIdentify) {
    return null
  }
  try {
    const popup = await dealResult(dialogIdentify, currDialog, pageCode, report, template, null, msg)
    if (!popup) {
      return null
    }
    const isShow = dialogRuleShow({
      dialogIdentify,
      isFrequency,
      isRule,
      rule: popup.ruleDialog,
    })
    if (!isShow) {
      return null
    }
    return popup
  } catch (err) {
    console.log('dealDialogApi', err)
  }
  return null
}

/** 根据弹框标识显示弹框
 * 处理非页面级单独业务弹窗逻辑,标题和内容有可替换内容,格式:{xxxx}...{xxxx}
 * @param {Object} options 弹窗配置
 * @returns Promise<any>
 */
export async function dealDialogShow(options: IDealDialogOptions) {
  const popup = await dealDialogApi({
    ...options,
  }).catch(() => null)
  if (!popup) {
    return { code: '500', message: '无弹窗', btnIndex: -1, itemClass: 'none' }
  }
  return wx.$.showModal({
    ...popup,
  })
}

/**
 * 弹窗公共处理部分
 * @param tkKey 弹窗标识
 * @param currDialog 后台返回的弹窗内容
 * @param report 弹窗曝光时上报的额外数据
 */
async function dealResult(tkKey: string, currDialog?: any, pageCode?: string, report?: any, contentRep?: any, ext?: any, msg?: string) {
  let list = await getDialogConfig()
  let templates = storage.getItemSync('dialogTemplate')
  /** 当前弹窗配置 */
  let currDialogConfig = list.find(itme => itme.popCode == tkKey) || {}

  if (!currDialogConfig || !currDialogConfig.popCode) {
    const dialogData = await getDialogConfig(true)
    if (!dialogData || dialogData.length === 0) {
      return null
    }
    // 重新获取最新配置
    list = dialogData
    currDialogConfig = list.find(itme => itme.popCode == tkKey) || {}
  }
  /** 未匹配到弹窗 */
  if (wx.$.u.isEmptyObject(currDialogConfig)) {
    return null
  }
  /** 当前弹窗状态是否关闭 */
  if (currDialogConfig.businessDetail && !currDialogConfig.businessDetail.popupStatus) {
    return null
  }
  if (!templates || templates.length === 0) {
    templates = await getDialogTemplate()
  }
  /** 当前弹窗匹配模版数据 */
  const currDialogTemplate = templates.find(item => currDialogConfig.businessDetail && item.templateId == currDialogConfig.businessDetail.templateId) || {}
  /** 业务弹窗数据 */
  const { businessDetail } = currDialogConfig
  /** 当前弹窗类型 */
  const { popType } = currDialogConfig
  /** 当前弹窗匹配的模版配置 */
  const { config } = currDialogTemplate
  /** 是否特殊弹窗 */
  const { special_design } = config || {}
  /** 标题和内容替换 */
  if (businessDetail && contentRep && !wx.$.u.isEmptyObject(contentRep)) {
    const keys = Object.keys(contentRep)
    keys.forEach(ky => {
      businessDetail.popupTitle = businessDetail.popupTitle ? businessDetail.popupTitle.replaceAll(`{${ky}}`, contentRep[ky]) : ''
      businessDetail.popupContent = businessDetail.popupContent ? businessDetail.popupContent.replaceAll(`{${ky}}`, contentRep[ky]) : ''
      businessDetail.paramContent = businessDetail.paramContent ? businessDetail.paramContent.replaceAll(`{${ky}}`, contentRep[ky]) : ''
      businessDetail.buttons && businessDetail.buttons.forEach((item: any) => {
        item.buttonName = item.buttonName.replaceAll(`{${ky}}`, contentRep[ky])
        item.routePath = item.routePath ? item.routePath.replaceAll(`{${ky}}`, contentRep[ky]) : ''
        // 判断是否存在tagText，存在则替换并且截取前5个字符
        item.tagText = item.tagText ? item.tagText.replaceAll(`{${ky}}`, contentRep[ky]) : ''
      })
    })
    currDialogConfig.businessDetail = businessDetail
  }
  /** 返回结果集 */
  const result = {
    special_design,
    currDialog,
    dialog_identify: tkKey,
    currDialogConfig,
    currDialogTemplate,
    dialogKey: '',
    /** 弹框显示规则 */
    ruleDialog: wx.$.u.getObjVal(currDialogConfig, 'businessDetail.rule', '') as IRuleDialog,
  }
  switch (popType) {
    case 1:
      if (!special_design) {
        // 业务弹窗
        result.dialogKey = 'base'
      }
      break
    case 2:
      //  活动弹窗
      result.dialogKey = 'active'
      break
    default:
      if (msg && typeof msg === 'string') {
        wx.hideLoading()
        await wx.$.msg(msg)
      }
      console.log('匹配值失败')
  }
  return result
}

/**
 * @description 弹窗行为上报
 * @param popCode 弹窗标识
 * @param action 行为
 * @param text 按钮文案 - 用于区分点击了哪个按钮
 * @param pageCode 页面标识
 * @param report 弹窗曝光时上报的额外数据
 */
type IOptions = {
  /** 弹窗标识 */
  popCode: string
  /** 行为 */
  action: number
  /** 按钮文案 - 用于区分点击了哪个按钮 */
  text?: string
  /** 页面标识 */
  pageCode?: string
  report?: any
  isWyzx?: boolean
  extParams?: any
}
export const uploadDialog = (option: IOptions) => {
  const { popCode, action, text, pageCode, isWyzx, extParams } = option || {}
  const report = option.report || {}
  let event = ''
  /** 当前弹窗模版配置 */
  switch (action) {
    case 1:
      event = 'universalBoxExposure'
      break
    case 2:
      event = 'universalBoxClose'
      break
    default:
      event = 'universalBoxClick'
  }
  // 火山埋点需要
  const ext = {
    event,
    ...report,
    ...(text ? { click_button: text } : {}),
    ...(extParams || {}),
  }
  actionReport({ popCode, action, ext, pageCode, isWyzx })
}

/** 存储首页发布找活名片弹窗 v4.0.0 的数据 - 用户必须登录
 * type:
 *  - detail 进入详情页,
 *  - contact 点击了联系老板或者聊一聊按钮
 *  - show 弹出了弹框
 */
export function postResume1DataSave(type: 'detail' | 'contact' | 'show'): typeof post_resume1Def | false {
  const { login } = store.getState().storage.userState
  if (!login) {
    return false
  }
  const key = 'post_resume1'
  const data = wx.$.u.deepClone(storage.getItemSync(key))
  switch (type) {
    case 'detail':
      data.detailNum = data.detailNum ? data.detailNum + 1 : 1
      break
    case 'contact':
      data.contactNum = data.contactNum ? data.contactNum + 1 : 1
      break
    case 'show':
      data.isShow = true
      break
    default:
  }
  // 存储缓存
  storage.setItem(key, data)
  return data
}

type IGetDialogDataReturn = {
  /** 弹框标识 */
  dialogIdentify: string
  /** 弹框内容所要替换的变量 */
  template: { [key: string]: any } | null,
}
/** 返回响应结果中的弹框对象，没有弹框对象返回{ dialogIdentify: '', template: null  }
 * @param res 接口返回结果
 * @returns {dialogIdentify: string, template: any}
 */
export function getDialogData(res): IGetDialogDataReturn {
  let dialogData = wx.$.u.getObjVal(res, 'data.dialogData', '')
  dialogData = dialogData.dialogIdentify ? dialogData : res.data || ''

  if (!dialogData.dialogIdentify) {
    // 无任何弹框数据
    return { dialogIdentify: '', template: null }
  }
  return dialogData
}

/**
 * @description 获取今日不在提醒功能的弹框标识
 * @param ky 自定义标识
 *  */
export function getRdTodayPopkey(ky) {
  const rdTodayPopkys = storage.getItemSync('rdTodayPopDialogkey')
  if (!wx.$.u.isEmptyObject(rdTodayPopkys)) {
    return rdTodayPopkys[ky] || []
  }
  return []
}

/**
 * @description 保存今日不在提醒功能的弹框标识
 * @param ky 自定义标识
 *  */
export function setRdTodayPopkey(ky, dialogky) {
  if (!ky) {
    return
  }
  const rdTodayPopkys = storage.getItemSync('rdTodayPopDialogkey')
  if (!wx.$.u.isEmptyObject(rdTodayPopkys)) {
    const nArr = rdTodayPopkys[ky] || []
    nArr.push(dialogky)
    storage.setItemSync('rdTodayPopDialogkey', { ...rdTodayPopkys, [ky]: nArr })
    return
  }
  storage.setItemSync('rdTodayPopDialogkey', { [ky]: [dialogky] })
}

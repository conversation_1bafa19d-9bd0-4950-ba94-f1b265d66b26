/*
 * @Date: 2022-01-11 13:50:07
 * @Description: 项目中所有 storage 和 model关联的初始值
 */

import { resumeDetailsDef, locationPopCityDef } from './defData'

export default {
  // !公共的缓存 如果是需要缓存的小对象，全放在这里，并且没有时间限制要求的
  common: {
    /** 是否有发布过职位 */
    hasIssue: false,
    /** 首页金刚区渲染列表 */
    indexIconList: null,
    /** 过期时间点，用于绑定手机弹窗内的时间提醒,该值会在用户点击绑定手机号按钮时，由后端返回 */
    expireTime: '',
    /** 用户是否展示过【绑定手机】弹窗 */
    showBindPhonePopup: false,
    /** 实名认证，用户手动输入的身份信息存储 */
    userIdCardForm: {
      username: '',
      id_card: '',
    },
    /** 是否第一次使用订阅招工 1-是 0-否 */
    isFirstSubscription: 1,
    /** 订阅通知时间 */
    subscribeNotice: 0,
    /** 订阅关注时间 */
    subscribeFocus: 0,
    /** 订阅关注/通知弹窗本地存储key */
    showNoticeModal: '',
    /** 订阅招工选择的工种 */
    subWorkSelect: [],
    /** 订阅招工选择的区域 */
    subCitySelect: [],
    /** 用户未绑定手机点击返回 显示到期时间弹窗 */
    showExpirePop: false,
    /** 是否展示过【限定时间内绑定手机】弹窗 */
    showBindPhoneTimeoutPopup: false,
    /** 是否弹出过角色弹窗过-true-false */
    rollPopupFlag: true,
    /** 引导用户获取积分弹窗,下次弹出弹窗的时间 */
    earnPointsExpiredTime: null,
    /** 招工首页-刷新找活名片弹窗弹出数量 */
    recruitRefreshResumeCount: {
      time: 0,
      count: 0,
    },
    /** 招工首页-发布找活名片弹窗弹出数量 */
    recruitPublishResumeCount: null,
    /** 是否喜欢我是工人栏目 */
    resumeLike: 0,
    /** 是否喜欢我是老板栏目 */
    recruitLike: 0,
    // 是否进行重新登录
    reLoginState: false,
    /** 找活列表视频卡片是否滑动-关联model */
    movedResumeCard: {
      /** 滑动次数 */
      num: 0,
      /** 记录滑动当天的结束时间戳（23:59:59.999） */
      time: 0,
    },
    /** 找活大列表气泡 筛选最合适您的优质师傅 */
    resumeFilterBubbleShow: true,
    /** 工人推荐登录标识 */
    workerRecommendLogin: false,
    // 记录是否从招工搜索结果页，已经选择过工种选择器
    isHaveChoosedRecruitResultlist: false,
    /** 招工大列表附近选项气泡展示 */
    showNearbyTips: true,
    /** 发布职位按钮显示 */
    showPublishButton: false,
    // im登录状态  0初始状态   1 登录状态  2 未登录状态
    timLoginState: 0,
    /** 通过简历大列表或搜索结果页首次进入简历详情展示滑动手势 */
    showSlideTip: true,
    /** 宕机页面是否已显示 */
    sysmainshow: false,
    /** 宕机接口是否可以请求 */
    sysmainrequest: true,
    /** 是否从选择身份页进入了简历完善流程 */
    isResumeProcess: false,
    // IM会话详情引导提示语
    guidingTipsNum: 0,
    /** 是否显示过地图页地址管理气泡 */
    isShowedAddManagePop: false,
    /** 找工人列表城市和工种 是否已选过 */
    resumeOption: {
      city: false,
      classify: false,
    },
    // 大转盘 提示弹窗  每日最多一次，最多弹出3次
    luckywheelpop: {
      time: 0,
      num: 0,
    },
    /** 个性化推荐状态设置 */
    perRecommendationSet: {
    // 招工信息智能推荐(筛选器)
      recruitList: true,
      // 找活信息智能推荐(筛选器)
      resumeList: true,
      // 招工找活搜索页面的猜你喜欢和热门
      search: true,
    },
  },
  /** 加群数据缓存 */
  joinGroup: {},
  /** 选择的角色, 1.我想找工人，2我想找工作 bc 分端角色，默认为2 */
  userChooseRole: 2 as 1 | 2,
  // !当日 找活名片-设置置顶-是否是第一次返回和返回时间
  firstSetTopTimeBack: {
    time: 0, // 当前时间
    status: 2, // 1：要弹 2：不弹
  },
  // !招工板块
  /** 快速发布招工内容 */
  fastIssueContent: '',
  // !找活版快
  /** 我的找活名片详情 */
  myResumeDetails: { ...resumeDetailsDef },
  // !置顶城市选择
  /** 置顶城市历史 */
  topCityHistoryInfo: [],
  /** 招工大列表气泡 输入工种名称,快速找到工作 是否显示 */
  recruitSearchBubble: {
    // 是否显示
    show: false,
    // 当日是否显示
    dayShow: false,
    // 是否已经请求过接口 判断
    isreq: false,
  },
  /** 找活列表过滤条件 */
  resumeFilterType: {
    label: '全部',
    value: 0,
  },
  resumeFilterSort: {
    label: '智能推荐',
    value: 'recommend',
  },
  // !全局设置
  /** params: refId 把用户分享的source存入本地 */
  sourceCode: '',
  /** params: track_seed 分享小程序的track_seed  */
  track_seed_share: '',
  /** params: source 分享小程序的source */
  source_share: '',

  // !城市
  /** 城市选择当前值 招工找活大列表搜索条件取这个 */
  userLocationCity: {
    id: '1',
    name: '全国',
    letter: 'quanguo',
    level: 0,
    isFirst: true,
    isGetUserIp: false,
    recruitCityObj: { id: '1', name: '全国', letter: 'quanguo', level: 0, citys: [], cityLen: 0 },
    resumeCityObj: { id: null, name: '选择城市', letter: '', level: 0, citys: [], cityLen: 0 },
  } as {
    id: string
    name: string
    letter?: string
    level?: number
    parents?: any[]
    pid?: string
    isFirst?: boolean
    recruitCityObj?: any
    recruitSearchCityObj?: any
    resumeCityObj?: any
    resumeSearchCityObj?: any
    isGetUserIp?: boolean
  },
  /** 高德定位城市(省、市) */
  userLocation_city: null as {
    cityId?: number
    cityName?: string
    cityCompleteName?: string
    provinceId: number
    provinceName: string
    provinceCompleteName?: string
    district?: string
    address: string
    name: string
  },
  /** 高德api的userLocation */
  userLocation: null,

  /** 选择的历史地区的缓存historyMapLocation */
  historyMapLocation: [],

  // !搜索页历史记录
  /** 主站搜索页-历史搜索数据 recruit:招工，resume:找活 */
  searchHistory: { recruit: [] as Array<string>, resume: [] as Array<string>, resumeobj: [] as Array<Object>, recruitobj: [] as Array<Object> },
  // !工种及类别
  /** 招工工种当前值(用作方便渲染[{id: pid}]格式) */
  recruitClassify: [],
  /** 找活工种当前值(用作方便渲染[{id: pid}]格式) */
  resumeClassify: [],
  /** 找活搜索结果页工种当前值(用作方便渲染[{id: pid}]格式) */
  sResumeClassify: [],
  /** 招工搜索列表排序 */
  recruitSearchSort: { value: 'newest', label: '最新排序' },
  // !定位
  /** 定位授权: 1 - 有权限，2 - 拒绝授权 */
  L_IS_GEO_AUTH: null,

  // !用户
  /** 用户授权 */
  loginAuthData: null as {
    /** 授权用户unionid */
    openid: string,
    /** 授权用户openid */
    unionid: string,
    /** 授权用户session key */
    sessionKey: string,
    /** 自动登录票据 */
    loginTicket: string
  },
  /** 用户状态 */
  userState: {
    userId: 0,
    token: '',
    tokenTime: 0,
    uuid: '',
    login: false,
    role: 0, // 用户角色
    header_image: '',
    userLoginCityId: '',
  },
  // 黑名单用户信息(弹框信息)
  entangledUserInfo: {
    dialogIdentify: '',
    dialogContent: '',
  },
  /** 引导用户发布招工 */
  publishRecruitGuidePop: null,
  // 是否首次进入im会话页面
  /** 实名 */
  realNameData: {
    /** 上一次离开的验证方式 */
    lastTab: 2,
    /** 缓存的上传图片 */
    url: '',
    httpUrl: '',
  },
  /** 用户IM账号 */
  userImAcc: '',
  /** 我的关注提示管理通知tip显示状态 当天23:59:59.999过期 */
  communicateTip: {
    /** 联系记录页的tip显示状态 */
    myContactHistory: true,
    /** 我的关注列表的tip显示状态 */
    communicateList: true,
    /** 我的关注详情的tip显示状态 */
    communicateDetail: true,
  },
  /** 工人推荐浏览记录 */
  workerRecommendHistory: [],
  /** 待评价红点展示数组 */
  toBeEvaluationRedPointList: [],
  /** 评价页面未发布名片弹窗 */
  evaluationNoResumemMadol: false,
  /** 工具页面选择器临时存放数据 */
  changeToolsPage: null,
  /** 隐私协议弹框是否弹出(目前所有小程序都在使用这个 状态, 以前是 => 鱼泡快照 在使用) */
  isShowServicePrivacyV5: true,
  /** 账号登录列表（最多存3个） */
  loginAccountList: [],
  /** 招工大列表附近选项跳转地址页选择的定位信息 */
  recruitLocaltion: null,
  /** 实名挽留弹窗 */
  showRealNameModal: false,
  /** 企业认证营业执照 ticket，用于换取营业执照信息 */
  licenseInfoTicket: null,
  /** 企业认证已同意《鱼泡直聘企业认证协议》 */
  acceptedFirmAuthLicense: false,
  /** 优质工人入口所需数据 */
  qualityWorkerEntryData: { isClickQualityWorkerEntry: false, saveDate: '', workerNumber: 0, provinceId: '', cityId: '', classifyIds: '' },
  /** 优质师傅推荐手势配置 */
  qualityWorkerGestureConfig: {
    /** 展示天数 */
    showDays: 0,
    /** 是否显示左滑手势 */
    gestureLeft: false,
    /** 是否显示右滑手势 */
    gestureRight: false,
    /** 首次展示手势记录日期 */
    firstGestureRecordDate: '',
    /** 最终展示手势失效日期 */
    lastInvalidGestureDate: '',
    /** 存储当日是否展示过左滑手势 */
    gestureLeftDateObj: {},
    /** 是否展示过右滑手势 */
    isViewedGestureRight: false,
  },
  /** 判断进入首页是否跳转发布找活名片页面 */
  shouYeToPublishResume: true,
  /** 首页切换城市弹框标识：locationPopCity的弹出条件缓存配置 */
  locationPopCity: { ...locationPopCityDef },
  /** 展示过的浮标气泡 */
  buoyBubbles: [],
  /** 存储页面资源数据 - 根据页面pageCode存储最近一次获取到的资源数据，当接口报错或无响应时使用 */
  pageResource: {},
  /** 招工置顶 挽留弹窗 今日随机置顶用户数 */
  jobTopRandomUserNumber: 0,
  /** 简历列表职位tab栏缓存数据 */
  resumeTabPosition: [],
  /** 首页工种tab栏选中项 */
  selectClassifyTabId: {
    userId: 0,
    industry: -1,
    occIds: [],
    isRecommend: false,
    positionType: 1,
  },
  /** 招工列表职位tab栏选中项--(活动次级(兼职类)工种) */
  selectSecondaryClassifyTabId: {
    userId: 0,
    industry: -1,
    occIds: [],
    isInit: false,
    positionType: 2,
    occName: '全部',
  },
  /** 简历列表职位tab栏选中项 */
  selectPositionTabId: {
    userId: 0,
    selectItem: {
    },
  },
  /** 其他小程序参数 */
  referrerInfo: {
    appId: '',
    extraData: {},
  },
  /** 发布简历用户选择的数据 */
  pubishData: {
    // 职位
    occs: [],
    // 期望工作地
    hopeAreas: [],
    // 性别
    gender: null,
    // 姓名
    userName: '',
    // 生日
    birthday: '',
    // 求职类型 1.全职 2.兼职
    positionType: 1,
    // 期望薪
    salaryObj: {},
    // 职业偏好
    preferenceList: [],
  },
  /** 聊一聊兜底弹窗 */
  imChatPrePopup: '',
  /** 外置拨打电话弹窗 */
  telPrePopup: '',
  /**
   * 通用弹框的 今日不再提醒 存储
   * key: 需要用到的接口自己定义
   * value: 数组，存储弹框标识
  */
  rdTodayPopDialogkey: {},
  /** 子账号自主申请的 冲突企业ID和名称 */
  conflictEnterprise: {
    conflictUserId: null,
    conflictName: '',
  },
  toPurchaseJobVieData: {},
  /** 发布简历页选择的第一个地址id */
  resumeSaveAreaId: '',
  // 分享弹窗分享卡片数据
  shareCardData: {
    isShareModal: false,
    title: '',
    img: '',
    path: '',
  },
  // 分享出去的trackSeed
  shareTrackSeed: '',
  // 工种所有的数据
  allOccData: {
    /** 招工筛选排序的数据集合 */
    jos_list_types: [],
    /** 找活筛选排序的数据集合 */
    resume_list_types: [],
    /** 工厂的工种数据集 */
    occupations_factory: [],
    /** 工厂找活筛选类型数据集 */
    staff_tree: [],
    // 工厂热门工种
    factoryHotClassify: {},
    // 工种数据
    occupations: [],
    // 工种数据版本号
    occupations_v: null,
    // 兼职工种数据
    partoccupations: [],
    // 兼职工种数据版本号
    partoccupations_v: null,
    // 物流专区数据源
    logistics_factory: [],
  },
  /** 特惠拨打，弹窗弹出的记录，记录的是用户ID */
  resumeDisCountCallPhone: [],
  /** 求职期望自动跳转到完善页的次数 */
  resumeAutoPerfect: 0,
  /** 离线消息的头像 */
  offlineMessageAvatar: {
    // 用户头像
    headImg: '',
    // 企业logo
    enterpriseImg: '',
  },
  /** 首页工种tab栏缓存数据 */
  classifyTabClassify: [],
  /** 首页--活动次级（兼职类）工种tab栏缓存数据 */
  secondaryClassifyTabClassify: [],
  /** 微信号编辑也忙顶部横条是否显示 */
  isWechatEditRailShow: true,
  /** 简历搜索中间页和简历搜索结果页的顶部职位的选中的职位ID */
  searchPageSltedJob: {} as any,
  /** 未发布职位简历列表ab实验 */
  isNewLogic: false,
}

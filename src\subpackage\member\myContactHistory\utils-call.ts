import { callMidPhoneResume } from './utils-popup'
import { storage } from '@/store/index'
import { SOURCE_ID_NAME_DATA } from '@/utils/helper/list/index'

// -------------------------------- 我联系的人
/**
 * @name 我联系的人 拨打我联系的人卡片对应的手机号
 */
export async function callMyContactCardPhone(after_refund = 0) {
  try {
    const { identityType, infoId, subUuid, uuid } = this.data.tempCardItem
    const isBoosCard = identityType.code == 1
    if (isBoosCard) {
      clickBossReport(infoId)
      const params: any = {
        jobId: infoId,
        scene: 5,
        lookType: 1,
        isPrivacy: true,
        hasPopRefundTip: !!after_refund,
      }
      wx.$.l.recruitTelChat(params, {
        query: {
          pageCode: this.data.pageCode,
        },
        midext: {
          nfoId: infoId,
          source: 1,
          pageName: '我联系的人',
        },
      }, {
        failCallPhoneReport: () => {
          callPhoneReport.call(this, '0')
        },
        succesCallPhoneReport: (res) => {
          const isConsume = wx.$.u.getObjVal(res, 'data.priceInfo.isConsume', false)
          callPhoneReport.call(this, isConsume ? '1' : '2', res)
        },
      })
      return
    }
    await wx.$.l.resumeMidTelV3.call(this, { uuid: subUuid, sceneV2: '5' }, { pageName: '我联系的人', from: 2, loading: true }, {
      callPopBack: (pop) => {
        wx.$.resumeMidModel({
          ...pop,
          zIndex: 10011,
          source: '2',
          infoId: uuid,
          pageName: '积分记录',
          call: (callresp) => {
            const { detail } = callresp || {}
            if (detail == 2) {
              const showRealTelState = storage.getItemSync('showRealTelState')
              if (showRealTelState) {
                const currentPage = wx.$.r.getCurrentPage()
                storage.setItemSync('showRealTel', currentPage.route)
              }
            }
            this.onCallMidPhone(callresp)
          },
        })
      },
      failReport: () => {
        callMyContactCardPhoneReport.call(this, '0', 1, { sourceId: '22' })
      },
      successReport: (resReport) => {
        const { data } = resReport || {}
        const { isFirstView } = data || {}
        callMyContactCardPhoneReport.call(this, isFirstView == 1 ? '1' : '2', 1, { sourceId: '22' })
      },
    })
  } catch (error) {
    console.error(error)
  }
}

/**
     * @description 拨打电埋点
     * @param {object} get_status 获取状态
     * @param {object} get_status 拨打电话返回的data数据
     * @param clickType 点击类型 1、2、3(1-页面底部、2-页面中间、3-聊一聊)
     */
export async function callMyContactCardPhoneReport(get_status, clickType = 1, ext:any = {}) {
  const { tempCardItem } = this.data
  const { sourceId } = ext || {}
  const { pagination, buriedPointData, resumeId, subUuid, occupation, guid, location_id,
    pagination_location } = tempCardItem || {}
  let occupations_v2 = ''
  let occupations_v2_name = ''
  let occupations_type = '-99999'
  if (wx.$.u.isArrayVal(occupation)) {
    const subs = await wx.$.l.getClassifyByNames(occupation)
    if (wx.$.u.isArrayVal(subs)) {
      const occIds = subs.map(i => i.id)
      const occNames = subs.map(i => i.name)
      const modes = subs.map(i => `${i.mode}`)
      occupations_v2 = occIds.join(',')
      occupations_v2_name = occNames.join(',')
      if (modes.includes('2')) {
        occupations_type = '招聘'
      } else if (modes.includes('1')) {
        occupations_type = '订单'
      }
    }
  }
  const eventData: any = {
    info_id: `${resumeId}`,
    request_id: `${guid || ''}`,
    location_id: `${location_id || ''}`,
    pagination: `${pagination || ''}`,
    pagination_location: `${pagination_location || ''}`,
    source_id: `${sourceId || '26'}`,
    source: SOURCE_ID_NAME_DATA[`${sourceId || '26'}`] || '小程序分享',
    active_status: '', // 活跃状态
    click_entry: `${clickType || '-99999'}`,
    dialing_interval_duration: '', // 拨打间隔时长(秒) 从进入详情页面到点击拨打电话的时间间隔
    get_status: `${get_status == 0 ? '0' : (get_status || '-99999')}`, // 获取状态 0、1、2（0-获取失败、1-获取成功（首次）、2-获取成功（非首次））
    resume_uuid: subUuid,
    occupations_v2,
    occupations_v2_name,
    occupations_type,
    ...(buriedPointData || {}),
  }
  wx.$.collectEvent.event('workersPhoneCalls', eventData)
}

/**
 * @name 拨打->我联系的人->中间号
 */
export async function callMyContactPeopleMidPhone(callPhoneType) {
  let isBoss = false
  if (this.data.tempCardItem) {
    const { identityType } = this.data.tempCardItem
    isBoss = identityType.code == 1
  } else if (this.data.item) {
    // 兼容评价页面
    const { commentRoleType } = this.data.item
    isBoss = commentRoleType.code != 1
  }

  // 我联系的人 1 为招工， 2为找活
  if (isBoss) {
    return
  }
  callMidPhoneResume.call(this, callPhoneType)
}

function callPhoneReport(get_status, chatRes?) {
  const info = this.data.tempCardItem
  const { location_id = '', pagination = '', pagination_location = '' } = info || {}
  const buriedPointData = info.buriedPointData || {}
  const occupations_v2_name = wx.$.u.isArrayVal(info.occupation) ? info.occupation.join(',') : ''
  /** 实名状态 */
  const statistics = {
    location_id,
    pagination,
    free_information: '-99999',
    consumption_product_score: '',
    detailed_address: info.address || '',
    post_distance: '',
    job_location: '',
    sort_time: '',
    search_result: '',
    real_name_view: '-99999',
    click_entry: '1',
    dialing_interval_duration: '',
    get_status: get_status || '-99999',
    topping: '-99999',
    urgent: '-99999',
    pagination_location,
    source: '联系记录',
    source_id: '11',
    check_degree: '-99999',
    fix_price_id: '',
    keywords_source: '',
    occupations_v2: '',
    occupations_v2_name,
    occupations_type: '',
    is_famous_company: '',
    recommend_reason: '',
    is_part_time: '-99999',
    active_label: '',
    referrer_page: '',
    landing_page: '',
    ...buriedPointData,

    position_source: info.tenant == 'YPHT' ? 2 : 1,
    info_id: String(info.infoId),
  }
  const { data } = chatRes || {}
  if (data) {
    const { priceId, discountPrice, consumeType, isConsume } = data.priceInfo || {}
    statistics.fix_price_id = Number(priceId)
    statistics.consumption_product_score = discountPrice != 'undefined' ? Number(discountPrice) : ''
    statistics.free_information = consumeType == 0 || !isConsume ? '免费' : '付费'
  }
  wx.$.collectEvent.event('clickBoss', statistics)
}

export function clickBossReport(id) {
  const data = {
    info_id: String(id),
    backend_id: '',
    source: '联系记录',
    source_id: '11',
    click_entry: '1',
  }
  wx.$.collectEvent.event('clickBossButton', data)
}

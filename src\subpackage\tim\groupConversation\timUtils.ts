import { dispatch, store, actions } from '@/store/index'
import { guid } from '@/utils/tools/common/index'
import { getRecruitDetails } from './operUtils'

/**
 * 删除消息数组中的某个消息,并且判断是否需要重新生成消息到model中
 * @parrm msgId 消息ID
 */
export const delMsg = (msgId) => {
  const messageList = [...(store.getState().timmsg.messageList || [])]
  if (!wx.$.u.isArrayVal(messageList)) {
    return { noCreate: false, msgId: '' }
  }

  const idx = messageList.findIndex((item) => item.oId == msgId)
  if (idx < 0) {
    return { noCreate: false, msgId: '' }
  }
  const msg = messageList[idx]
  const isLastMsg = idx == messageList.length - 1
  if (isLastMsg) {
    return { noCreate: true, msgId }
  }

  if (msg.type == 'TIMImageElem') {
    const curConImages = [...(store.getState().timmsg.curConImages || [])]
    const { payload } = msg || {}
    const { imageInfoArray } = payload || {}
    if (wx.$.u.isArrayVal(imageInfoArray)) {
      const { url, imageUrl } = imageInfoArray[0] || {}
      const cpUrl = url || imageUrl
      const nCurConImages = curConImages.filter((item) => item !== cpUrl)
      dispatch(actions.timmsgActions.setState({ curConImages: nCurConImages }))
    }
  }

  messageList.splice(idx, 1)
  dispatch(actions.timmsgActions.setState({ messageList }))
  return { noCreate: false, msgId: '' }
}

// 获取会话开始时间文案
export async function getStartTime(msgList) {
  const { isShowTime } = this.data
  if (wx.$.u.isArrayVal(msgList) && !isShowTime) {
    const [msg] = msgList
    const { clientTime, isSelf } = msg || {}
    if (clientTime) {
      const timeStr = await wx.$.l.transDate(clientTime)
      let timStr = ''
      if (isSelf) {
        timStr = `${timeStr} 你向对方发起了沟通`
      } else {
        timStr = `${timeStr} 对方向你发起了沟通`
      }
      this.setData({
        startTime: timStr,
        isShowTime: true,
      })
    }
  }
}

// 新增引导提示
export async function addGuidingTips() {
  const { userChooseRole: role, userImAcc } = store.getState().storage
  const { guidingTipsNum } = store.getState().storage.common || {}
  const { conversation } = store.getState().timmsg
  if (role != 1 || guidingTipsNum > 0 || !(role == 1 && userImAcc == conversation.payUserImId)) {
    return
  }
  const isOK = await getRecruitDetails.call(this)
  if (!isOK) {
    return
  }
  dispatch(actions.storageActions.setCommonItem({ guidingTipsNum: guidingTipsNum + 1 }))
  const tips = {
    id: `tip${guid()}`,
    payload: { content: [
      {
        type: 'text',
        text: '聊天中可更换其他正在招职位与对方沟通哟~ ',
        color: 'rgba(0, 0, 0, 0.45)',
        size: 13,
      },
      {
        type: 'btn',
        text: '去试试>',
        color: 'rgba(0, 146, 255, 1)',
        size: 13,
        ctype: 'gtry',
      },
    ] },
    isSelf: false,
    type: 'TIMTxtTipElem', // TIMTxtTipElem 提示类型
    avatar: '',
    isAvatar: false,
    time: new Date().getTime(),
    pType: 'SystemMsg', // 系统消息
  }
  const { messageList } = store.getState().timmsg
  dispatch(actions.timmsgActions.setState({ messageList: [...messageList, tips] }))
  dispatch(actions.timmsgActions.setState({ scrollLocId: tips.id }))
}

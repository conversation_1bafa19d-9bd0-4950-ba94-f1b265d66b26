<view class="video-page" style="padding-top:{{statusBarHeight}}px">
  <view class="video-wrap" style="height:{{videoHeight}}px;" catch:tap="onClickVideo">
    <view wx:if="{{baseInfo.userStr}}" class="base-info">
      <view class="base-item" wx:if="{{baseInfo.userStr}}">
        <view class="base-item-cont">{{baseInfo.userStr}}</view>
      </view>
      <view class="base-item" wx:if="{{baseInfo.resumeInfo}}">
        <view class="base-item-cont">{{baseInfo.resumeInfo}}</view>
      </view>
      <view class="base-item" wx:if="{{baseInfo.classifyStr}}">
        <view class="base-item-cont">{{baseInfo.classifyStr}}</view>
      </view>
    </view>
    <video
        id="video"
        class="video"
        style="height:{{videoRealHeight}}px;"
        object-fit="{{videoObjectFit}}"
        controls="{{false}}"
        enable-play-gesture="{{false}}"
        show-center-play-btn="{{false}}"
        muted="{{false}}"
        autoplay="{{true}}"
        src="{{videoUrl}}"
        bind:error="onError"
        bind:loadedmetadata="onLoadVideoData"
        bind:timeupdate="onTimeUpdate"
        bind:ended="onEnded">
    </video>
    <view class="header" style="top:{{menuButtonTop - statusBarHeight}}px;">
      <!-- #ifndef swan -->
      <view class="back-btn" bind:tap="onBack">
        <icon-font type="yp-icon_back" size="48rpx" color="#fff" />
      </view>
      <!-- #endif -->
      <view wx:if="{{pageSource === 'resumeList'}}" class="show-detail-btn" catch:tap="onNavigateToDetail">
        <image mode="aspectFill" class="detail-icon" src="https://staticscdn.zgzpsjz.com/miniprogram/images/wjj/yp-mini_video_see_resume.png?x-oss-process=image/format,webp/quality,q_91" />
        <view>查看牛人信息</view>
      </view>
      <view wx:else class="header-title" style="width: calc({{rectLeft}}px - 72rpx - 32rpx)">
        {{title}}
      </view>
    </view>
    <image wx:if="{{!playState}}" class="play-btn"  src="https://staticscdn.zgzpsjz.com/miniprogram/images/wjj/yp-mini_video_act.png?x-oss-process=image/format,webp/quality,q_91" />
  </view>
  <view class="footer" id="video-footer">
    <view class="progress-wrap">
      <slider class="progress-slider" value="{{progress}}" step="{{1}}" bind:change="onChangeProgress" bind:changing="onChangeProgress">
      </slider>
      <view class="progress-bar">
        <view class="progress-item" style="width:{{progress}}%" />
      </view>
    </view>
    <view class="action-wrap">
      <view class="video-timeline">{{videoTime}}s</view>
      <!-- https://axure.vrtbbs.com/app/project/i6wy41/preview/lp9a65 下掉 联系工人按钮 -->
      <!-- <view wx:if="{{showPhoneBtn && !detail.isFindWork && !isSelf}}" class="btn-item" bind:tap="onPhoneClick">
        <view class="btn-item-cont">
          <text wx:if="{{detail.viewed}}" class="btn-text">继续沟通</text>
          <block wx:if="{{!detail.viewed}}">
            <text class="btn-text">{{detail.isFree ? '免费拨打' : '拨打电话'}}</text>

            <view class="btn-call-count" wx:if="{{detail.freeCount > 1}}">
              <text class="btn-call-count-text">{{detail.freeCount}}次</text>
            </view>
          </block>
        </view>
      </view> -->
      <view class="footer-left" catch:tap="onClickVideo">
        <icon-font type="{{playState?'yp-icon_video_stop':'yp-icon_video_action'}}" size="48rpx" color="#fff"></icon-font>
      </view>
    </view>
  </view>
</view>

import { REQUEST_JAVA_URL } from '@/config/app'
import { formatter } from '@/utils/tools/index'
import { actions, dispatch, storage } from '@/store/index'
import { applyUploadParams, uploadHeaders } from './utils'
import { IResourceHandingParams, ResourceProcessParams, UploadFileParams } from './type'
import { toLogin } from '@/utils/helper/common/toLogin'

/** 处理多个路径末尾拼接时多个斜杠的问题, 如果第一个数组是有一个斜杠需要拼接上, 如果最后一个有斜杠则去掉 */
function joinPath(...paths: (string | number)[]) {
  const path = paths
    .map((path, index) => {
      const newPath = path != null ? `${path}` : ''
      if (index === 0) {
        return newPath.replace(/\/$/, '')
      }
      return newPath.replace(/(^\/|\/$)/g, '')
    })
    .filter((path) => path)
    .join('/')
  if (!path.startsWith('/') && `${paths[0]}`.startsWith('/')) {
    return `/${path}`
  }
  return path
}

/** 资源处理并上传-http://yapi.3pvr.com/project/30/interface/api/38571
 * @param hideMsg 是否隐藏提示
 */
export async function resourceProcess(params: IResourceHandingParams, header = {}, hideMsg = false): Promise<any> {
  const url = joinPath(REQUEST_JAVA_URL, 'media/v2/resource/process')
  const uid = storage.getItemSync('userState')?.userId
  const { videoTempFilePath, watermarkTempFilePath, entryIdVideo, ...processParams } = params
  const newParams: ResourceProcessParams = {
    /** processType处理类型-默认1.图片 */
    processType: 1,
    ...applyUploadParams,
    ...processParams,
    uid,
    extraInfo: { watermark: true, screenshot: !!(videoTempFilePath && watermarkTempFilePath) },
  }
  if (Number(entryIdVideo)) {
    newParams.extraInfo.entryId = entryIdVideo
  }
  const headers = await uploadHeaders(newParams, { ...header })
  return new Promise((resolve) => {
    wx.request({
      url,
      header: headers,
      data: newParams,
      method: 'POST',
      timeout: +ENV_REQUEST_TIMEOUT,
      success: async ({ data }: any) => {
        if (data.error) {
          wx.$.msg(data.message)
          if (data.code == 401 || data.code == 601) {
            wx.$.l.timLogout()
            wx.hideLoading()
            dispatch(actions.storageActions.removeItem('userState'))
            dispatch(actions.storageActions.removeItem('myResumeDetails'))
            toLogin(true)
          }
          resolve(data)
        } else {
          try {
            if (videoTempFilePath && watermarkTempFilePath) {
              // 分别上传视频、水印到服务器
              await Promise.all([
                applyUploadFile({
                  url: data.data.extraInfo.uploadUrlInfo.uploadUrl,
                  filePath: params.videoTempFilePath,
                  formData: data.data.extraInfo.uploadUrlInfo.formData,
                }),
                applyUploadFile({
                  url: data.data.extraInfo.watermarkUploadUrlInfo.uploadUrl,
                  filePath: params.watermarkTempFilePath,
                  formData: data.data.extraInfo.watermarkUploadUrlInfo.formData,
                }),
              ])
            }
            resolve(data)
          } catch (err) {
            !hideMsg && wx.$.msg('上传失败，请重新上传')
            resolve({ err, code: 500, error: true })
          }
        }
      },
      fail: async (err) => {
        !hideMsg && wx.$.msg('上传失败，请重新上传')
        resolve({ err, code: 500, error: true })
      },
    })
  })
}

/** 上传文件
 * url: 上传的url地址
 * filePath: 临时文件路径
 * formData: 提交的formData {}
 */
export function applyUploadFile(params: UploadFileParams): Promise<any> {
  const header = params.header || {}
  if (!header['Content-Type'] && !header['content-type']) {
    header['Content-Type'] = 'multipart/form-data'
  }
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: params.url, // 开发者服务器的URL。
      filePath: params.filePath,
      name: 'file', // 必须填file。
      header: params.header,
      formData: params.formData,
      fail: (err) => {
        reject({ code: 501, msg: '上传失败', error: true, err })
      },
      success: (res) => {
        // 如果是20x的状态码，说明上传成功
        if (res.statusCode >= 200 && res.statusCode < 300) {
          let data = res.data || { error: false }
          if (typeof data === 'string') {
            data = formatter.toJSON(res.data, { error: false })
          }
          if (data && !data.error) {
            resolve({ code: 200, msg: '上传成功', data })
          } else {
            reject({ code: 500, msg: '上传失败', error: true, err: data })
          }
        } else {
          reject({ code: 500, msg: '上传失败', error: true, err: res })
        }
      },
    })
  })
}

/** 获取uri路径
 * @param uri: 完整的uri地址 http://www.baidu.com/r/cadsa
 * @returns 返回路径 /r/cadsa
 */
export function getUriPath(uri: string) {
  return uri.replace(/^(https?:)?\/\/[^/]+/, '')
}

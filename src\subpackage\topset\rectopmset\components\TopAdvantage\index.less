.topRow {
  .flexR();
  justify-content: space-between;
  margin-top: 24rpx;
}

.topItem {
  flex: 1;
  text-align: center;
}

.topImg {
  width: 112rpx;
  height: 112rpx;
}

.topTip {
  margin-top: 8rpx;
  color: @text85;
  font-size: 26rpx;
}

.topNumBtn {
  .flexRCC();
  display: inline-flex;
  margin-top: 8rpx;
  width: fit-content;
  height: 36rpx;
  // line-height: 36rpx;

  padding-left: 16rpx;
  padding-right: 16rpx;
  background-color: #ff8904;
  border-radius: 36rpx;
  color: #fff;
  font-size: 22rpx;
}

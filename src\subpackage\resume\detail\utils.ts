/*
 * @Date: 2022-01-08 10:08:02
 * @Description: 处理找活名片的工具函数
 */

import { helper } from '@/utils/index'
import { store, storage, dispatch, actions } from '@/store/index'
import { SHARE_BTN_LOCATION, SHARE_CHANNEL } from '@/config/share'
import { getSharePathInfo, completeShare, getShareInfo, getShareTimeLineInfo } from '@/utils/helper/share/index'

const formatWorkStatusTxt = (status: number) => {
  switch (status) {
    case 1:
      return '离职-随时到岗'
    case 2:
      return '在职-暂不考虑'
    case 3:
      return '预约下一个活'
    case 4:
      return '在职-月内到岗'
    case 5:
      return '在职-考虑机会'
    default:
      return ''
  }
}

const formatEducationDegree = (eduBackground: string | number, eduType?: number, isShow = true) => {
  switch (String(eduBackground)) {
    case '1':
      return '初中以下'
    case '2':
      return '中专/技校'
    case '3':
      return '高中'
    case '4':
      return '专科'
    case '5':
      if (isShow) {
        return eduType == 2 ? '非全日制 · 本科' : '全日制 · 本科'
      }
      return '本科'

    case '6':
      if (isShow) {
        return eduType == 2 ? '非全日制 · 硕士' : '全日制 · 硕士'
      }
      return '硕士'

    case '7':
      if (isShow) {
        return eduType == 2 ? '非全日制 · 博士' : '全日制 · 博士'
      }
      return '博士'

    case '8':
      return 'MBA/EMBA'
    default:
      return ''
  }
}

const transformSubs = (data) => {
  if (data && data.length) {
    return data.map(each => ({
      ...each,
      positionTypeTxt: each.positionType != 1 ? '兼职' : '',
      tags: each.occCtrls ? each.occCtrls.flatMap(item => item.controlValueList).filter(txt => !!txt) : [],
    }))
  }
  return []
}

const transformWorkExpResp = (workExpResp) => {
  if (workExpResp && workExpResp.length) {
    return workExpResp.map((each) => ({
      ...each,
      /** !each.startTime && !each.endTime ? '' :  */
      timeStr: `${each.startTime || '1990年以前'}-${each.endTime || '至今'}`,
      occName: (each.occupationInfo && each.occupationInfo.occName) || '',
    }))
  }
  return []
}

const getOccupationIdList = (data) => {
  if (data && data.length) {
    return data.map((item) => {
      return (item.occupationInfo && item.occupationInfo.occId) ? String(item.occupationInfo.occId) : ''
    })
  }

  return []
}

/** 项目经验的时间处理 */
const handleExpTime = (data) => {
  const { startTime, endTime, version } = data
  if (version < 5) {
    if (!startTime && !endTime) {
      return ''
    }
    if (!startTime) {
      return `${endTime || '至今'}`
    }
    if (!endTime) {
      return `${startTime || '1990'}`
    }
  }

  return `${startTime || '1990年以前'} - ${endTime || '至今'}`
}

/** 数据清洗 */
export const formatResumeDetailData = (odata/* , resume_uuid */, query: any = {}) => {
  const data = wx.$.u.deepClone(odata || {})
  const { userInfoResp, basicResp, browseResp, complainResp, projectExpResp, workExpResp, videoResp, eduExpResp, resumeCheckoutResp } = data
  const workExpYear = userInfoResp.workExpYear ? `${userInfoResp.workExpYear}年经验` : ''
  const age = userInfoResp.age ? `${userInfoResp.age}岁` : ''
  const sex = userInfoResp.sex == 2 ? '女' : '男'
  const userDescArr = [query.sex || sex, age, userInfoResp.nation, formatEducationDegree(userInfoResp.eduBackground, null, false), workExpYear]
  return {
    ...data,
    resumeId: basicResp.resumeId,
    name: query.name || userInfoResp.userName || '先生',
    avatar: query.avatar || userInfoResp.headPortrait,
    occupationIdList: getOccupationIdList(basicResp.subs),
    resumeSubUuid: query.resumeSubUuid || query.uuid || basicResp.subs[0].resumeSubUuid,
    // 是否已查看手机号（接口返回或来自于whoContacted页面）
    viewed: browseResp.viewed,
    // 是否实名
    realNameStatus: userInfoResp.realNameStatus == 2,
    // 用户的基本描述（年龄、性别等）
    userDesc: userDescArr.filter((txt) => txt).join(' · '),
    // 是否已关注（已废弃）
    // concerned: browseResp.concerned,
    // 是否已收藏牛人
    collected: browseResp.collected,
    // 当前职位的用户id
    userId: userInfoResp.userId,
    // 找工作状态文案
    workStatusTxt: formatWorkStatusTxt(basicResp.workStatus),
    // 是否投诉
    isComplained: complainResp.complained,
    // 是否可以投诉
    complainAvailable: complainResp.complainAvailable,
    // 是否展示投诉/已投诉按钮
    isShowComplainedBtn: complainResp.complained ? browseResp.viewed : browseResp.viewed && complainResp.complainAvailable,
    // 是否活跃
    online: userInfoResp.activeStatus,
    // 活跃文案
    activeStatusText: userInfoResp.activeStatusText,
    // 被查看次数
    browseMeResumeCount: browseResp.browseMeResumeCount || 0,
    // 看过的工作数
    myBrowseJobCount: browseResp.myBrowseJobCount || 0,
    // 求职期望
    subs: transformSubs(basicResp.subs),
    // 工作城市
    hopeAreaTxt: basicResp.hopeAreaCity && basicResp.hopeAreaCity.length ? basicResp.hopeAreaCity.map(item => item.name).join('、') : '',
    // 个人优势
    introduce: basicResp.introduce || '',
    // 工作经历
    workExpResp: transformWorkExpResp(workExpResp),
    // 项目经历
    projectExpResp: projectExpResp && projectExpResp.length ? projectExpResp.map(each => ({
      ...each,
      wholeTimeStr: handleExpTime(each),
      attaches: each.attaches && each.attaches.length ? each.attaches.map((item) => ({
        ...item,
        timeStr: !item.startTime && !item.endTime ? '' : `${each.startTime || '1990'}-${each.endTime || '至今'}`,
      })) : [],
    })) : [],
    // 教育经历
    eduExpResp: eduExpResp && eduExpResp.length ? eduExpResp.map(each => ({
      ...each,
      eduBackgroundTxt: formatEducationDegree(each.eduBackground, each.eduType, true),
      timeStr: !each.startTime && !each.endTime ? '' : `${each.startTime || '1990年以前'}-${each.endTime || '至今'}`,
    })) : [],
    // 面试视频
    videoResp: videoResp ? { ...videoResp, duration: wx.$.u.transformationTime(videoResp.attach.duration) } : undefined,
    // 是否免费拨打
    isFree: resumeCheckoutResp && resumeCheckoutResp.free,
    // 免费拨打次数
    freeCount: (resumeCheckoutResp && resumeCheckoutResp.freeCount) || 0,
    // 期望工作地
    hopeArea: basicResp.hopeAreaCity && basicResp.hopeAreaCity.length ? basicResp.hopeAreaCity[0].name : '',
  }
}

/** 获取找活详情数据 */
export const getResumeDetail = (params) => {
  return new Promise<any>((resolve) => {
    wx.$.javafetch['POST/resume/v3/detail/app/otherDetail'](params)
      .then((res) => {
        resolve(res)
      })
      .catch((res) => {
        resolve(res)
      })
  })
}

/**
 * 操作我的收藏页缓存是否取消当前数据(找活)
 * @param isCollected true 收藏成功 false 取消收藏
*/
export const handleMyCollectItem = (id, isCollected: boolean) => {
  const pages = getCurrentPages()
  const prevPage = pages[pages.length - 2]
  // 如果上一页是我的收藏页
  if (prevPage && prevPage.handleCollectItem) {
    isCollected
      ? prevPage.handleCollectItem(0, false)
      : prevPage.handleCollectItem(id, true)
  }
}

/** 滚动到技能证书位置 */
export async function handleScrollTop() {
  return new Promise((resolve) => {
    const query = wx.createSelectorQuery()
    query.select('#custom-header').boundingClientRect()
    query.select('#certificate').boundingClientRect()
    query.selectViewport().scrollOffset()
    query.exec((rects) => {
      const [customHeader, certificate, scrollOffset] = rects
      if (certificate) {
        resolve(certificate.top - certificate.height / 2 - scrollOffset.scrollTop - customHeader.height)
      }
    })
  })
}

/** 设置当前页面的分享内容 */
export const shareAppMessageContent = (options) => {
  const { userState } = store.getState().storage
  const { resumeDetailsOther } = store.getState().resume
  const { basicResp, specialArea, userInfoResp } = (resumeDetailsOther || {}) as any
  const { userId } = userInfoResp || {}
  /** @name 该名片是否是当前用户 */
  const isCurrentUser_sRmd = userState.login && userId == userState.userId
  /** @name 该用户是否存在 */
  const isUserExisted_sRmd = userId != '0'
  /** 登录了，存在找活 */
  const IS_SHOW_MOD_SELF_BTN = userState.login && isCurrentUser_sRmd && isUserExisted_sRmd
  let btnId = SHARE_BTN_LOCATION.RES_DETAIL_BTM_FRIEND
  const sharePage = 'packageOther/pages/boss-look-card/lookcard'

  if (IS_SHOW_MOD_SELF_BTN) {
    btnId = SHARE_BTN_LOCATION.RES_DETAILS_OWN_FRIEND
  }

  const resumeSubUuid = basicResp?.subs?.[0]?.resumeSubUuid

  const payload = { btnId, specialArea, uuid: resumeSubUuid }
  // 获取分享信息
  const shareInfo = getSharePathInfo(payload, userState.userId, '', sharePage)
  // 对接后台请求
  completeShare(SHARE_CHANNEL.SHARE_WECHAT_FRIEND, shareInfo, 1, { type: 'resume', detail_id: resumeSubUuid })
  // 返回找活详情的分享信息
  return getShareInfo({ path: shareInfo.path, title: options?.title, imageUrl: options.canvasImg, resetImg: false, isCurPageImg: true, from: options.from })
}

/** 分享到朋友圈 */
export const shareTimelineMe = () => {
  const { userState } = store.getState().storage
  const { resumeDetailsOther } = store.getState().resume || {}
  const { basicResp, specialArea } = (resumeDetailsOther || {}) as any
  const resumeSubUuid = basicResp?.subs?.[0]?.resumeSubUuid

  // 找活胶囊分享到朋友圈
  const payload = { btnId: SHARE_BTN_LOCATION.WX_CG_CAP_MENU_PATH, special_type: specialArea, uuid: resumeSubUuid }
  const shareInfo = getSharePathInfo(payload, userState.userId, '', SHARE_BTN_LOCATION.WXCGCAPMENU_PAGE)
  // 对接后台请求
  completeShare(SHARE_CHANNEL.SHARE_WECHAT_MOMENTS, shareInfo, 1, { type: 'resume', detail_id: resumeSubUuid })
  return getShareTimeLineInfo({
    query: `uuid=${resumeSubUuid}&track_seed=${shareInfo.track_seed}&refid=${userState.userId}&source=${shareInfo.source}`,
  })
}

/**
 * @description 设置列表进入详情次数（列表弹窗使用）
 */
export const setListPopData = (id) => {
  const isLogin = store.getState().storage.userState.login
  if (!isLogin) {
    return
  }
  const factoryDetailCount = storage.getItemSync('factoryDetailCount')
  let resumeDetailIds = factoryDetailCount?.resumeDetailIds || []
  resumeDetailIds = resumeDetailIds?.includes(String(id)) ? resumeDetailIds : [...resumeDetailIds, String(id)]
  isLogin && storage.setItemSync('factoryDetailCount', { ...factoryDetailCount, resumeDetailIds })
}

/** 设置微信找活详情SEO */
export const setWechatTDK = (detail) => {
  if (!ENV_IS_WEAPP || !detail) {
    return
  }
  const title = `${detail.hopeArea || ''}${detail.name || ''}找工作，${detail.introduce.slice(0, 8) || ''}`
  wx.setNavigationBarTitle({ title })
}

/** 滑动进入时获取列表数据 */
export const getResumeListDom = async (options) => {
  const pages = getCurrentPages()
  const prePage = pages[pages.length - 2] || ''
  if (!prePage) {
    return
  }
  let comp = {} as any
  if (prePage.route.indexOf('subpackage/resume/listSearchResultsPage/index') > -1) {
    comp = prePage
  } else {
    comp = await wx.$.selectComponent.call(prePage, '#card-list') || {}
  }
  const { list = [] } = comp.data
  // 如果当前数据是最后一条数据，加载下一页
  if (list[list.length - 1].resumeSubUuid === options.uuid || list[list.length - 1].uuid === options.uuid) {
    if (comp.route && comp.route.indexOf('subpackage/resume/listSearchResultsPage/index') > -1) {
      comp.onService({ detail: { isFresh: false, showloading: false } })
      return
    }
    comp.onRefresh(comp.page + 1)
  }
}

/**
 * @description 获取详情页路由参数
 * @param {string} id - 当前详情页id
 * @param {string} type - 1：翻页、2:过滤异常数据
 * @param {object} ext - 额外参数
*/
export async function getResumeDetailQuery(id, guid, ext, type) {
  const pages = getCurrentPages()
  const prePage = pages[pages.length - 2] || ''
  if (!prePage) {
    return {}
  }
  let comp = {} as any
  if (prePage.route.indexOf('subpackage/resume/listSearchResultsPage/index') > -1) {
    comp = prePage
  } else {
    comp = await wx.$.selectComponent.call(prePage, '#card-list') || {}
  }
  const { list = [] } = comp.data
  const index = list.findIndex(resume => (resume.resumeSubUuid === id || resume.uuid === id) && resume.guid == guid) || 0
  let num = ext.dircetion === 'moveLeft' ? 1 : -1
  // 上一页------>第一页
  if (num == -1 && index === 0) {
    wx.$.msg('已经是第一页')
    if (type == 1) {
      return ''
    }
    // 如果是过滤数据，num取当前id的下一条数据
    type == 2 && (num = 1)
  }
  // 下一页----->最后一页
  if (num == 1 && index === list.length - 1) {
    wx.$.msg('已经是最后一页')
    if (type == 1) {
      return ''
    }
    // 如果是过滤数据，num取当前id的上一条数据
    type == 2 && (num = -1)
  }
  const item = list[index + num]
  // 删除异常数据
  const showRecommendTip = ext.dircetion === 'moveLeft' && comp.data && comp.data.showTjDataTips && comp.data.showTjDataTips.oguid == guid
  type == 2 && comp.onDeleteItem(index)
  if (type == 2 && ((index + num) < 0 || (index + num) >= list.length)) {
    return ''
  }
  dispatch(actions.resumeActions.setInfo({ uuid: item.uuid, id: item.id }))
  /** 详情页埋点使用字段 */
  const buryingPoint = {
    id: item.id,
    info: {
      request_id: item.guid || '-99999',
      location_id: `${item.location_id || ''}`,
      pagination: item.pagination || '',
      pagination_location: item.pagination_location || '',
      source_id: '24',
      search_result: item.search_result || '',
      ...(item.buriedPointData || {}),
    },
  }
  return {
    uuid: item.uuid,
    id: item.id,
    guid: item.guid,
    // avatar: item.headPortrait,
    // name: item.name,
    // sex: item.sex,
    sourceId: '24',
    buryingPoint: JSON.stringify(buryingPoint),
    ...(showRecommendTip ? { showRecommendTip } : {}),
    ...ext,
  }
}

/** 预加载前后一条数据 */
export async function preload(query: any = {}) {
  const { sourceId, uuid, selectedTab, jobId } = query
  const newJobId = selectedTab?.jobId || jobId || ''
  if (!['1', '2', '24'].includes(sourceId)) {
    return
  }
  const pages = getCurrentPages()
  const prePage = pages[pages.length - 2] || ''
  if (!prePage) {
    return
  }
  let comp = {} as any
  if (prePage.route.indexOf('subpackage/resume/listSearchResultsPage/index') > -1) {
    comp = prePage
  } else {
    comp = await wx.$.selectComponent.call(prePage, '#card-list') || {}
  }
  const { list = [] } = comp.data
  const index = list.findIndex(resume => resume.resumeSubUuid === uuid || resume.uuid === uuid) || 0
  if (index === -1) {
    return
  }
  const requestData = store.getState().resume.requestData || {}
  // 上一条数据
  const prevItem = index > 0 ? list[index - 1] : null
  // 下一条数据
  const nextItem = index < list.length - 1 ? list[index + 1] : null
  const extParams = newJobId ? { jobId: newJobId } : {}
  try {
    await Promise.all([
      prevItem && !requestData[prevItem.uuid] && dispatch(actions.resumeActions.fetchGetDetailOther({ uuid: prevItem.uuid, isSlide: true, ...extParams })),
      nextItem && !requestData[nextItem.uuid] && dispatch(actions.resumeActions.fetchGetDetailOther({ uuid: nextItem.uuid, isSlide: true, ...extParams })),
    ])
  } catch (error) {
    console.error('Error:', error)
  }

  const updatedData = store.getState().resume.requestData
  // 处理requestData数据，只保留当前页和前后页的数据
  const newData = {}
  if (prevItem && updatedData[prevItem.uuid]) {
    newData[prevItem.uuid] = updatedData[prevItem.uuid]
  }
  if (uuid && updatedData[uuid]) {
    newData[uuid] = updatedData[uuid]
  }
  if (nextItem && updatedData[nextItem.uuid]) {
    newData[nextItem.uuid] = updatedData[nextItem.uuid]
  }
  dispatch(actions.resumeActions.setRequestData(newData))
}

/** 获取找活详情的标签ab埋点数据
 * @param {object} detail - 详情页数据
 * @param {string} source_id - 24：找活详情页
 */
export async function getActivePoint(detail) {
  const isActiveAb = await wx.$.u.isAbUi('resumeTags', 'newLogic')
  if (!isActiveAb || !detail || !detail.userInfoResp) {
    return {
      isActiveAb,
      pointData: {},
    }
  }

  /** 埋点上报 */
  const userInfo = detail.userInfoResp || {}
  const pointData: any = {}
  const active_status = []
  if (userInfo.isNew) {
    // 新牛人
    active_status.push('100')
  }
  if (wx.$.u.isArrayVal(userInfo.activeLabels)) {
    active_status.push(userInfo.activeLabels[0])
  }
  if (wx.$.u.isArrayVal(active_status)) {
    pointData.active_status = active_status.join(',')
  }
  return {
    isActiveAb,
    pointData,
  }
}

const H5SourceMap = { SetTopEffectList: { name: '置顶效果页查看牛人列表', sceneV2: 15 },
  EffectContactList: { name: '置顶效果页沟通牛人列表', sceneV2: 15 },
  UrgentEffectList: { name: '加急招效果页查看牛人列表', sceneV2: 16 },
  UrgentContactList: { name: '加急招效果页沟通牛人列表', sceneV2: 16 },
  Bombs: { name: '群发炸弹', sceneV2: 0 },
  YUPAO_B_VISIT_CARD: { name: '电话直拨卡效果页', sceneV2: 12 },
  YUPAO_B_CHAT_CARD: { name: '在线畅聊卡效果页', sceneV2: 13 },
  SEARCH_CHAT_CARD: { name: '搜索畅聊卡效果页', sceneV2: 14 } }

export function getQueryScene(query: any = {}) {
  const queryScene = query.sceneV2
  const querySource = query.source
  /** 来源h5的优先获取H5SourceMap映射表上的值 */
  if (querySource) {
    const sceneInfo = H5SourceMap[querySource]
    if (sceneInfo) {
      return sceneInfo.sceneV2
    }
    /** 未获取到，取 query.scene */
    return queryScene || 0
  }
  return queryScene || 0
}

// 场景值到来源数字的映射
export function mapSceneV2ToScene(sceneV2: number, selectedTab?: any): number {
  // 来源大列表
  if (sceneV2 == 1 || sceneV2 == 2) {
    if (selectedTab?.jobDraftId) {
      return 2
    }
    if (selectedTab?.checkStatus == 2) {
      return 1
    }
    if (selectedTab?.checkStatus == 1) {
      return 3
    }
    if (selectedTab?.checkStatus === 0) {
      return 4 // 默认是从职位详情页进入
    }
    return 30
  }

  const mapping: Record<number, number> = {
    5: 8, // 我沟通过谁列表
    6: 8, // 我沟通过谁列表
    7: 7, // 谁沟通过我列表
    8: 7, // 谁沟通过我列表
    9: 5, // 谁看过我列表
    10: 6, //  我看过谁列表
    // 11: 9, // 搜索
    12: 12, // 道具效果页-简历道具-电话直拨卡
    13: 14, // 道具效果页-简历道具-畅聊卡
    14: 13, // 道具效果页-简历道具-搜索畅聊卡
    15: 20, // 道具效果页-职位道具
    16: 20, // 道具效果页-职位道具
    17: 30, // 其他来源（push、im、无来源)
    18: 30, // 其他来源（push、im、无来源)
    19: 30, // 其他来源（push、im、无来源)
    20: 30, // 其他来源（push、im、无来源)
    0: 30, // 其他来源（push、im、无来源)
  }
  return mapping[sceneV2] ?? 0
}

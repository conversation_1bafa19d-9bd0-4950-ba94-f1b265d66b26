/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 普通招工详情，底部tabbar
 */
import { dispatch, actions, store } from '@/store/index'

let timer
Component({
  properties: {
    /** 招工详情，接口内容，及自定义状态 */
    msgInfo: {
      type: Object,
      value: {},
    },
    isShowTk: {
      type: Boolean,
      value: false,
    },
  },
  data: {},
  lifetimes: {
    ready() {},
  },
  methods: {
    onTkClick() {
      this.setData({ isShowTk: false })
    },
    onClick() {
      const { msgInfo } = this.data
      const { payload } = msgInfo || {}
      const { imageInfoArray } = payload || []
      const { curConImages } = store.getState().timmsg
      let current = ''
      if (wx.$.u.isArrayVal(imageInfoArray)) {
        current = imageInfoArray[0].url || imageInfoArray[0].imageUrl
      }
      wx.previewImage({
        urls: wx.$.u.isArrayVal(curConImages) ? curConImages : [current],
        showmenu: true,
        current,
      })
    },
    onLongpressMsg(e) {
      const { msgInfo } = this.data
      if (msgInfo.status && msgInfo.status != 'success') {
        return
      }
      const { id } = e.currentTarget
      dispatch(actions.timmsgActions.setState({ longpressid: id }))
      if (timer) {
        clearTimeout(timer)
      }
      const query = this.createSelectorQuery()
      // 选择id
      query.select(`#${id}`).boundingClientRect()
      query.exec((res) => {
        this.setData({ isShowTk: true, tkWith: res[0].width })
        timer = setTimeout(() => {
          this.setData({ isShowTk: false })
        }, 3000)
      })
    },
    bindload() {
      this.triggerEvent('bindload')
    },
    onStatusClcik(e) {
      const { msgInfo } = this.data
      this.triggerEvent('statusclcik', { msgInfo, type: e.detail.type })
    },
  },
})

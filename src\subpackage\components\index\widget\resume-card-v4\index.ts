/*
 * @Date: 2022-01-08 16:43:49
 * @Description: 找活卡片
 */

import { store, dispatch, actions, MapStateToData, connect, messageQueue, storage } from '@/store/index'
import dayjs from '@/lib/dayjs/index'
import { VIP_ICON_C } from '@/config/app'
import { getFilterData } from '@/utils/helper/resume/index'
import { helper } from '@/utils/index'
import { dealDialogApi } from '@/utils/helper/dialog/index'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { toLogin } from '@/utils/helper/common/toLogin'
import { SOURCE_ID_NAME_DATA } from '@/utils/helper/list/index'

// 组件内部包含model数据，页面可以减少setData
const mapStateToData: MapStateToData = (state) => {
  const { storage } = state
  return {
    movedResumeCard: storage.common.movedResumeCard,
    dutyConfig: state.classify.dutyConfig,
  }
}

Component(
  connect(mapStateToData)({
    options: {
      addGlobalClass: true,
      multipleSlots: true, // 在组件定义时的选项中启用多slot支持
      virtualHost: true,
    },
    externalClasses: ['custom-class'],
    properties: {
      /** 是否显示活跃标签AB实验 */
      isActiveAb: { type: Boolean, value: false },
      // default: 代表主站的卡片, factory: 代表工厂的卡片, collect: 个人收藏的卡片, logistics: 物流的卡片,
      type: { type: String, value: 'default' },
      /** 关联工厂 */
      isRelatedFactory: { type: Boolean, value: false },
      // 点击卡片时跳转的方式，默认值为：push, 可选值为： push | replace | reLaunch
      routerType: { type: String, value: 'push' },
      item: { type: Object },
      /** 去掉卡片底部的边距 */
      no_m_b: { type: Boolean, value: false },
      /** 专区类型 1 工程 2 工厂 3 物流 */
      specialArea: { type: String, value: '1' },
      /** 附近推荐工人 source 标识 */
      nearbyWorkerListApiSource: { type: String, value: '' },
      /** 附近推荐工人 调整到详情所需标识 */
      sourceId: { type: String, value: '' },
      /** 列表筛选的城市 */
      cities: { type: null, value: [] },
      /** 列表筛选的工种 */
      occupations: { type: null, value: [] },
      /** 是否展示聊一聊、联系按钮 */
      showContentBtn: { type: Boolean, default: false },
      /** 关联职位ID */
      relatedInfoId: { type: String, value: '' },
      /**
       * 当前卡片列表使用在那个页面上，
       * searchResult 搜索结果页
       * resumeIndex 简历列表首页
       * */
      origin: { type: String, value: '' },
      selectPositionTabId: { type: String, value: '' },
      /** 详情需要的职位信息 */
      selectedTab: { type: Object, value: {} },
      /** 是否模糊显示 */
      isVague: { type: Boolean, default: false },
      /** 场景值 */
      sceneV2: { type: String, value: '' },
      /** jobId职位id */
      jobId: { type: String, value: '' },
      /** 是否需要显示- 合作意向标签 */
      isShowCooperationTag: { type: Boolean, value: false },
      /** 是否需要显示- 已投诉标签 */
      isShowHasComplainTag: { type: Boolean, value: false },
      /** 信息来源 */
      infoSource: { type: Number, value: 0 },
    },
    data: {
      VIP_ICON_C,
      /** 是否显示手势引导 */
      currTimeset: Date.now(),
      /** 面试视频以及项目经验视频图片合集 */
      projectList: [],
      /** 拨打电话时间间隔-埋点使用 */
      startTime: null,
      /**  中间号弹窗类型, 1=可选直接拨号，2=必须使用安全号 0=安全号不弹窗 */
      popType: 1,
      /** 中间号弹窗文案 */
      popContent: [],
      newContent: [],
      /** 中间号弹窗额外数据 */
      popContentData: {
        /** 是否消耗积分查看该信息：0-未消耗，1-已消耗。备注：只用于中间号交流弹窗。 */
        is_expense_integral: 0,
        /* 标识本次查看电话动作，是否真实消耗了积分。0-未消耗，1-已消耗。 */
        has_expense_integral: 0,
      },
      //! 中间号--end
      // 工种展示行-文案
      occupationStr: '',
      // 薪资展示行-文案
      salaryText: '',
      // 显示按钮数据
      btnObj: {
        type: 'phone',
        btnText: '拨打电话',
        isShow: false,
      },
      /* 活跃标签数据 */
      activeLabels: [],
      /** 详情配置里面是否有拨打电话按钮 */
      detailHasCallBtn: false,
      userName: '',
    },

    observers: {
      item(val) {
        if (val) {
          const { occupationStr, forwardSalary, name } = val
          const sData: any = {}
          if (!this.data.startTime) {
            sData.startTime = dayjs().unix()
          }
          if (occupationStr) {
            sData.occupationStr = occupationStr
          }
          if (forwardSalary) {
            sData.salaryText = occupationStr ? ` ・ ${forwardSalary}` : forwardSalary
          }
          if (name) {
            sData.userName = name == '先生' || name == '女士' ? '**' : `${name.slice(0, 1)}**`
          }
          this.contactBtnText()
          this.setData(sData)
        }
      },
    },
    methods: {
      /**
       * 父组件调用的方法，获取特惠标签的展示状态
       * @returns { discount: boolean, chat: boolean }
       * discount: true 是特惠拨打
       * chat:  true 是聊一聊
       * 两个都为false，不是特惠拨打
       */
      getDiscountState() {
        return new Promise(async (resolve) => {
          try {
            const { origin, item, btnObj, detailHasCallBtn } = this.data
            if (
              (origin === 'resumeIndex' || origin === 'searchResult')
              && item.isSpecialTel
              && ((btnObj.isShow && btnObj.type === 'phone') || detailHasCallBtn)
              && !item.hasTeltRigh
            ) {
              resolve({ discount: true, chat: false })
            } else {
              resolve({ discount: false, chat: (btnObj.type === 'chat' && btnObj.isShow) })
            }
          } catch (error) {
            resolve({ discount: false, chat: false })
          }
        })
      },
      /** 点击实名认证弹出弹窗 */
      async onShowModal() {
        await wx.$.u.waitAsync(this, this.onShowModal, [], 500)
        const { to_auth } = store.getState().user.userInfo
        const { login } = store.getState().storage.userState
        // 已登陆跳转认证并且没有实名
        if (login && to_auth) {
          wx.$.model
            .realName({
              type: 'realNameGrayHead',
              confirmText: '立即认证',
              cancelIcon: true,
            })
            .then(() => {
              wx.$.r.push({ path: '/subpackage/member/realname/index?origin=100021' })
            })
          return
        }
        wx.$.model.realName({ type: 'realNameSimple' })
      },
      /** 找活名片详情 */
      async onClickToDetail() {
        await wx.$.u.waitAsync(this, this.onClickToDetail, [], 500)
        this.handleNavigate(this.data.type)
      },
      /** im直接扣费 */
      async onGetImChat(e) {
        await wx.$.u.waitAsync(this, this.onGetImChat, [e], 500)
        const { item } = this.data
        const { rightInfo, resumeSubUuid } = item || {}
        const { hasImRight } = rightInfo || {}
        const { relatedInfoId: nRelatedInfoId } = e || {}
        wx.$.l.getImChat({ uuid: resumeSubUuid, scene: 2 }, { hasImChatRight: hasImRight }, {
          success: (sucRes) => {
            const { relatedJobId, isChatSearchPurchase } = sucRes || {}
            wx.hideLoading()
            this.phoneOrChatReport(hasImRight ? '2' : '1', 3)
            this.chatOk()

            wx.$.l.initGroup(resumeSubUuid, 2, { relatedInfoId: relatedJobId || (nRelatedInfoId && nRelatedInfoId != '0' ? nRelatedInfoId : ''), fromType: isChatSearchPurchase ? 10 : '' })
          },
          fail: () => {
            setTimeout(() => {
              wx.hideLoading()
            }, 2000)
            this.phoneOrChatReport('0', '0', 3)
          },
        })
      },
      // 拨打电话
      async onCallPhone() {
        await wx.$.u.waitAsync(this, this.onCallPhone, [], 1000)
        const { item, btnObj, selectedTab, sceneV2 } = this.data
        const { rightInfo, resumeSubUuid } = item || {}
        const { hasImRight } = rightInfo || {}
        const { userState } = store.getState().storage
        const { login } = userState || {}
        /** 未登录，去登录 */
        if (!login) {
          toLogin(true)
          return
        }
        const { jobId, jobDraftId, checkStatus } = selectedTab || {}
        // 聊一聊
        if (btnObj.type == 'chat') {
          wx.showLoading({ title: '正在联系...', mask: true })
          const filterData = await getFilterData()
          const { classify_id } = filterData || {}
          const occV2 = (classify_id || []).join(',') || ''
          // eslint-disable-next-line @typescript-eslint/no-this-alias
          const that = this
          wx.$.l.getImChatPre(
            { uuid: resumeSubUuid, scene: 2, jobId, jobDraftId, sceneV2 },
            { hasImChatRight: hasImRight, fastQuery: { occV2 }, cpShow: true, bossPickJobScene: jobId && checkStatus == 2 && jobId != '0' ? 2 : 0, selectPositionTabId: jobId },
            {
              selectpostionback: (spRes) => {
                that.triggerEvent('selectpostionback', { ...spRes })
              },
              reGetImChat: (res) => {
                that.onGetImChat(res)
              },
            },
          ).then(async (sucRes) => {
            const { relatedJobId, isChatSearchPurchase } = sucRes || {} as any
            wx.hideLoading()
            this.phoneOrChatReport(hasImRight ? '2' : '1', 3)
            this.chatOk()
            wx.$.l.initGroup(resumeSubUuid, 2, { relatedInfoId: relatedJobId || (jobId && checkStatus == 2 && jobId != '0' ? jobId : ''), fromType: isChatSearchPurchase ? 10 : '' })
          }).catch(() => {
            setTimeout(() => {
              wx.hideLoading()
            }, 2000)
            this.phoneOrChatReport('0', '0', 3)
          })
          // 拨打电话
        } else {
          // 打电话之前，需要做特殊处理
          // await this.beforeCall()
          this.handleCallPhone()
        }
        this.triggerEvent('callPhoneFunc', item)
      },
      /**
       * 打电话之前，需要做特殊处理
       * 需要弹出特惠拨打的弹窗
       */
      async beforeCall() {
        const { origin, item, btnObj, detailHasCallBtn } = this.data
        /**
         * 满足特惠拨打的条件
         * “找牛人”页面的简历大列表、搜索牛人列表
         * 简历满足定价方案设置的信息标签“特惠拨打”。
         * 简历列表 或 简历详情页配置了打电话按钮
         * 简历无生效中的打电话权益。
         */
        if (
          (origin === 'resumeIndex' || origin === 'searchResult')
          && item.isSpecialTel
          && ((btnObj.isShow && btnObj.type === 'phone') || detailHasCallBtn)
          && !item.hasTeltRigh
        ) {
          // 弹窗配置 是否有配置弹窗标识
          const popup = await dealDialogApi({ dialogIdentify: 'dialDiscount' })
          if (popup) {
            const l = store.getState().storage.resumeDisCountCallPhone
            const { userId } = store.getState().storage.userState
            if (l.includes(userId)) {
              return Promise.resolve(true)
            }
            await wx.$.model.discountCall({
              success: () => {
                return Promise.resolve(true)
              },
            })
          }
        }
        return Promise.resolve(true)
      },
      async handleCallPhone() {
        const { item, selectedTab, sceneV2 } = this.data
        const { dial, resumeSubUuid } = item || {}
        // 拨打电话前需要进行实名判断
        wx.showLoading({ title: '正在联系...', mask: true })
        const hasShowPhone = dial
        const filterData = await getFilterData()
        const popParams = { occupations: filterData.classify_id, cities: filterData.area_id }

        wx.$.l.resumeTelV3({ uuid: resumeSubUuid, popParams, jobId: selectedTab?.jobId, jobDraftId: selectedTab?.draftId, sceneV2 }, {
          pageName: '简历列表',
          hasShowPhone,
        }, item.buriedPointData).then(async (res) => {
          wx.hideLoading()
          const { data, code } = res || {}
          if (!data) {
            this.phoneOrChatReport('0')
            return
          }
          /** 拨打电话埋点 */
          this.phoneOrChatReportRes = res
          this.phoneOrChatReport(!['0', '200', '10000'].includes(`${code}`) ? '0' : `${!hasShowPhone ? '1' : '2'}`)
          const { tel, dialogIdentifyDefault, dialogData } = data
          const { dialogIdentify = '' } = dialogData || {}
          const showNewResumeMp = (await wx.$.l.newResumeMidPops()).includes(dialogIdentify)
          if (tel || dialogIdentifyDefault || showNewResumeMp) {
            await wx.$.l.operationMidCallV3(
              { ...res.data },
              { hasShowPhone },
              {
                callPopBack: (pop) => {
                  wx.$.resumeMidModel({
                    ...pop,
                    zIndex: 10011,
                    source: '2',
                    infoId: resumeSubUuid,
                    pageName: '简历列表',
                    call: (e) => {
                      this.onCallMidPhone(e)
                    },
                  })
                  this.callPhoneOk()
                },
                callPhoneOk: () => {
                  this.callPhoneOk()
                },
                callRealPhone: (isPrivacy = 0) => {
                  this.callRealOrMidPhone(isPrivacy)
                },
                callPhone: () => {
                  this.onCallPhone()
                },
              },
            )
          } else {
            this.callRealOrMidPhone(0)
          }
        })
          .catch(() => {
            wx.hideLoading()
            this.phoneOrChatReport('0')
          })
      },

      /** 拨打电话(真实拨打或者中间号拨打) */
      callRealOrMidPhone(isPrivacy = 1) {
        const { item } = this.data
        const { resumeSubUuid } = item || {}
        wx.$.l.resumeTelV3({ uuid: resumeSubUuid, isPopup: 0, isPrivacy }, {}, item.buriedPointData).then(async (resTel) => {
          if (resTel.code == 0) {
            await wx.$.l.operationMidCallV3(
              { ...resTel.data },
              {},
              {
                callPopBack: (pop) => {
                  wx.$.resumeMidModel({
                    ...pop,
                    zIndex: 10011,
                    source: '2',
                    infoId: resumeSubUuid,
                    pageName: '简历列表',
                    call: (e) => {
                      this.onCallMidPhone(e)
                    },
                  })
                },
              },
            )
            this.callPhoneOk()
          }
        })
      },

      /** 组件回调拨打中间号 */
      async onCallMidPhone(e) {
        await wx.$.u.waitAsync(this, this.onCallMidPhone, [e], 1500)
        const { item } = this.data
        // val.detail 为 2 代表拨打虚拟号
        const val = e ? e.detail : 2
        if (val == 2) {
          const showRealTelState = storage.getItemSync('showRealTelState')
          if (showRealTelState) {
            const currentPage = wx.$.r.getCurrentPage()
            storage.setItemSync('showRealTel', currentPage.route)
          }
        }
        wx.showLoading({ title: '正在联系...', mask: true })
        wx.$.l.resumeTelV3({ uuid: item.resumeSubUuid, isPopup: 0, isPrivacy: val == 2 ? 1 : 0 }, {}, item.buriedPointData)
          .then(async (resTel) => {
            wx.hideLoading()
            const { code, data } = resTel
            if (code == 0) {
              if (val == 2 && data.timeRemaining) {
                await wx.$.l.midPhoneTimeMsg(data.timeRemaining)
              }
              setTimeout(() => {
                wx.$.u.callPhone(data.tel)
                this.callPhoneOk()
              }, 200)
            }
          })
          .catch(() => {
            wx.hideLoading()
          })
      },
      /** 拨打电话返回结果处理逻辑 */
      callPhoneOk() {
        helper.list.cardViewHistory.setHistory('resume', this.data.item.resumeSubUuid, true, true)
        // 成功消费后设置为已联系
        const rightInfo = this.data.item.rightInfo ? this.data.item.rightInfo : {}
        const nItem = { ...this.data.item, dial: true, viewed: true, rightInfo: { ...rightInfo, hasTelRight: true } }
        this.setData({ item: nItem })
        this.triggerEvent('itchange', { item: nItem })
      },
      /** 拨打电话返回结果处理逻辑 */
      chatOk() {
        // 成功消费后设置为继续聊
        const rightInfo = this.data.item.rightInfo ? this.data.item.rightInfo : {}
        const nItem = { ...this.data.item, dial: true, viewed: true, rightInfo: { ...rightInfo, hasImRight: true } }
        this.setData({ item: nItem })
        this.triggerEvent('itchange', { item: nItem })
      },

      /**
   * @description 拨打电话和聊一聊埋点
   * @param {object} get_status 获取状态
   * @param {object} get_status 拨打电话返回的data数据
   * @param clickType 点击类型 1、2、3(1-页面底部、2-页面中间、3-聊一聊)
   */
      async phoneOrChatReport(get_status, clickType = 1) {
        const { startTime, item, sourceId } = this.data
        const { rightInfo, pagination, occupation, occupationStr, activeStatusText, guid, resumeId, location_id, pagination_location, search_result } = item || {}
        const r = this.phoneOrChatReportRes
        const { telRightPricingId } = rightInfo || {}
        const source = SOURCE_ID_NAME_DATA[sourceId] || ''
        const { occId, mode } = occupation || {}
        let occupations_type = '-99999'
        if (mode == 2) {
          occupations_type = '招聘'
        } else if (mode == 1) {
          occupations_type = '订单'
        }
        const eventData: any = {
          info_id: `${resumeId}`,
          request_id: guid || '',
          location_id: `${location_id || ''}`,
          pagination: `${pagination || ''}`,
          pagination_location: `${pagination_location || ''}`,
          source_id: `${sourceId || ''}`,
          source,
          // 活跃状态
          active_status: activeStatusText || '',
          click_entry: `${clickType || '-99999'}`,
          dialing_interval_duration: (dayjs().unix() - startTime).toString(), // 拨打间隔时长(秒) 从进入详情页面到点击拨打电话的时间间隔
          get_status: `${get_status == 0 ? '0' : (get_status || '-99999')}`, // 获取状态 0、1、2（0-获取失败、1-获取成功（首次）、2-获取成功（非首次））
          resume_uuid: item.resumeSubUuid,
          occupations_v2: `${occId || ''}`,
          occupations_v2_name: occupationStr || '',
          occupations_type,
          ...(r ? {
            consumption_product_score: r.data.expenseIntegral,
            fix_price_id: String(r.data.pricingId),
          } : {}),
          ...(item.buriedPointData || {}),
        }
        if (!eventData.fix_price_id) {
          eventData.fix_price_id = Number(telRightPricingId || '0')
        }

        eventData.source_id = eventData.source_id || '-99999'
        wx.$.collectEvent.event('workersPhoneCalls', eventData)
        this.phoneOrChatReportRes = null
        this.setData({ startTime: dayjs().unix() })
      },
      // eslint-disable-next-line sonarjs/cognitive-complexity
      async handleNavigate(origin = '') {
        await wx.$.u.waitAsync(this, this.handleNavigate, [origin], 500)
        const { cities, occupations, sceneV2, jobId, infoSource } = this.data
        const { item, routerType, isRelatedFactory, type, nearbyWorkerListApiSource, sourceId, selectedTab } = this.data
        // 用沟通记录自己的点击事件
        if (nearbyWorkerListApiSource == 'myContactHistory') {
          return
        }
        if ((type === 'collect' && item.show_tips == 1) || item.infoStatus == 3) {
          wx.$.msg('该信息已被下架，暂不支持查看')
          return
        }
        let nOrigin = origin
        const { userState } = store.getState().storage
        // 如果是自己的找活id就跳转到我的找活名片，否则就跳转到找活详情
        if (item.isSelf || (userState.login && item.userId && item.userId == userState.userId)) {
          wx.$.r.push({
            path: '/subpackage/resume/publish/index',
          })
        } else {
          if (isRelatedFactory && item.specialArea == 2) {
            nOrigin = 'factory'
          }
          if (item.specialArea == 3) {
            nOrigin = 'logistics'
          }
          /** 详情页埋点使用字段 */
          const buryingPoint = {
            id: item.id,
            info: {
              request_id: item.guid || '',
              location_id: `${item.location_id || ''}`,
              pagination: item.pagination || '',
              pagination_location: item.pagination_location || '',
              source_id: item.source_id || sourceId || '',
              search_result: item.search_result || '',
              ...(item.buriedPointData || {}),
            },
          }
          const path = '/subpackage/resume/detail/index'
          const params = {
            uuid: item.uuid,
            id: item.id,
            guid: item.guid,
            origin: nOrigin,
            specialArea: this.data.specialArea,
            nearbyWorkerListApiSource,
            sourceId,
            avatar: item.headPortrait,
            name: item.name,
            sex: item.sex,
            buryingPoint: JSON.stringify(buryingPoint),
            selectedTab,
            cities,
          }
          const { jobId: stJobId } = selectedTab || {}
          const query = {
            sceneV2,
            jobId: stJobId || jobId || '',
            infoSource,
          }
          this.triggerEvent('toResumeDetail')
          dispatch(actions.resumeActions.setRequestData({}))
          if (routerType === 'replace') {
            wx.$.r.replace({ path, query, params })
          } else if (routerType === 'reLaunch') {
            wx.$.r.reLaunch({ path, query, params })
          } else {
            wx.$.r.push({ path, query, params })
          }
          dispatch(actions.resumeActions.setInfo({ uuid: params.uuid, id: params.id }))
          dispatch(actions.resumeActions.fetchGetDetailOther({
            uuid: params.uuid,
            cities,
            occupations,
            originType: nOrigin,
            jobId: selectedTab?.jobId || jobId,
          }))
        }
        this.triggerEvent('click')
      },
      // 是否滑动过面试视频
      onTouchMove() {
        const { movedResumeCard = {} } = this.data
        // 本地已经记录过滑动时间，且未大于当前时间不更新缓存
        if (movedResumeCard?.time && movedResumeCard.time > Date.now()) {
          return
        }
        const value = {
          num: movedResumeCard?.num ? movedResumeCard.num + 1 : 1,
          time: dayjs()
            .endOf('day')
            .valueOf(),
        }
        dispatch(actions.storageActions.setCommonItem({ movedResumeCard: value }))
      },
      /**
       * 特惠标签展示根据详情的配置接口来判断
       */
      disCountLabelRule() {
        const { list_b: btnConfig } = storage.getItemSync('btn_resume_detail')
        const { item } = this.data
        const { occupation } = item
        const config = [] // 按钮配置
        if (btnConfig && btnConfig.length) {
          const obj = btnConfig.find((j) => j.occId == occupation.occId)
          const n_obj = btnConfig.find((j) => j.occId == 0)
          if (obj) {
            config.push(obj)
          } else if (n_obj) {
            config.push(n_obj)
          }
        }
        // 如果有配置，那么就看是否配置了打电话的按钮
        let detailHasCallBtn = false
        if (config.length) {
          detailHasCallBtn = config[0].buttonInfoList.findIndex(i => i.buttonCode.indexOf('phone') >= 0) >= 0
        } else {
          detailHasCallBtn = occupation.occClassification != 1
        }
        this.setData({ detailHasCallBtn })
      },
      // 联系按钮展示
      async contactBtnText() {
        const pageCode = getPageCode()
        await messageQueue((state) => (!!state.config.btnConfigStatus[`${pageCode}_b`] && !!state.config.btnConfigStatus.resume_detail_b))
        if (!this.data.showContentBtn || pageCode == 'resume_detail') {
          return
        }
        this.disCountLabelRule()
        // 联系按钮配置
        const { list_b: btnConfig } = storage.getItemSync(`btn_${pageCode}`)
        const { item } = this.data
        const { occupation } = item
        let btnObj = {
          type: 'phone',
          btnText: '拨打电话',
          isShow: false,
        }// 显示按钮数据
        const config = [] // 按钮配置
        if (btnConfig && btnConfig.length) {
          const obj = btnConfig.find((j) => j.occId == occupation.occId)
          const n_obj = btnConfig.find((j) => j.occId == 0)
          if (obj) {
            config.push(obj)
          } else if (n_obj) {
            config.push(n_obj)
          }
        }
        const callBtnText = item.rightInfo.hasTelRight ? '继续沟通' : item.rightInfo.telRightIsFree ? '免费拨打' : '拨打电话'
        const chatBtnText = item.rightInfo.hasImRight || item.rightInfo.hasTelRight ? '继续聊' : '聊一聊'

        if (!config.length) { // 无配置不显示
          btnObj = {
            ...btnObj,
            isShow: false,
          }
        } else if (config.length) { // 有配置
          const hConfig = config[0]// 取优先级最高的配置
          if (hConfig.buttonInfoList.findIndex(i => i.buttonCode.indexOf('phone') >= 0) >= 0 && item.userId != 0) { // 电话直拨(正常)
            btnObj = {
              type: 'phone',
              btnText: callBtnText,
              isShow: true,
            }
          } else if (hConfig.buttonInfoList.findIndex(i => i.buttonCode.indexOf('phone') >= 0) >= 0 && item.userId == 0) { // 电话直拨(代发)
            btnObj = {
              type: 'chat',
              btnText: chatBtnText,
              isShow: true,
            }
          } else if (hConfig.buttonInfoList.findIndex(i => i.buttonCode.indexOf('chat') >= 0) >= 0) { // 聊一聊
            btnObj = { type: 'chat', btnText: chatBtnText, isShow: true }
          } else {
            btnObj = {
              ...btnObj,
              isShow: false,
            }
          }
        }

        if (btnObj.type == 'chat') {
          this.triggerEvent('ischat')
        }
        this.triggerEvent('isbtnchange', { btnObj })
        this.setData({ btnObj })
      },
    },
  }),
)

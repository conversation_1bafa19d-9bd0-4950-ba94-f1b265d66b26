/*
 * @Date: 2022-04-01 09:26:20
 * @Description: 卡片列表
 */

import { subscribeComponent } from '@/lib/mini-component-page/index'
import { MapStateToData, connect, messageQueue, store, dispatch, actions, storage } from '@/store/index'
import { cardViewHistory, getResumeRecommendList, isReportEvent, listExposure, reportFindWorkList } from '@/utils/helper/list/index'
import { resumeCardFormatDataV4 } from '@/utils/helper/resume/index'
import { getDom, guid } from '@/utils/tools/common/index'
import { dealGuideLogin } from '@/utils/helper/login/index'
import { reportNoMoreExposure } from './utils'

const mapStateToData: MapStateToData = (state) => {
  const { userState, resumeFilterSort, resumeFilterType, resumeTabPosition, selectPositionTabId } = state.storage
  return {
    login: userState.login,
    userId: userState.userId,
    type: resumeFilterType.value,
    sort: resumeFilterSort.value,
    resumeTabPosition,
    selectPositionTabId,
  }
}
Component(
  subscribeComponent({
    // 下拉刷新
    async onPullDownRefresh() {
      isReportEvent.call(this, this.data.list, (res) => this.uploadStatisticsData.call(this, res))
      // this.triggerEvent('ischat', { have: false })
      // this.onRefresh(1, false, false).finally(() => {
      //   wx.stopPullDownRefresh()
      // })
    },
    // 触底刷新
    onReachBottom() {
      const { firstLoaded, hasMore, noData } = this.data
      if (hasMore || noData || !firstLoaded) {
        return
      }
      this.onRefresh(this.page + 1)
    },
  })(
    connect(mapStateToData)({
      properties: {
        topHeight: {
          type: String,
          value: '0rpx',
        },
        relatedInfoId: { type: String, value: '' },
        /**
         * 当前卡片列表使用在那个页面上，
         * searchResult 搜索结果页
         * resumeIndex 简历列表首页
         * */
        origin: { type: String, value: '' },
        firstLoaded: { type: Boolean, value: false },
      },
      data: {
        page: 1,
        list: [],
        loading: true,
        noData: false, // 没有数据
        hasMore: false, // 没有更多数据
        // 列表到顶部的距离
        listScrollTop: 0,
        isConnected: true, // 网络状态
        isNetWorkShow: false,
        is_null: 0,
        firsetloading: false, // 是否已经进行第一次加载数据
        // 推荐信息提示
        showTjDataTips: {},
        isTabChange: false, // 是否是切换tab栏
        _cityOccObj: {},
        /** 是否开启活跃标签AB实验 */
        isActiveAb: false,
        /** 是否开启列表城市工种未选中差异传参（https://yupaowang.feishu.cn/wiki/JQYowazt4iP2ejkzUuOciGWgngf） */
        isFilterAb: null,
      },
      observers: {
        list(v) {
          if (!this.isHide) {
            const { _cityOccObj } = this.data
            const sData: any = { list: v, ..._cityOccObj, page: this.page }
            // 用于判断是否显示更换职位
            this.triggerEvent('listchange', sData)
          }
        },
      },
      lifetimes: {
        created() {
          this.params = {
            /** 页码 */
            currentPage: 1,
            /** 工种id,用逗号分割 */
            occupationIdList: [],
            /** 搜索关键词 */
            keyword: '',
            /** 经纬度 */
            longitude: '',
            latitude: '',
            /** 每页记录数 */
            pageSize: 15,
          }
        },
        ready() {
          wx.$.u.isAbUi('resumeTags', 'newLogic').then(isBool => {
            this.setData({
              isActiveAb: isBool,
            })
          })
          wx.$.u.isAbUi('cvFilterOptimizition', 'newLogic').then(isBool => {
            this.setData({
              isFilterAb: isBool,
            })
            const userLocationCity = storage.getItemSync('userLocationCity')
            const id = wx.$.u.getObjVal(userLocationCity, 'resumeCityObj.id')
            if (!id && !isBool) {
              dispatch(actions.storageActions.setItem({
                key: 'userLocationCity',
                value: {
                  ...userLocationCity,
                  resumeCityObj: { id: '1', name: '全国', letter: 'quanguo', level: 0, citys: [], cityLen: 0 },
                },
              }))
            }
          })
        },
      },
      pageLifetimes: {
        show() {
          wx.$.u.isAbUi('resumeTags', 'newLogic').then(isBool => {
            this.setData({
              isActiveAb: isBool,
            })
          })
          /** 水印处理 */
          this.setData({
            list: cardViewHistory.getHistoryList('resume', this.data.list, 'resumeSubUuid'),
          })
          this.isHide && this.data.list?.length && this.onListScroll(this.page + 1)
          if (this.toResumeDetail) {
            this.triggerEvent('aggregation')
            this.toResumeDetail = false
          }
          this.isHide = false
        },
        hide() {
          this.isHide = true
          isReportEvent.call(this, this.data.list, (res) => this.uploadStatisticsData.call(this, res))
        },

      },
      methods: {
        async getDiscountState(id) {
          const n = this.selectComponent(`.child-component-${id}`)
          const { data } = n || {}
          const { origin: d, btnObj, showContentBtn } = data || {}
          // 只有找活大列表和搜索列表才上报，其他不上报这个参数
          let offer_call = ((d === 'resumeIndex' || d === 'searchResult') ? '0' : null)
          if (n?.getDiscountState) {
            const s = await n.getDiscountState()
            offer_call = (s.discount && '1') || offer_call
          }
          let is_button_external = '0'
          const { isShow } = btnObj || {}
          if (showContentBtn && isShow) {
            is_button_external = '1'
          }
          return { offer_call, is_button_external }
        },
        onIsChat() {
          this.triggerEvent('ischat', { have: true })
        },
        onNetWorkChange(e) {
          this.setData({ isConnected: e.detail.isConnected })
        },
        // page: 多少页, clear 是否立即清楚数据
        async onRefresh(nPage = 1, isSupply = false, showloading = true) {
          const page = nPage
          this.page = page
          const { isConnected, firsetloading, selectPositionTabId, resumeTabPosition } = this.data
          const { selectItem } = selectPositionTabId || {}
          const { jobId } = selectItem || {}
          let { isFilterAb } = this.data
          if (page == 1) {
            showloading && firsetloading && !jobId && wx.showLoading({ title: '加载中...' })
            showloading && firsetloading && jobId && this.setData({ isTabChange: true })
            !isConnected && this.setData({ isNetWorkShow: true })
          } else if (!isConnected) {
            wx.$.msg('您的网络好像不太给力，请稍后再试')
            return
          }
          this.setData({ loading: true })
          try {
            // 判断有工种数据情况
            await messageQueue((state) => state.classify.requestData, true)
            // 获取工种数据及label，id
            const { resumeClassify } = store.getState().storage
            const { resumeFilterScreen } = store.getState().listFilter
            const nResumeClassify = await wx.$.l.handleClassifyData(resumeClassify)
            await dispatch(actions.classActions.setResumeClassify(nResumeClassify))
            // 获取工种数据及label，id
            const { resumeOcc2Value } = store.getState().classify
            const { value, filter: rfsFilter } = resumeFilterScreen || {}
            const { age } = rfsFilter || {} as any
            const params = {
              ...this.params,
              currentPage: this.page,
              pageSize: 15,
              ...(value || {}),
              ageRangeList: [],
            }
            if (wx.$.u.isArrayVal(age)) {
              const [ageFrom, ageTo] = age
              params.ageRangeList = [{
                ageFrom,
                ageTo,
              }]
            }
            if (isFilterAb === null) {
              isFilterAb = await wx.$.u.isAbUi('cvFilterOptimizition', 'newLogic')
              this.setData({
                isFilterAb,
              })
            }
            if (wx.$.u.isArrayVal(resumeOcc2Value)) {
              params.occupationIdList = resumeOcc2Value.map(item => item.occIds).flat().filter(item => !!item)
            } else {
              params.occupationIdList = isFilterAb ? null : []
            }
            const { currentPage, ageRangeList = [], filter = [] } = params
            // 兜底数据请求参数
            const ddPrams: any = { ageRangeList, filter, currentPage, pageSize: 15, isSupply: ((this.isSupply && currentPage > 1) || isSupply) }
            if (((this.isSupply && currentPage > 1) || isSupply) && wx.$.u.isArrayVal(params.occupationIdList)) {
              ddPrams.occupationIdList = params.occupationIdList
            } else {
              ddPrams.occupationIdList = isFilterAb ? null : []
            }
            const listRes = await getResumeRecommendList.call(this, (this.isSupply && currentPage > 1) || isSupply ? { ...ddPrams, areaFlag: 2 } : { ...params, areaFlag: 1 }, isFilterAb, 'resume')
            this.setData({ isTabChange: false })
            let { res: { data = {} } } = listRes
            const { isNoTjDataTips } = listRes
            if (isNoTjDataTips) {
              this.setData({ showTjDataTips: {} })
            }

            const { provinceId, cityId, areaId, areaIds, occupationIdList: jobIds, industryId } = listRes.params
            if (!data) {
              data = { list: [] }
            }
            this.params = listRes.params
            const paramsKeys = Object.keys(resumeFilterScreen.value || {})
            paramsKeys.forEach(ky => {
              delete this.params[ky]
            })
            this.setData({ isNetWorkShow: false })
            this.triggerEvent('loadFinished')

            // 城市和职位的数据用户更换职位的数据获取
            if (!((this.isSupply && currentPage > 1) || isSupply)) {
              const nCpCityIds = (areaIds || [areaId || cityId || provinceId]).filter(Boolean).map(id => Number(id))
              const nCpOccupationIds = jobIds
              this.setData({ _cityOccObj: { cpCityIds: nCpCityIds, cpOccupationIds: nCpOccupationIds } })
            }

            // 如果是第一页并且数据为0，就展示空数据
            if (page == 1 && data.list.length == 0 && (!this.isSupply || !isSupply)) {
              this.setData({ list: [] })
              this.isSupply = true
              this.setData({ showTjDataTips: {} })
              this.triggerEvent('checkNoData', { value: false, isSupply: true })
              this.onRefresh(1, true)
              return
            }
            const sData: any = {}

            if (page == 1 && data.list.length > 0 && !isSupply) {
              this.isSupply = false
              this.isShowTjData = false
              sData.showTjDataTips = {}
              this.triggerEvent('checkNoData', { value: true, isSupply: false })
            }
            // 代表没有下一页数据了
            if (data.list.length <= 0 || data.totalPage <= params.currentPage) {
              if (!this.isShowTjData && !this.isSupply) {
                const dataList = this.handleListData(params, data.list)

                if ((!this.isShowTjData) && params.currentPage == 1) {
                  this.setData({ list: dataList })
                } else {
                  const { length: len } = this.data.list
                  const newList = dataList.reduce(
                    (obj, item, i) => {
                      // 判断是否有重复的
                      if (!this.isSupply && this.data.list.find((record) => record?.id === item.id)) {
                        // eslint-disable-next-line no-param-reassign
                        obj[`list[${i + len}]`] = {}
                      } else {
                        // eslint-disable-next-line no-param-reassign
                        obj[`list[${i + len}]`] = item
                      }
                      return obj
                    },
                    { noData: false },
                  )
                  this.setData({ ...newList })
                }
                this.isShowTjData = true
                const { length: len } = this.data.list
                let oItem = { guid: '' }
                if (len > 0) {
                  oItem = this.data.list[len - 1]
                }
                this.setData({ showTjDataTips: { guid: guid(), type: 'isShowTjData', oguid: oItem.guid } })
                // 判断如果有搜索数据的情况下，翻页到最后是否需要显示推荐数据
                this.isShowTjData = true
                // 是否已显示推荐数据(搜索数据有和没有的都用这个参数判断)
                this.isSupply = true
                this.onRefresh(1, true, false)
                reportNoMoreExposure.call(this)
                return
              }
              sData.hasMore = true
            } else {
              sData.hasMore = false
            }
            // !这里进行值过滤
            const list = this.handleListData(params, data.list)
            // 如果是第一页，那么就直接赋值
            if (page == 1 && !this.isShowTjData) {
              sData.list = list.slice(0, 5)
              sData.noData = false
              if (data.list.length == 0 && this.isSupply) {
                sData.noData = true
              }
              // 只赋值5条
              this.setData(sData)
              // 等待200ms赋值剩下的
              setTimeout(() => {
                this.setData({ ...sData, list: this.data.list.concat(list.slice(5)) })
                this.onListScroll(page)
              }, 200)
              return
            }
            // 如果不是第一页就对某个数组下标赋值
            const { length } = this.data.list
            const newList = list.reduce(
              (obj, item, i) => {
                // 判断是否有重复的
                if (!this.isSupply && this.data.list.find((record) => record?.id === item.id)) {
                  // eslint-disable-next-line no-param-reassign
                  obj[`list[${i + length}]`] = {}
                } else {
                  // eslint-disable-next-line no-param-reassign
                  obj[`list[${i + length}]`] = item
                }
                return obj
              },
              { noData: false },
            )
            dealGuideLogin('resumeList', '', this.page)
            this.setData({ ...sData, ...newList })
            this.onListScroll(page)
          } finally {
            if (page == 1 && showloading) {
              firsetloading && wx.hideLoading()
            }
            this.setData({ loading: false, firsetloading: true })
            this.triggerEvent('loadFinished')
            this.triggerEvent('onRefreshFinally')
          }
        },
        // 处理列表数据
        handleListData(params, nlist) {
          let list = cardViewHistory.getHistoryList('resume', resumeCardFormatDataV4(nlist), 'resumeSubUuid')
          // 如果登录了，就吧列表上属于自己的找活名片排除掉！
          // list = this.data.login ? list.filter(item => item.user_id !== this.data.userId) : list
          // 先去重当前返回的 加上uuid
          const { list: olist } = this.data
          const dataList = params.currentPage == 1 && !this.isShowTjData ? [] : olist
          const len = dataList.length
          list = list.filter(
            (item, i, arr) => item.uuid && arr.findIndex((record) => {
              return record.id === item.id
            }) === i,
          ).map((item, index) => {
            item.pagination_location = `${index + 1}`
            item.location_id = `${index + 1 + len}`
            item.pagination = params.currentPage
            item.source_id = '1'
            return item
          })
          return list
        },
        onItChange(e) {
          const { index } = e.currentTarget.dataset
          const { item } = e.detail
          this.setData({ [`list[${index}]`]: item })
        },
        /** 监听列表滚动上报埋点 */
        onListScroll(page) {
          const { listScrollTop } = this.data
          if (!listScrollTop) {
            getDom('#header').then((res) => {
              this.setData({ listScrollTop: res?.height || 0 })
              listExposure.call(this, {
                page,
                elementId: '.resume-item',
                top: -(res?.height || 0),
                callback: (res) => this.uploadStatisticsData.call(this, res),
              })
            })
            return
          }
          listExposure.call(this, {
            page,
            elementId: '.resume-item',
            top: -listScrollTop,
            callback: (res) => this.uploadStatisticsData.call(this, res),
          })
        },
        /** 埋点上报 */
        async uploadStatisticsData(res) {
          const { offer_call, is_button_external } = await this.getDiscountState(res.item.id)
          res.offer_call = offer_call

          const { isActiveAb } = this.data
          const otherEvent: any = {
            source_id: '1',
            is_button_external,
          }
          if (isActiveAb) {
            const active_status = []
            const userInfo = res.item.userInfo || {}
            if (userInfo.isNew) {
              // 新牛人
              active_status.push('100')
            }
            if (wx.$.u.isArrayVal(userInfo.activeLabels)) {
              active_status.push(userInfo.activeLabels[0])
            }
            if (wx.$.u.isArrayVal(active_status)) {
              otherEvent.active_status = active_status.join(',')
            }
          }
          reportFindWorkList(res, otherEvent)
        },
        /** 卡片点击位置授权 */
        onLocalClick(e) {
          this.triggerEvent('localchange', e.detail)
        },
        /** 进入找活详情 */
        onToResumeDetail(e) {
          this.toResumeDetail = true
        },
        /** 删除列表数据中的某一条数据 */
        onDeleteItem(index) {
          const { list } = this.data
          let showTjDataTips = this.data.showTjDataTips || {}
          if (this.isSupply && this.data.showTjDataTips.oguid && this.data.showTjDataTips.oguid == list[index].guid) {
            if (index == 0) {
              showTjDataTips = {}
              this.triggerEvent('checkNoData', { value: false, isSupply: true })
            } else {
              showTjDataTips = { ...this.data.showTjDataTips, oguid: list[index - 1].guid }
            }
          }
          list.splice(index, 1)
          this.setData({ list, showTjDataTips })
        },
        onSelectPostionBack(e) {
          this.triggerEvent('selectpostionback', e.detail)
        },
        /** 搜索 */
        onSearch() {
          this.triggerEvent('onSearch')
        },
        /** 查看收藏列表 */
        toCollectList() {
          wx.$.r.push({
            path: '/subpackage/member/b_collect/index',
          })
        },
      },
    }),
  ),
)

.body {
  padding-bottom: 120rpx;
}

.no-data-img {
  width: 264rpx !important;
  height: 258rpx !important;
}

.spe_text {
  width: 85% !important;
}

.em-tj-v {
  padding: 26rpx 0;
  margin: 16rpx 0;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 16rpx;
  padding-bottom: 32rpx;
  color: #808080;
  font-size: 28rpx;
}

.empty {
  padding-top: 12vh;
}

.empty-btn {
  padding: 0 24rpx !important;
  height: 80rpx !important;
  line-height: 80rpx !important;
  background: #0092ff !important;
  margin: 30rpx auto 0 auto !important;
  border-radius: 8rpx !important;
  color: rgba(255, 255, 255, 0.95) !important;
  font-size: 32rpx !important;
  font-weight: bold !important;
  text-align: center !important;
}


.no-more-text{
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.25);
  font-size: 26rpx;
}

.no-more-text-btn{
  color: rgba(0, 146, 255, 1);
  font-size: 26rpx;
  font-weight: bold;
  border-radius: 16rpx;
  border: 2rpx solid rgba(0, 146, 255, 1);
  padding: 12rpx 32rpx;
  margin: 16rpx 24rpx;
  background-color: #fff;
}

.em-tj-v{
  display: flex;
  align-items: center;
  justify-content: center;
}

.em-tj-v-box-left{
  width: 66rpx;
  height: 2rpx;
  background: linear-gradient(270deg,  rgba(0, 146, 255, 1) 0%, rgba(245, 246, 250, 1) 100%);
}

.em-tj-v-box-left-text{
  display: flex;
  align-items: center;
  color:rgba(0, 146, 255, 1);
  font-size: 26rpx;
  margin: 0 16rpx;
}

.em-tj-v-box-img{
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.em-tj-v-box-right{
  width: 66rpx;
  height: 2rpx;
  background: linear-gradient(90deg,  rgba(0, 146, 255, 1) 0%, rgba(245, 246, 250, 1) 100%);
}
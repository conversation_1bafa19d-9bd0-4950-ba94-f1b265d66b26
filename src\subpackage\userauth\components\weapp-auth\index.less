.user-auth-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background-color: #fff;
}

.login-bg {
  width: 100%;
  height: 400rpx;
  background: linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(224, 243, 255, 1) 100%);
}

.header-bg {
  background-color: unset !important;
}

.phoneLoginHeader {
  // height: 32rpx;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.logo {
  position: relative;
  display: block;
  width: 176rpx;
  height: 176rpx;
  margin: -100rpx auto 32rpx;
  border-radius: 8rpx;
}

.desc {
  margin-top: 32rpx;
  display: block;
  line-height: 70rpx;
  color: rgba(0, 0, 0, 0.85);
  font-size: 50rpx;
  text-align: center;
  font-weight: bold;
  letter-spacing: 2rpx;

}

.login-text {
  height: 36rpx;
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
  font-weight: 400;
  font-size: 26rpx;
  line-height: 36rpx;
  margin-top: 16rpx;
}

.login-btn {
  position: relative;
  width: 654rpx !important;
  line-height: 96rpx;
  margin-top: 84rpx;
  border-radius: 16rpx;
  font-size: 34rpx;
  font-weight: bold;
  padding: 0 14rpx;
  text-align: center;
}

.fast-login-btn {
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
}

.default-login-btn {
  background: white;
  border: 2rpx solid #eee;
  color: rgba(0, 0, 0, 0.85);
}

.back-last-page-btn {
  line-height: 48rpx;
  background: #fff;
  border: none;
  color: rgba(0, 0, 0, 0.45);
  position: relative;
  width: 622rpx;
  margin-top: 40rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  text-align: center;
  font-weight: normal;
  padding: 0;

  &::after {
    border: none;
  }
}

.user-register-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background-color: #fff;
}

.register-title {
  width: fit-content;
  display: flex;
  flex-direction: row;
  align-items: center;
  line-height: 56rpx;
  margin-left: 64rpx;
  color: rgba(0, 0, 0, 0.85);
  padding-top: 92rpx;

  .register-title-icon {
    font-size: 52rpx;
    color: #0092ff;
    margin-right: 16rpx;
  }

  .register-title-text {
    font-size: 40rpx;
    font-weight: bold;
  }
}

.register-form {
  width: 100vw;
  box-sizing: border-box;
  padding: 48rpx 64rpx 0 64rpx;

  .register-form-item {
    width: 622rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    margin-top: 48rpx;
    border-bottom: 2rpx solid #eff1f6;

    .phone-icon {
      color: #0092ff;
      margin-right: 16rpx;
    }

    .default-icon {
      color: #000;
      margin-right: 16rpx;
    }

    .form-item-input {
      flex: 1;
      height: 88rpx;
      line-height: 88rpx;
      font-size: 28rpx;
      caret-color: #0092ff;
    }

    .form-item-input-pwd {
      flex: 1;
      height: 88rpx;
      line-height: 88rpx;
      font-size: 28rpx;
      caret-color: #0092ff;
    }

    .get-code-btn {
      color: #0097fe;
      font-size: 28rpx;
    }
  }

  .word-tips {
    width: 100%;
    height: 40rpx;
    line-height: 40rpx;
    font-size: 24rpx;
    text-align: center;
    color: #f74742;
    margin-top: 16rpx;
  }

  .register-btn {
    width: 622rpx;
    height: 88rpx;
    line-height: 88rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    text-align: center;
    background: #0092ff;
    margin-top: 12rpx;
    color: white;
    font-weight: normal;
    padding: 0 14rpx;
  }

  .user-register-toggle {
    padding-top: 32rpx;
    font-size: 28rpx;
    font-weight: bold;
    color: #0092ff;
    text-align: center;

    .register-toggle-text {
      display: inline-block;
      line-height: 44rpx;
    }
  }
}

.agreement {
  padding: 40rpx 0rpx;
  color: @text-third-color;
  font-size: 24rpx;
  line-height: 40rpx;

  &.padding {
    padding: 40rpx 48rpx;
  }

  .box {
    display: flex;

    .check {
      flex-shrink: 0;
      width: 32rpx;
      height: 40rpx;
      margin-right: 8rpx;
    }

    .icon {
      display: flex;
      align-items: flex-start;
      color: #d5d9e5;
      font-size: 48rpx;
    }

    .color {
      color: @primary-color;
    }
  }

  .agreement-text {
    color: rgba(0, 0, 0, 0.45);
    font-weight: 400;
    font-size: 26rpx;
    line-height: 1.6;
  }
}

.agreementFast {
  margin: 0 auto;
}

.close {
  width: 52rpx;
}

// 登录->温馨提示(隐私协议)
.loginProtocolPopup {
  background-color: #fff;
  min-height: 20vh;
  padding: 48rpx 32rpx 0;
  border-radius: 16rpx 16rpx 0 0;

  width: 100%;
  overflow: hidden;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  .tipTitle {
    position: relative;
    color: @text85;
    font-size: 38rpx;
    font-weight: bold;
    text-align: left;
  }

  .loginTipCloseIcon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }

  .tipDesc {
    color: @text65;
    font-size: 30rpx;
    font-weight: 400;
    text-align: left;
    margin-top: 24rpx;
  }

  .color {
    color: #0092ff;
  }

  .footer {
    background: #fff;

    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 48rpx;

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8rpx;

      height: 96rpx;
      font-size: 34rpx;
      font-weight: 500;
    }

    .btn-confirm,
    .register-btn {
      width: 100% !important;
      background: @primary-color;
      margin: 0;
      color: rgba(255, 255, 255, 0.95);
    }
  }

  .emptyView {
    height: 48rpx;
  }
}

.tips {
  font-size: 26rpx;
  padding-top: 16rpx;
  color: rgba(0, 0, 0, 0.65);
  margin-left: 64rpx;
}
/*
 * @Date: 2022-03-09 14:58:54
 * @Description: 视频上传进度弹窗组件
 */

import { getSystemInfoSync } from '@/utils/tools/common/index'

Component({
  properties: {
    /** 弹窗显示状态 */
    visible: {
      type: Boolean,
      value: false,
    },
    /** 上传状态：0 - 上传中，1 - 上传失败，2 - 上传成功 */
    state: {
      type: Number,
      value: 0,
    },
    /** 上传进度 */
    progress: {
      type: Number,
      value: 0,
    },
  },
  data: {
    /** canvas实例 */
    canvas: null,
    /** canvas画圆的半径 */
    canvasRadius: 0,
  },
  observers: {
    progress(val) {
      if (val > 0) {
        this.drawProgress(val)
      }
    },
  },
  methods: {
    /** 上传成功点击按钮 */
    onClickSuccess() {
      this.triggerEvent('success')
    },
    /** 上传失败点击关闭 */
    onClickClose() {
      this.triggerEvent('close')
    },
    /** 上传失败点击重新上传 */
    onClickRetry() {
      this.triggerEvent('retry')
    },
  },
})

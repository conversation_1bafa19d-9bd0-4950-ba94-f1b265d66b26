import { isIos } from '@/utils/tools/validator/index'
import { createIntegerDesc } from './utils-popup'

/** @name JSON格式化ext信息 */
export function getFmtOnLoadQuery(query) {
  // 站内信进入
  const ext = query.ext ? JSON.parse(query.ext) : query.ext
  return { ...query, ext }
}

/** @name 展示获取积分弹窗->用户决定是否->弹出充值积分弹窗 */
export async function showGetIntegralModal(res) {
  try {
    const resData = res.data || {}
    const resHeadMsg = res.head?.msg
    const popupData = resData.popup || {}

    const title = popupData.title || '温馨提示'
    const content = popupData.content || resHeadMsg || ''
    await wx.$.confirm({ title, content, confirmText: '获取积分', cancelText: '取消' })
    // iOS无法充值
    if (isIos()) {
      wx.$.r.push({ path: '/subpackage/member/getintegral/index' })
      return
    }
    // 弹出积分充值弹窗
    this.onOpenRechargePopup({ detail: { textList: createIntegerDesc(resHeadMsg, resData.rules) } })
  } catch (error) {
    console.error(error)
  }
}

/** 检测是否已发布 */
export function publishFetchCall() {
  wx.$.javafetch['POST/job/v2/manage/job/publish/check']().then(res => {
    if (res.code == 0) {
      this.setData({
        isPublished: res.data.published || false,
      })
    }
  })
}

/** 检测是否已发布 -- 我联系过谁的 */
export function publishFetchMyContact() {
  wx.$.javafetch['POST/job/v3/manage/job/tab/checkUserHasNotEndJob']().then(res => {
    if (res.code == 0) {
      this.setData({
        isExistRecruit: res.data?.exist || false,
      })
    }
  })
}

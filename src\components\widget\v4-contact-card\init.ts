/* eslint-disable max-len */

import { store } from '@/store/index'

/** @name 初始化数据 */
export const initData = {
  /** 我联系的人 */
  isMyContact: false,
  /** 谁联系过我 */
  isWhoContactedMe: false,

  /** 是否为招工卡片 */
  isBoss: false,
  /** 是否为找活卡片 */
  isWorker: false,

  /** 是否展示已下架图标 */
  isShowSoldOutIcon: false,
  /** 是否展示已招满图标 */
  isShowBoosFullIcon: false,
  /** 是否展示点击卡片提示信息 */
  isShowClickCardTip: false,
  /** 是否展示 已下架文案 */
  isShowSoldOutText: false,

  /** 是否展示投诉按钮 */
  isShowComplainBtn: false,
  /** 是否展示真实号码按钮 */
  isShowRealTelBtn: false,
  /** 是否展示点击联系按钮 */
  isShowContactBtn: false,

  /** 是否存信息 id */
  isExistInfoId: false,

  /** 牛人卡片--是否显示三个点-更多按钮 */
  isShowMoreBtn: false,
}

type ContactCardItem = YModels['POST/clues/v1/contact/page']['Res']['data']['data'][0]
type ContactWorkCardItem = YModels['POST/clues/v2/contact/pageResume']['Res']['data']['data'][0]

/** @function 初始化卡片展示所需数据 */
export function initV4ContactCardData(cardType: string, item: ContactCardItem | ContactWorkCardItem) {
  const { userChooseRole } = store.getState().storage
  // 是否是B端查看牛人信息
  if (userChooseRole == 1) {
    return initV4ContactWorkerCardData(cardType, item)
  }

  // 处理C端--查看老板信息的卡片逻辑
  const { identityType, userId, uuid, infoId, resumeId, infoStatusType, infoAuditStatusType, isFocus, isDial, isViewRealTel, moreThanSevenDay } = item || {}
  /** 是否为我联系的人 */
  const isMyContact = cardType === 'myContacted'
  /** 是否为谁联系过我 */
  const isWhoContactedMe = cardType === 'whoContacted'

  /** 是否为采集信息 */
  const isIC = !userId
  /** 是否为招工信息 */
  const isBoss = identityType?.code == 1 || userChooseRole == 2
  /** 是否为找活信息 */
  const isWorker = identityType?.code == 2
  /** 是否存在信息 */
  const isExistInfoId = isMyContact ? !!infoId : !!resumeId

  /** 是否正在找 */
  const isLooking = infoStatusType?.code == 1
  /** 是否 已找到 or 已招满 */
  const isFindItOrFull = infoStatusType?.code == 2
  /** 是否已下架 */
  const isSoldOut = infoStatusType?.code == 3

  /** 是否审核中 */
  const isAudit = infoAuditStatusType?.code == 1
  /** 是否审核失败 */
  const isAuditFail = infoAuditStatusType?.code === 0
  /** 是否审核成功 */
  const isAuditSuccess = infoAuditStatusType?.code == 2

  /** 是否 7 天内 */
  const isWithIn7Day = !moreThanSevenDay
  const baseCardStatus = { isMyContact, isWhoContactedMe, isIC, isBoss, isWorker, isExistInfoId, isFindItOrFull }

  if (isMyContact) {
    // 我沟通过的 
    return { ...getMyContactBoosCardStatus(), ...baseCardStatus }
  }
  // 谁沟通过我
  return { ...getWhoContactMeBoosCardStatus(), ...baseCardStatus }

  function getMyContactBoosCardStatus() {
    // 彻底删除无 uuid 和 infoId
    const isDelBoosCard = !infoId
    if (isDelBoosCard) {
      return { ...initData, isShowSoldOutIcon: true, isShowClickCardTip: true, isShowSoldOutText: true }
    }

    const isShowSoldOutIcon = isAuditFail || isSoldOut
    const isShowBoosFullIcon = !isShowSoldOutIcon && isFindItOrFull

    const isShowClickCardTip = isAudit || isAuditFail || !isWithIn7Day || (isIC && (isFindItOrFull || isSoldOut))
    const isShowSoldOutText = !isWithIn7Day && isFindItOrFull
    const isShowComplainBtn = !isLooking && !isFindItOrFull && (isWithIn7Day || isAudit || isAuditFail)

    const showBtn = isAuditSuccess && (isLooking || isFindItOrFull)
    const isShowRealTelBtn = isViewRealTel && showBtn
    const isShowContactBtn = isDial && showBtn
    return {
      ...initData,
      isShowSoldOutIcon,
      isShowBoosFullIcon,

      isShowClickCardTip,
      isShowSoldOutText,
      isShowComplainBtn,

      isShowRealTelBtn,
      isShowContactBtn,
    }
  }

  /** 谁联系过我的找活信息的boss卡片信息处理 */
  function getWhoContactMeBoosCardStatus() {
    const isShowSoldOutIcon = isSoldOut || isAudit || isAuditFail || !isWithIn7Day || (isIC && (isFindItOrFull || isSoldOut))

    const isShowClickCardTip = isSoldOut || isAudit || isAuditFail || !isWithIn7Day || (isIC && (isFindItOrFull || isSoldOut))
    const isShowSoldOutText = isSoldOut || isAudit || isAuditFail || !isWithIn7Day || (isIC && (isFindItOrFull || isSoldOut))

    const isShowRealTelBtn = false
    const isShowContactBtn = !isSoldOut
    return {
      ...initData,

      isShowSoldOutIcon,

      isShowClickCardTip,
      isShowSoldOutText,

      isShowRealTelBtn,
      isShowContactBtn,
    }
  }
}

/** @function 初始化--B端-查看牛人卡片--展示所需数据 */
export function initV4ContactWorkerCardData(cardType: string, item: ContactWorkCardItem) {
  /** 是否为我联系的人 */
  const isMyContact = cardType === 'myContacted'
  /** 是否为谁联系过我 */
  const isWhoContactedMe = cardType === 'whoContacted'
  /** 是否为采集信息 */
  const isIC = !item?.userInfo?.userId
  /** 是否存在此信息 */
  const isExistInfoId = !!item?.infoId

  /**
 * 1用户发布 0系统发布（不展示拨打按钮）
 * private Integer resumeStatus;
 * 
 */
  /**
  审核状态 1:待审核,2:审核通过,3:审核不通过
  private  Integer auditStatus;
  /**
   * 信息状态 1:正在找 2:已找到 3:已下架
  private  Integer infoStatus;
   */

  /** 是否正在找 */
  const isLooking = item?.infoStatus == 1
  /** 是否 已找到 */
  const isFindItOrFull = item?.infoStatus == 2
  /** 是否已下架 */
  const isSoldOut = item?.infoStatus == 3

  /** 是否待审核(审核中) */
  const isAudit = item?.auditStatus == 1
  /** 是否审核失败 */
  const isAuditFail = item?.auditStatus == 3
  /** 是否审核成功 */
  const isAuditSuccess = item?.auditStatus == 2

  /** 是否 7 天内 （接口都是返回的7天内的数据） */
  const isWithIn7Day = true

  const baseCardStatus = { isMyContact, isWhoContactedMe, isIC, isBoss: false, isWorker: true, isExistInfoId, isFindItOrFull }

  if (isMyContact) {
  // 我沟通过的 
    return { ...getMyContactWorkerCardStatus(), ...baseCardStatus }
  }
  // 谁沟通过我
  return { ...getWhoContactMeWorkerCardStatus(), ...baseCardStatus }

  function getMyContactWorkerCardStatus() {
    const isDelWorkerCard = !item?.infoId
    // if (isDelWorkerCard) {
    //   return { ...initData, isShowSoldOutIcon: true, isShowClickCardTip: true, isShowSoldOutText: true }
    // }

    // 已下架-斜标
    const isShowSoldOutIcon = isAuditFail || isSoldOut
    const f_isShowClickCardTip = isAudit || isAuditFail || !isWithIn7Day || (isIC && (isFindItOrFull || isSoldOut))

    // 显示 投诉/已投诉的按钮
    const isShowComplainBtn = isSoldOut && item?.complaintStatus == 3

    // 显示按钮的前置条件： 审核成功 + 未下架 +  用户自己创建的简历
    const showBtn = isAuditSuccess && (isLooking || isFindItOrFull) && item?.resumeStatus == 1
    // 显示真实号码
    const isShowRealTelBtn = item?.ifViewRealTel && showBtn
    // 显示拨打号码
    const isShowContactBtn = showBtn
    const isShowMoreBtn = item.cooperationSwitch || isShowComplainBtn || item.canComment || isShowRealTelBtn

    return {
      ...initData,
      isShowSoldOutIcon,
      isShowClickCardTip: isDelWorkerCard ? true : f_isShowClickCardTip,
      isShowComplainBtn,

      isShowRealTelBtn,
      isShowContactBtn,
      isShowMoreBtn,
    }
  }

  /** 谁联系过我的工友卡片信息处理 */
  function getWhoContactMeWorkerCardStatus() {
    // 已下架-斜标
    const isShowSoldOutIcon = isAuditFail || isSoldOut
    // 显示 投诉/已投诉的按钮
    const isShowComplainBtn = isSoldOut && item?.complaintStatus == 3

    const isShowClickCardTip = isSoldOut || isAudit || isAuditFail || !isWithIn7Day || (isIC && (isFindItOrFull || isSoldOut))

    const showBtn = isAuditSuccess && (isLooking || isFindItOrFull) && item?.resumeStatus == 1
    const isShowRealTelBtn = item?.ifViewRealTel && showBtn // 与我沟通过的判断逻辑保持一致 
    const isShowContactBtn = showBtn
    const isShowMoreBtn = item.cooperationSwitch || isShowComplainBtn || item.canComment || isShowRealTelBtn

    return {
      ...initData,
      isShowSoldOutIcon,
      isShowClickCardTip,
      isShowComplainBtn, // 投诉按钮的显示逻辑

      isShowRealTelBtn,
      isShowContactBtn,
      isShowMoreBtn,

    }
  }
}

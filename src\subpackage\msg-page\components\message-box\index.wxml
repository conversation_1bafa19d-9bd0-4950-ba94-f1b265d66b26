<view class="msg-container">
  <view class="msg-item" bind:tap="onClick" data-path="{{affixData.uri}}" data-pType="{{pType}}" data-affixData="{{affixData}}" style="margin-left: {{InfoId == affixData.conversationID ? '-'+ leftRange +'rpx': '' }}" data-item="{{affixData}}" bindtouchstart="onClickS" bindtouchmove="onClickM" bindtouchend="onClickE">
    <image class="topStatus" wx:if="{{affixData.isPinned}}" src="https://staticscdn.zgzpsjz.com/miniprogram/images/wyl/yupao_mini_icon_up_masa_13x.png" />
    <view class="head-img">
      <image mode="aspectFill" class="left-img" src="{{affixData.image || affixData.toUserAvatar || 'https://cdn.yupaowang.com/yupao_mini/yp_mini_zp_head_photo.png'}}" />
      <view class="ddown {{affixData.isNotice?'ddown-isnotice':''}}" wx:if="{{affixData.finallyUnReadNumber > 0 || affixData.show_number > 0}}">
        {{(affixData.finallyUnReadNumber || affixData.show_number)  > 99 ? '99+' : (affixData.finallyUnReadNumber || affixData.show_number)}}
      </view>
      <!-- 置顶广告位和系统消息的未读样式样式模板 -->
      <view  wx:if="{{redDotObjs && redDotObjs[affixData.funcCode]}}" class="ddown {{redDotObjs[affixData.funcCode].number > 9 ? 'ddown-more' : ''}}">
        <text>{{redDotObjs[affixData.funcCode].number > 99 ? '99+' : redDotObjs[affixData.funcCode].number}}</text>
      </view>
    </view>
    <view class="right-con">
      <view class="top-con">
        <view class="title-v">
          <view class="title {{name.length > 5 ? 'title-max':''}}">{{name}}</view>
          <view class="conver-suff" wx:if="{{affixData.conversationSuffix.length > 0}}">{{affixData.conversationSuffix}}</view>
          <block wx:if="{{pType == 'im' && affixData.groupTag.length}}">
            <block wx:for="{{affixData.groupTag}}" wx:key="index">
              <view class="tt-label"  wx:if="{{item == 'MASS'}}">群发炸弹</view>
            </block>
          </block>
          <block wx:if="{{pType == 'im' && affixData.tags.length}}">
            <block wx:for="{{affixData.tags}}" wx:key="index">
              <view class="tt-label">{{item}}</view>
            </block>
          </block>
        </view>
        <view class="time" wx:if="{{isShowTime && affixData.message_time != 0}}">
          {{affixData.message_time}}
        </view>
      </view>
      <view class="down-con">
        <view class="desc-label" wx:if="{{affixData.finallyLabel}}">{{affixData.finallyLabel}}</view>
        <view class="dtop">{{pType == 'im' ? affixData.finallyDesc : affixData.desc}}</view>
        <view class="discardImg-v">
          <image class="discardImg" wx:if="{{affixData.isNotice}}" src="https://staticscdn.zgzpsjz.com/miniprogram/images/wyl/yupao_mini_icon_x85rtrx.png" />
        </view>
      </view>
    </view>
  </view>
  <!-- 这里是左滑按钮部分----start -->
  <view class='isMove' wx:if="{{isShowTime && leftRange && InfoId == affixData.conversationID}}">
    <view class="commonBanner operateTop {{affixData.isPinned && 'operatehaveTop'}}" bind:tap="handleTop" data-timchatid="{{affixData.conversationID}}" data-ispinned="{{affixData.isPinned}}">
      <image class="contentImg" src="{{affixData.isPinned? topedIcon: topIcon}}" />
      <text class="contentText">{{affixData.isPinned? '取消置顶': '置顶'}}</text>
    </view>
    <view class="commonBanner operateDel" bind:tap="handleDel" data-timchatid="{{affixData.conversationID}}">
      <image class="contentImg" src="https://staticscdn.zgzpsjz.com/miniprogram/images/wyl/yupao_mini_icon_xx_sc3trtrx.png" />
      <text class="contentText">删除聊天</text>
    </view>
  </view>
  <!-- 这里是左滑按钮部分----end -->
</view>

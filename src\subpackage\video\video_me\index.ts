/*
 * @Date: 2022-03-01 13:56:55
 * @Description: 我的视频查看页
 */
import { getMenuButtonBoundingClientRect, getSystemInfoSync } from '@/utils/tools/common/index'
import { MapStateToData, connectPage, store } from '@/store/index'
import { refreshMyInfo } from '@/utils/helper/resume/index'

/** 视频的总时长 */
let videoDuration = 0
const { statusBarHeight, windowWidth, windowHeight } = getSystemInfoSync()
const { top: menuButtonTop } = getMenuButtonBoundingClientRect()

const mapStateToData: MapStateToData = (state) => {
  return {
  }
}

Page(
  connectPage(mapStateToData)({
    data: {
      statusBarHeight,
      menuButtonTop,
      /** 视频信息 */
      resumeVideoInfo: null as YModels['POST/resume/v1/resumeVideo/detail']['Res']['data'],
      /** video实例 */
      videoCtx: null,
      /** video标签外层的包裹view高度 */
      videoHeight: 0,
      /** video的object-fir */
      videoObjectFit: 'cover',
      /** video标签的真实高度 */
      videoRealHeight: 0,
      /** 播放状态 */
      playState: false,
      /** 播放时的倒计时 */
      videoTime: '00:00',
      /** 播放时的进度条 */
      progress: 0,
      /** 下载状态： 0 - 未下载，1 - 下载中，2 - 下载成功，3 - 下载失败 */
      downloadState: 0,
      /** 下载进度 */
      downloadProgress: 0,
      /** 下载结果弹窗 */
      showDownloadModal: false,
    },
    /** 重新拍摄 */
    async onClickToShoot() {
      /** 判断是否拥有拍摄权限 */
      await wx.$.u.getAuthorize('scope.camera', '相机')
      wx.$.r.replace({
        path: '/subpackage/video/video_shoot/index',
        query: {
          isVideoMePage: '1',
        },
      })
    },
    /** 下载视频 */
    async onClickDownload() {
      if (this.data.downloadState === 1) {
        return
      }
      this.setData({
        downloadState: 1,
      })
      try {
        await wx.$.u.getAuthorize('scope.writePhotosAlbum')
      } catch (e) {
        wx.$.msg('授权失败')
        return
      }
      const downloadTask = wx.downloadFile({
        url: this.data.resumeVideoInfo.attach.filePath,
        success: (downloadRes) => {
          const { tempFilePath } = downloadRes
          /** 下载成功，保存到相册 */
          wx.saveVideoToPhotosAlbum({
            filePath: tempFilePath,
            success: () => {
              this.setData({
                downloadState: 2,
                showDownloadModal: true,
              })
            },
            fail: (err) => {
              if (err.errMsg === 'saveVideoToPhotosAlbum:fail auth deny') {
                wx.$.msg('授权失败')
              }
              this.setData({
                downloadState: 3,
                showDownloadModal: true,
              })
            },
            complete: () => {
              /** 弹窗三秒后关闭 */
              setTimeout(() => {
                this.setData({
                  showDownloadModal: false,
                })
              }, 3000)
            },
          })
        },
      })
      /** 处理下载进度条 */
      downloadTask.onProgressUpdate((progressRes) => {
        this.setData({
          downloadProgress: progressRes.progress,
        })
      })
    },
    /** 点击返回上一页 */
    onBack() {
      wx.$.r.back()
    },
    onClickVideo() {
      this.data.videoCtx[this.data.playState ? 'pause' : 'play']()
      this.setData({
        playState: !this.data.playState,
      })
    },
    /** 当视频播放中会触发 */
    onTimeUpdate(e) {
      this.setData({
        videoTime: wx.$.u.transformationTime(e.detail.duration - e.detail.currentTime),
        progress: (e.detail.currentTime / e.detail.duration) * 100,
      })
    },
    /** 当视频播放完毕时 */
    onEnded() {
      this.setData({
        playState: false,
        videoTime: wx.$.u.transformationTime(videoDuration),
        progress: 0,
      })
    },
    /** 视频数据加载完成时触发 */
    onLoadVideoData(e) {
      videoDuration = e.detail.duration
      // eslint-disable-next-line prefer-const
      let { videoObjectFit, videoHeight, videoRealHeight } = this.data
      if (e.detail.width > e.detail.height) {
        // 说明是横屏
        videoObjectFit = 'contain'
        videoRealHeight = videoHeight
      } else {
        videoRealHeight = windowWidth * (e.detail.height / e.detail.width)
      }
      this.setData({
        videoTime: wx.$.u.transformationTime(e.detail.duration),
        videoObjectFit,
        videoRealHeight,
      })
      if (!this.data.playState) {
        this.onClickVideo()
      }
    },
    /** 获取我的面试视频数据 */
    async requestResumeVideo() {
      wx.$.javafetch['POST/resume/v1/resumeVideo/detail']().then((res) => {
        // console.log('res', res)
        if (!res.error) {
          this.setData({
            resumeVideoInfo: res.data,
          })
          this.initLayout()
        }
      })
    },
    /** 点击进度条触发 */
    onChangeProgress(e) {
      const { value } = e.detail
      const second = videoDuration * (value / 100)
      this.setData({
        videoTime: wx.$.u.transformationTime(second),
        progress: value,
      })
      this.data.videoCtx.seek(second)
    },
    /** 初始化布局信息 */
    initLayout() {
      const videoCtx = wx.createVideoContext('video')
      wx.createSelectorQuery()
        .select('#video-foot')
        .boundingClientRect((rect) => {
          if (rect) {
            this.setData({
              videoCtx,
              videoHeight: windowHeight - rect.height - (statusBarHeight || 0),
            })
          }
        })
        .exec()
    },
    /** 删除视频 */
    onDelete() {
      const { resumeUuid } = store.getState().storage.myResumeDetails
      const { resumeVideoInfo } = this.data
      wx.$.loading('删除中...')
      wx.$.javafetch['POST/resume/v3/video/delete']({
        resumeUuid,
        uuid: resumeVideoInfo.uuid,
      }).then((res) => {
        wx.hideLoading()
        if (res.code === 0) {
          this.onSuccess()
        } else {
          wx.$.msg(res.message)
        }
      }).catch(() => wx.hideLoading())
    },

    /** 操作成功的回调 */
    onSuccess(msg = '删除成功') {
      refreshMyInfo('refresh', { isStore: true })
      wx.$.msg(msg, 1500, true).then(() => {
        wx.$.r.back()
      })
    },
    onLoad() {
      // 每次进页面都要重置该值，防止缓存
      videoDuration = 0
      this.requestResumeVideo()
    },
  }),
)

import { actions, dispatch, storage, store } from '@/store/index'
import { toJSON } from '@/utils/tools/formatter/index'
import { dateTransformation } from './util'
import { peerReadPtypes, peerReadTypes } from './type.d'
import { handleTixingCallback } from './messages'
import { reGetImData } from './im-method'
import { defaultState } from '@/store/model/globalData/message'

/**
 *群组数据重新排序
 * @param msgGroups: 群组数据
 * @param newGroup : 新接收到的群组数据
*/
export const sortMsgGroup = (msgGroups, newGroup) => {
  const nMsgGroups = [...(msgGroups || [])].map((item, index) => ({ ...item, __index: index }))
  const idx = nMsgGroups.findIndex((item) => item.conversationID == newGroup.conversationID)
  if (idx >= 0) {
    nMsgGroups[idx] = newGroup
    // nMsgGroups.splice(idx, 1)
  } else {
    nMsgGroups.push(newGroup)
  }
  // idx = nMsgGroups.findIndex((item) => item.timestamp < newGroup.timestamp)
  // if (idx > 0) {
  //   nMsgGroups.splice(idx, 0, newGroup)
  // } else {
  //   const last = nMsgGroups[nMsgGroups.length - 1]
  //   if (last && last?.timestamp > newGroup.timestamp) {
  //     nMsgGroups.push(newGroup)
  //   } else {
  //     nMsgGroups.unshift(newGroup)
  //   }
  // }
  return nMsgGroups.sort((a, b) => {
    const timeDiff = b.timestamp - a.timestamp
    if (timeDiff != 0) return timeDiff
    return a.__index - b.__index
  })
}

/**
 * 处理临时数组排序
 *  */
export const sortImChatList = (myMsgGroupOjb, imChatList, newchat) => {
  let topList = []
  let list = []
  imChatList.forEach((chat) => {
    const item = myMsgGroupOjb[chat.conversationID]
    if (item && item.isPinned) {
      topList.push(chat)
    } else {
      list.push(chat)
    }
  })
  const nitem = myMsgGroupOjb[newchat.conversationID]
  if (nitem && nitem.isPinned) {
    topList = sortMsgGroup(topList, newchat)
  } else {
    list = sortMsgGroup(list, newchat)
  }
  return topList.concat(list)
}

/** 组装初步会话对象 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export const assConverPre = (item) => {
  const { markList, conversationGroupList, lastMessage } = item || {}
  const groupTag = []
  if (markList && markList.length) {
    const mark = markList[0]
    // 将标记转换为 33 位的二进制字符串，并在不足的地方补0 并反转
    const binaryMark = mark.toString(2).padStart(33, '0').split('').reverse()
    const hasMass = binaryMark[32] === '1' // 判断是否有 群发炸弹 标记
    hasMass && groupTag.push('MASS')
  }
  const { txt } = msgTypeTransformation(item.lastMessage || {})

  let finallyLabel = ''
  const { isRevoked, fromAccount, isPeerRead, type, payload } = lastMessage || {}
  if (!isRevoked) {
    if (conversationGroupList.includes('NEW_CONVERSATION')) {
      finallyLabel = '[新招呼]'
    } else {
      const { data } = payload || {}
      let dataObj: any = {}
      if (data) {
        dataObj = toJSON(data)
      }
      const { type: ptype } = dataObj || {}
      const userImAcc = storage.getItemSync('userImAcc')
      if (fromAccount == userImAcc && (peerReadTypes.includes(type) || peerReadPtypes.includes(`${ptype}`))) {
        finallyLabel = isPeerRead ? '[已读]' : '[送达]'
      }
    }
  }
  const { lastTime } = lastMessage || {}
  return {
    finallyUnReadNumber: item.unreadCount,
    finallyDesc: txt,
    message_time: dateTransformation(lastTime),
    timestamp: Number(lastTime),
    isPinned: item.isPinned,
    groupTag,
    isNotice: item.messageRemindType == 'AcceptNotNotify', // 判断是否免打扰 true:免打扰
    conversationID: item.conversationID,
    finallyLabel,
    conversationGroupList,
  }
}

/**
 * @function 消息类型是否支持-判断
 * */
// eslint-disable-next-line sonarjs/cognitive-complexity
export const msgTypeTransformation = (lastMsg) => {
  const msg = { ...(lastMsg || {}) }
  const { fromAccount, from, isRevoked, payload } = msg || {}
  const { userImAcc, userChooseRole } = store.getState().storage
  const isSelf = fromAccount == userImAcc || from == userImAcc
  const { data, text, description } = payload || {}
  let dataObj = toJSON(data || {})
  if (dataObj && dataObj.data) {
    dataObj = toJSON(dataObj.data || {})
  }
  const { type, content } = dataObj || {}
  const { subType, text: msgText, title, houseNumber, cityName, countyName } = content || {}
  // 如果消息被撤回了，显示当前人撤回了消息
  if (isRevoked) {
    return { txt: `${isSelf ? '你' : '对方'}撤回了一条消息` }
  }
  // 提醒消息
  if (type == '390.1') {
    let customExts: any = {}
    let content = []
    if (msg.payload) {
      const payload = toJSON(msg.payload)
      if (wx.$.u.typeOf(payload.ext) == 'array') {
        payload.ext.forEach(item => {
          customExts = { ...customExts, ...item }
        })
      } else {
        customExts = payload.ext
      }
      content = handleTixingCallback(dataObj, isSelf)
    }
    return { txt: content.map(item => item.text).filter(item => item).join('') || '消息内容' }
  }
  if (isSelf && type == '610.1') { // 交换电话
    if (subType == 1) {
      return { txt: '请求交换电话已发送' }
    }
    if (subType == 2) {
      return { txt: '你已经成功拒绝了对方交换电话的请求' }
    }
  }
  if (isSelf && type == '660.1') { // 交换微信
    if (subType == 1) {
      return { txt: '请求交换微信已发送' }
    }
    if (subType == 2) {
      return { txt: '你已经成功拒绝了对方交换微信的请求' }
    }
  }

  if (isSelf && type == '710.1') { // 附件简历
    if (subType == 1) {
      return { txt: userChooseRole == 2 ? '附件简历请求已发送' : '请求附件简历已发送' }
    }
    if (subType == 2) {
      return { txt: userChooseRole == 2 ? '你已拒绝向对方发送附件简历' : '你已经成功拒绝了对方发送附件简历的请求' }
    }
  }
  if (!isSelf && type == '710.1' && subType == 3) {
    if (userChooseRole == 1) {
      return { txt: title || msgText }
    }
    if (userChooseRole == 2) {
      return { txt: title || msgText }
    }
  }

  if (userChooseRole == 1 && type == '910.1') {
    if (subType == 1) {
      return { txt: '已向牛人发起面试邀请' }
    }
    if (subType == 4) {
      return { txt: '牛人已达面试现场' }
    }
  }

  if (userChooseRole == 2 && type == '910.1' && subType == 1) {
    const { faceInviteInfo } = content || {}
    const { employerName, type: fiiType } = faceInviteInfo || {}
    return { txt: `${employerName}邀请你参加${fiiType == 1 ? '线下面试' : '线上面试'}，请及时前往确认` }
  }

  if (type == '910.1' && ['2', '3', '4', '5'].includes(`${subType}`)) {
    return { txt: title }
  }

  const defaultArr = ['TIMTextElem', 'TIMSoundElem', 'TIMImageElem', 'TIMCustomElem', 'TIMMapElem']
  if (defaultArr.includes(msg.type)) {
    const { isSdkVer240 } = store.getState().message
    const types = ['6', '7', '12']
    if (!isSdkVer240) {
      types.push('8')
    }

    if (types.includes(`${type}`)) {
      return { txt: '[暂不支持此消息类型，请前往客户端查看]' }
    }
    if (msg.type == 'TIMCustomElem' || msg.type == 'TIMMapElem') {
      if (type == 1) {
        return { txt: '[职位]' }
      }
      if (type == 2) {
        return { txt: '[简历]' }
      }

      if (type == 4 || type == 5) {
        return { txt: '[电子合同]' }
      }

      if (type == 8 && isSdkVer240 && title) {
        return { txt: `[位置]${cityName || ''}${countyName || ''}${title}${houseNumber || ''}` }
      }

      if (text || description || msgText) {
        return { txt: text || description || msgText }
      }
    }
    let messageForShow = ''
    if (msg.type == 'TIMImageElem') {
      messageForShow = '[图片]'
    } else if (msg.type == 'TIMSoundElem') {
      messageForShow = '[语音]'
    } else if (msg.type == 'TIMTextElem' && text) {
      messageForShow = text
    }
    if (messageForShow) {
      return { txt: messageForShow }
    }
  }
  if (!msg.type && !fromAccount) {
    return { txt: '' }
  }
  return { txt: '[暂不支持此消息类型，请前往客户端查看]' }
}

/**
 * @description 获取会话列表数据
 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export const fetchTenCentData = async () => {
  const { userState } = store.getState().storage
  const { isSessionList, imGlobalSwitch, systemSingleChat } = store.getState().message
  dispatch(actions.messageActions.setState({ isSessionList: true }))
  if (!userState.login || !ENV_IS_WEAPP || isSessionList) {
    // 没有登录直接返回空对象
    return {}
  }
  let nImGlobalSwitch = imGlobalSwitch ? { ...imGlobalSwitch } : null
  if (!imGlobalSwitch) {
    const res = await wx.$.javafetch['POST/reach/v2/im/config/query']()
    const { code, data } = res || {}
    if (code == 0) {
      const { systemAccountList, toolbarIcon, maxMessageWordCountLimit, b2cSayHelloAgainContent } = data || {}
      nImGlobalSwitch = { accountList: systemAccountList, toolbarIcon, b2cSayHelloAgainContent, maxMessageWordCountLimit: maxMessageWordCountLimit || 300 }
      dispatch(actions.messageActions.setState({ imGlobalSwitch: nImGlobalSwitch }))
    }
  }

  // 系统消息单聊ID
  let sysTemChatId = systemSingleChat?.accountUserId
  // 单聊系统消息相关信息存储
  const scSystemMsg: any = {}
  if (!sysTemChatId) {
    const nChat = getSystemMsgByImgs(nImGlobalSwitch)
    if (nChat) {
      scSystemMsg.systemSingleChat = nChat
      sysTemChatId = nChat.accountUserId
    }
  } else {
    scSystemMsg.systemSingleChat = systemSingleChat
  }
  // 置顶群组会话数据
  const newTopCurrentList = []
  // 正常群组会话数据
  const newCurrentList = []
  // 正常群组会话对象
  const newCurrentObjs = {}
  const all: any = {}
  if (!wx.$.tim && userState.login) {
    await wx.$.l.timLogin()
  }
  try {
    const { ctab, poptab } = storage.getItemSync('imtyps') || {}
    let options: any = {}
    if (ctab != 'ALL') {
      options.groupName = ctab
      if (ctab == 'OTHER') {
        if (poptab != 'UNREAD') {
          options.groupName = poptab
        } else {
          options = {}
        }
      }
    }

    const imResponse = await wx.$.tim.getConversationList(options)
    let nConversationList: Array<any> = wx.$.u.deepClone(imResponse.data.conversationList)
    if (options.groupName == 'ALREADY_EXCHANGE_TEL') {
      const wxImResponese = await wx.$.tim.getConversationList({ groupName: 'ALREADY_EXCHANGE_WEIXIN' })
      nConversationList = nConversationList.concat(wx.$.u.deepClone(wxImResponese.data.conversationList))
      const fileImResponese = await wx.$.tim.getConversationList({ groupName: 'ALREADY_EXCHANGE_RESUME_FILE' })
      nConversationList = nConversationList.concat(wx.$.u.deepClone(fileImResponese.data.conversationList))
    }
    if (nConversationList && nConversationList.length > 0) {
      nConversationList.forEach((item: any) => {
        let isOk = newTopCurrentList.findIndex(it => it.conversationID == item.conversationID)
        if (isOk < 0) {
          isOk = newCurrentList.findIndex(it => it.conversationID == item.conversationID)
        }
        if (isOk < 0) {
          const nItem = wx.$.u.deepClone(item)
          if (!(ctab == 'OTHER' && poptab == 'UNREAD' && nItem.unreadCount == 0)) {
            let sortItem = {}
            const { lastMessage } = nItem || {}
            const { lastTime } = lastMessage || {}
            if (sysTemChatId && nItem.conversationID == `C2C${sysTemChatId}`) {
              if (ctab != 'ALL') {
                return
              }
              // 组装系统消息
              const nI = setSystemMsgByImChatMsg(scSystemMsg.systemSingleChat, nItem)
              scSystemMsg.systemSingleChat = { ...systemSingleChat, ...nI }
              sortItem = { conversationID: nItem.conversationID, timestamp: !lastTime ? 0 : Number(lastTime), type: 'sys' }
            } else {
              // 用groupId去请求后端接口
              const id = nItem.conversationID
              newCurrentObjs[id] = assConverPre(nItem)
              sortItem = { conversationID: id, timestamp: !lastTime ? 0 : Number(lastTime) }
            }
            if (nItem.isPinned) {
              newTopCurrentList.push(sortItem)
            } else {
              newCurrentList.push(sortItem)
            }
          }
        }
      })
      all.myTenMsgGroupOjb = newCurrentObjs
    } else {
      wx.showLoading({ title: '加载中' })
      setTimeout(() => {
        wx.hideLoading()
        reGetImData()
      }, 800)
    }

    all.myMsgGroup = newCurrentList
    all.myTopMsgGroup = newTopCurrentList
    await dispatch(actions.messageActions.setState(all))
    dispatch(actions.messageActions.setState({ isSessionList: false, ...scSystemMsg, isRequestIm: true }))
  } catch (err) {
    dispatch(actions.messageActions.setState({ isSessionList: false, ...scSystemMsg, isRequestIm: true }))
    console.warn('getGroupList error:', err) // 获取群组列表失败的相关信息
  }

  return { ...defaultState }
}

/** 通过IM返回的单聊信息组装系统消息信息 */
export const setSystemMsgByImChatMsg = (systemSingleChat, chatMsg: any = {}) => {
  const { lastMessage } = chatMsg || {}
  const { lastTime, payload } = lastMessage || {}

  const nSystemSingleChat: any = {
    ...systemSingleChat,
    timestamp: lastTime || 0,
  }
  nSystemSingleChat.message_time = dateTransformation(lastTime)
  nSystemSingleChat.show_number = chatMsg.unreadCount || 0
  nSystemSingleChat.conversationID = chatMsg.conversationID
  if (payload && payload.data) {
    const customExt: any = toJSON(payload.data) || {}
    const content: any = toJSON(customExt.content) || {}
    const ext = toJSON(customExt.ext || {})
    if (customExt.type == 15 && ext && ext.length > 0) {
      const contentArr = content.content?.split('{message_replace}') || []
      contentArr.forEach((item, i) => {
        nSystemSingleChat.desc = `${nSystemSingleChat.desc}${item}${ext[i]?.content}`
      })
    } else {
      nSystemSingleChat.desc = content.content
    }
  }
  return nSystemSingleChat
}

/** 通过 imGlobalSwitch获取系统消息基本信息 */
export const getSystemMsgByImgs = (imGlobalSwitch) => {
  if (imGlobalSwitch && imGlobalSwitch.accountList?.length > 0) {
    let systemMsg: any = {}
    imGlobalSwitch.accountList.forEach((item) => {
      if (item.type == 3) {
        systemMsg = { ...item }
      }
    })
    return {
      accountUserId: systemMsg.accountUserId,
      conversationID: `C2C${systemMsg.accountUserId}`,
      name: systemMsg.nickName,
      image: systemMsg.faceUrl,
      title: systemMsg.nickName,
      uri: '/subpackage/member/system_info/index',
      jump_type: 1,
      show_number: 0,
      page_unique_index: 'system_message',
      desc: '系统消息',
    }
  }
  return {
    accountUserId: '',
    conversationID: '',
    image: 'https://staticscdn.zgzpsjz.com/images/content/********/****************.png',
    title: '系统消息',
    uri: '/subpackage/member/system_info/index',
    jump_type: 1,
    show_number: 0,
    page_unique_index: 'system_message',
    desc: '系统消息',
  }
}

// 没有发过消息的群组是不存在会话，或者超过一定时间会话删除了，重新发送消息消息列表添加会话
export const setMyMsgGroupByGroup = (payload) => {
  const { conversationID, payUserImId, createdAt } = payload || {}
  const { myMsgGroup, myMsgGroupOjb } = store.getState().message
  if (!conversationID || myMsgGroup.includes(conversationID)) {
    return
  }
  const nPayload = { ...payload }
  let time = Number(createdAt)
  if (time) {
    if (`${createdAt}`.length === 10) {
      time *= 1000
    }
    const sortItem = { conversationID, timestamp: time }
    const nMyMsgGroup = sortMsgGroup(myMsgGroup, sortItem)
    dispatch(actions.messageActions.setState({ myMsgGroup: nMyMsgGroup }))
    const message_time = dateTransformation(time)
    const { userImAcc } = store.getState().storage
    let timStr = `${message_time} 你向对方发起了沟通`
    if (userImAcc && payUserImId !== userImAcc) {
      timStr = `${message_time} 对方向你发起了沟通`
    }
    nPayload.message_time = message_time
    nPayload.finallyDesc = timStr
  }
  dispatch(actions.messageActions.setState({ myMsgGroupOjb: { ...myMsgGroupOjb, [conversationID]: nPayload } }))
}

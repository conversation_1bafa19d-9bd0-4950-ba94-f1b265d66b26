/*
 * @Date: 2021-12-15 15:52:08
 * @Description: 路由初始化
 */

import YpRouter from '../../lib/YpRouter/index'
import { interceptList } from './utils'
import { store } from '@/store/index'
import { isIos } from '../tools/validator/index'
import { parsePath } from '../tools/common/index'
import { clearLoginCall, toLogin } from '../helper/common/toLogin'

const router = new YpRouter({
  tabBarPages: ['/pages/index/index', '/pages/resume/index', '/pages/ucenter/index', '/pages/msg-page/index'],
  immediate: true,
  beforeEach(to, from, url, next) {
    // 如果没有登录并且路径在拦截名单中
    if (!store.getState().storage.userState.login) {
      let returnFlag = false
      let toPath = ''
      for (let i = 0; i < interceptList.length; i += 1) {
        // to会出现xxx?xx=xxx
        if (to?.startsWith(interceptList[i]) && to != '') {
          returnFlag = true
          toPath = interceptList[i]
          break
        }
      }
      if (returnFlag) {
        toLogin().then(() => {
          if (toPath) {
            if (toPath === '/pages/msg-page/index') {
              wx.$.r.push({
                path: toPath,
              })
            } else {
              wx.$.r.replace({
                path: to || toPath,
              })
            }
          }
        })
        return
      }
    }
    const clearLoginPath = [
      '/subpackage/userauth/auth/index',
      '/subpackage/userauth/tel/index',
    ]
    if (!clearLoginPath.some(item => to?.startsWith(item))
      && !(from?.route == 'subpackage/userauth/auth/index' || to.indexOf('subpackage/web-view/index') >= 0)) {
      clearLoginCall()
    }
    /** ios跳积分充值页重定向到获取积分页（处理通用弹窗配置积分充值跳转） */
    if (isIos() && to.indexOf('subpackage/recharge/recharge/index') >= 0) {
      next({ path: '/subpackage/member/getintegral/index' })
      return
    }
    /** 处理重复跳登录页面 */
    const currentPage = wx.$?.router && wx.$.r.getCurrentPage()
    if (currentPage && currentPage.route === 'subpackage/userauth/auth/index' && to.indexOf(currentPage.route) != -1) {
      return
    }
    if (this.tabBarPages.find(item => to && to.startsWith(item))) {
      const { path, query } = parsePath(to)
      // 判断query是否有值 且是否为空对象
      if (query && Object.keys(query).length > 0) {
        next({ path, params: query })
        return
      }
    }
    next()
  },
})

export default router

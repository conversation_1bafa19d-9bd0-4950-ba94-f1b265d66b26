<view class="out-v {{msgInfo.isSelf ? '' : 'out-start'}}">
  <msg-head-img wx:if="{{!msgInfo.isSelf && msgInfo.isAvatar}}" msgInfo="{{msgInfo}}" />
  <msg-state wx:if="{{msgInfo.isSelf}}" style="height: 100%;" margin="right" msgInfo="{{msgInfo}}" status="{{msgInfo.status}}"/>
  <view class="resumes-content" catch:tap="onClick">
    <view class="user-info">
      <view class="user-info-o">
        <view class="user-info-name">{{info.user_name}}<view class="user-info-auth" wx:if="{{info.auth == 2}}">已实名</view></view>
        <view class="user-info-hometown" wx:if="{{info.address}}">期望项目地点：{{info.address}}</view>
      </view>
    </view>
    <view class="classly-p" wx:if="{{classNames.length > 0}}">
      <view class="classly-content">
        <view class="classly-txt" wx:for="{{classNames}}" wx:for-index="idx" wx:for-item="itemName" wx:key="idx">
          {{itemName}}
        </view>
      </view>
    </view>
    <view class="detail-txt" wx:if="{{info.detail}}">{{info.detail}}</view>
  </view>
  <msg-head-img wx:if="{{msgInfo.isSelf && msgInfo.isAvatar}}" msgInfo="{{msgInfo}}" />
</view>
/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 普通招工详情，底部tabbar
 */

import { actions, dispatch, store } from '@/store/index'

Component({
  properties: {
    greetingList: {
      type: Array,
      value: [],
    },
  },
  methods: {
    onAdd() {
      wx.$.nav.push(
        '/subpackage/tim/comWordsOper/index',
        {},
        (data) => {
          const { content } = data || {}
          const { greetingList } = store.getState().comwords
          dispatch(actions.comwordsActions.setState({ greetingList: [content, ...(greetingList || [])] }))
        },
      )
    },
    onMan() {
      wx.$.r.push({ path: '/subpackage/tim/comWordsMan/index' })
    },
    onComWordsClick(e) {
      const { value } = e.detail
      this.triggerEvent('comWordsClick', { value })
    },
    /** 禁止滚动操作 */
    onDisableMove() { },
  },
})

/*
 * @Date: 2022-01-28 16:17:45
 * @Description: 业务通用方法
 */

import { app } from '@/config/index'
import { dispatch, actions, store, storage, messageQueue } from '@/store/index'

import miniConfig from '@/miniConfig/index'
import { common } from '@/utils/tools/index'
import { transFromRuntime } from '@/utils/request/utils'
import { login } from '@/utils/helper/index'
import { dealDialogByApi } from '../dialog/index'
import { getArrayVal } from '@/utils/tools/validator/index'
import { getPageCode, PageCodeType } from '../resourceBit/index'
import { REQUEST_VERSION } from '@/config/app'

/**
 * 拼接弹窗的提示信息:spliceContent
 * @param {String} text 需要拆分的文本 eg: 每天只能免费发布3条招工信息!是否消耗1积分继续发布?
 * @param {Array} rules 拆分文本的数组配置 eg: [{"length":2,"start":8,"type":"color","value":"#dd4b39"},{"length":3,"start":19,"type":"color","value":"#dd4b39"}]
 * @returns {Array}
 */
export function initRuleTipsInfo(text: string, rules: Array<any>) {
  const spliceContent = []
  if (!wx.$.u.isArrayVal(rules)) {
    return [{ text }]
  }
  for (let i = 0; i < rules?.length; i += 1) {
    const start = Number(rules[i]?.start)
    if (i === 0) {
      spliceContent.push({ text: text.substring(i, start) })
    } else {
      spliceContent.push({ text: text.substring(Number(rules[i - 1].start) + rules[i - 1].length, start) })
    }
    spliceContent.push({
      text: text.substring(start, start + rules[i].length),
      color: rules[i].value || rules[i].color,
    })
    if (i === rules.length - 1) {
      spliceContent.push({ text: text.substring(start + rules[i].length) })
    }
  }
  return spliceContent
}

/** 工种数组 */
type IArrClassIds = {
  /** 工种id */
  id: string | number,
  /** 行业id */
  hids: string[] | number[],
  /** 工种名 */
  name: string,
  /** 1.代表订单类工种， 2.代表招聘类工种 */
  mode: 1 | 2,
}[]
/**
 * 根据传参 储存筛选器对应的工种，城市model
 * @params areaId string
 * @params classifyId string
 * @params type 'recruit' | 'resume' | 'factoryResume'
 * */
export const nwSaveFilterStoreByIds = async (
  /** 地址id */
  areaId: string,
  /** 工种数组 */
  arrClassIds: IArrClassIds | any,
  /** 类型 */
  type: 'recruit' | 'resume' | 'factoryResume' = 'recruit',
  sType = '',
) => {
  // 保存城市用做筛选器
  if (areaId) {
    const ids = String(areaId).split(',')
    if (ids && ids.length) {
      const citys = (await wx.$.l.getAreaSearches(ids)).map(({ current }) => ({ ...current, children: [] }))
      let city = null
      if (wx.$.u.isArrayVal(citys, 2)) {
        const { pid } = citys[0] || {} as any
        city = (await wx.$.l.getAreaById(pid)).current
        delete city.children
      } else if (wx.$.u.isArrayVal(citys)) {
        city = { ...(citys[0] || {}) }
      }

      if (city) {
        const { userLocationCity } = store.getState().storage
        let val = { ...userLocationCity, ...city }
        const nVal = { ...city, citys, cityLen: citys.length }
        if (type == 'resume') {
          val.resumeCityObj = nVal
        } else if (type == 'recruit') {
          val.recruitCityObj = nVal
        }
        if (sType) {
          val = { ...userLocationCity, [sType]: nVal }
        }
        // 储存选择城市
        dispatch(actions.storageActions.setItem({ key: 'userLocationCity', value: val }))
      }
    }
  }
  // 保存工种用做筛选器
  if (wx.$.u.isArrayVal(arrClassIds)) {
    const { value: classifies } = await wx.$.l.nwtransformClassifyValueByIds(arrClassIds, { isSaveHid: true })
    // 储存选择工种
    await dispatch(actions.storageActions.setItem({ key: `${type}Classify`, value: classifies }))
    // 设置工种文案
    const firstWord = type.slice(0, 1).toUpperCase()
    const nType = firstWord + type.slice(1)
    await dispatch(actions.classActions[`set${nType}Classify`](classifies))
  }
}

export enum REPORT_CONFIG_TYPE {
  TRADER = 1100,
  WORKER = 1101,
  IM = 1102,
}

/** 跳转到投诉页还是投诉分类页
 * @params id string 信息id
 * @params projectId string 1100-招工投诉,1101-找活投诉,1102-举报投诉,1105-联系前职位,1106-联系前简历
 * @params targetUserId string 被投诉人id
 * @params targetId string im场景消息id
 * @params complaintSource string 信息id
 */
export const isToComplaintOrClassify = async (query) => {
  const { id, projectId, targetUserId, targetId, complaintSource } = query
  // new投诉页（h5）
  const h5Query = encodeURIComponent(`?infoId=${id}&projectId=${projectId}&${targetUserId ? `targetUserId=${targetUserId}` : ''}${targetId ? `&targetId=${targetId}` : ''}${complaintSource ? `&complaintSource=${complaintSource}` : ''}`)
  wx.$.r.push({
    path: '/subpackage/web-view/index',
    query: {
      url: `/complaint-report/index${h5Query}`,
      isLogin: true,
    },
  })
}

/** 投诉，投诉分类页面会携带 type，type 要被分为招工和找活，用于传递接口 */
export function judgeWorkerOrTraderByType(type) {
  // 是工人吗？
  return ['resume', 'top_resume', 'auto_refresh', 'resume_refresh'].includes(type)
}

/**
 * @description 获取TDk数据
 */
export const getTdkData = async (params) => {
  try {
    const { data, head } = await wx.$.fetch['POST/jlist/seo/getTdkForList'](params, {
      showLoading: false,
      hideMsg: true,
      hideError: true,
    })
    if (head.code !== 200) {
      return {}
    }
    return data
  } catch (error) {
    return {}
  }
}

/**
 * @name 搜索词的埋点上报
 * @param {string} eventName 事件名称
 * @param {Data} data 找活详情数据
 * @param {object} reportData 自定义事件数据
 */
export const uploadCommonSearchWordsStatisticsData = (eventName, type = '1', list = [], identification = '', reportData = {}) => {
  let reportParams: any = {
    source_id: type,
    keywords_list: list,
  }

  // 点击埋点
  if (identification == 'click') {
    reportParams = {
      ...reportParams,
      ...reportData,
    }
  }
  wx.$.collectEvent.event(eventName, reportParams)
  return reportParams
}

/**
 * @name 手机号快捷登录的埋点上报
 * @param {string} eventName 事件名称
 * @param {object} reportData 自定义事件数据
 */
export const uploadCommonWxPhoneAuthData = (eventName, reportData = {}) => {
  const reportParams = { ...reportData }
  wx.$.collectEvent.event(eventName, reportParams)
}

// 跳转h5页面,header信息和token
export const getH5OfHeader = async ({ isHeader, isSession, isBottom, isUid, isOpenId = 1, refid = '', track_seed = '' }) => {
  const platformList = {
    /** 微信小程序 */
    weapp: 'wx_mini',
    /** 百度小程序 */
    swan: 'baidu_mini',
    /** 字节小程序 */
    tt: 'bd_mini',
  }
  let openid = storage.getItemSync('loginAuthData')?.openid
  let unionid = storage.getItemSync('loginAuthData')?.unionid
  /** 没有openid就重新获取 */
  if (!openid && ENV_IS_WEAPP) {
    const result: any = await login.getWechatAuthInfo()
    openid = result?.openid
    unionid = result?.unionid
  }
  const userState = storage.getItemSync('userState')
  const { token } = store.getState().user.webTokenData
  const { system, systemVersion } = common.getSystemName()
  // 获取手机系统信息
  const { safeArea, screenHeight } = wx.getSystemInfoSync()
  const iosSABottom = Number(screenHeight - (safeArea?.bottom || 0))
  const trackSeed = track_seed || storage.getItemSync('track_seed_share')
  const refId = refid || storage.getItemSync('sourceCode')
  const headers = {
    os: system,
    business: 'YPZP',
    runtime: transFromRuntime(),
    packagename: miniConfig.appid,
    // channel: 'dev',
    osversion: common.getSystemInfoSync().version,
    runtimeversion: ENV_IS_SWAN ? app.REQUEST_VERSION : systemVersion,
    packageversion: app.REQUEST_VERSION,
    systemversion: systemVersion,
    appversion: app.REQUEST_VERSION,
    minipackage: miniConfig.token,
    system,
    /** wx_mini: 微信小程序 bd_mini: 字节小程序 baidu_mini: 百度小程序 qq_mini: QQ小程序 */
    platform: platformList[ENV_MINI_TYPE],
    openid,
    unionid,
  }
  /**  */
  let srcStr = ''
  if (isHeader) {
    srcStr += `headers=${encodeURIComponent(JSON.stringify(headers))}&`
  }
  if (isSession) {
    srcStr += `session=${token || userState.token}&`
  }
  if (isBottom) {
    srcStr += `bottom=${iosSABottom}&`
  }
  if (isUid) {
    srcStr += `uid=${userState.userId}&`
  }
  if (isOpenId && ENV_IS_WEAPP) {
    srcStr += `openId=${openid}&`
  }
  if (trackSeed) {
    srcStr += `trackSeed=${trackSeed}&`
  }
  if (refId) {
    srcStr += `refid=${refId}&`
  }
  if (srcStr) {
    srcStr = srcStr.substring(0, srcStr.length - 1)
  }

  return srcStr
}

/**
 * @description: 获取基础配置信息
 */
export const getBaseConfig = (() => {
  let promiseState
  /**
   * @param {string} key 配置key
   * @param {boolean} isUpdate 是否更新配置
   * @param {boolean} isAll 是否获取所有配置
   */
  return async (options) => {
    const { key, isUpdate, isAll } = options
    try {
      // 如果有正在请求的配置信息，等待请求完成
      if (promiseState) {
        await messageQueue((state) => Object.keys(state.config.baseConfig).length > 0)
      }
      let { baseConfig } = store.getState().config || {}
      if (isUpdate || Object.keys(baseConfig).length === 0) {
        // 防止重复请求
        promiseState = true
        const res = await wx.$.javafetch['POST/account/v1/setting/config']()
        const { data } = res || {}
        promiseState = false
        if (!data) {
          return isAll ? {} : ''
        }
        baseConfig = { ...baseConfig, ...data }
        dispatch(actions.configActions.setState({ baseConfig }))
      }
      return isAll ? baseConfig : baseConfig[key]
    } catch (err) {
      return isAll ? {} : ''
    } finally {
      promiseState = false
      dispatch(actions.configActions.setState({ isReqConfig: true }))
    }
  }
})()

/**
 * @description: 加群以及关注公众号埋点上报 （调用此方法需要在页面卸载和隐藏时重新设置埋点公共属性user_unique_id）
 * @param eventName 上报事件名
 * @param data 自定义数据
 * @returns
 */
export const attentionReport = (eventName, data) => {
  if (!data) {
    return
  }
  // 字段相关参考埋点文档
  // https://w3nu1yaadv.feishu.cn/sheets/shtcnOULYD9cCQ1g6syPwuanZ4c
  const { loginAuthData } = store.getState().storage
  const statistics = {
    ...data,
    openid: loginAuthData?.openid,
    unionid: loginAuthData?.unionid,
  }
  eventName === 'entryAddFriendPage' && delete statistics.official_account_name
  wx.$.collectEvent.config({ user_unique_id: data.uid || '' })
  wx.$.collectEvent.event(eventName, statistics)
}

/** 判断 主站/物流/工厂 */
export function judageOrigin(val, storegeName, type?, extra?) {
  if (type == 1) {
    const numberObj = storage.getItemSync(storegeName)
    switch (Number(val)) {
      case 1:
        return numberObj.index
      case 2:
        return numberObj.factory
      case 3:
        return numberObj.logitics
      default:
        return numberObj.index
    }
  }

  if (type == 2) {
    switch (Number(val)) {
      case 1:
        return 'index'
      case 2:
        return 'factory'
      case 3:
        return 'logitics'
      default:
        return 'index'
    }
  }

  if (type == 3) {
    switch (Number(val)) {
      case 1:
        return {
          ...storage.getItemSync(storegeName),
          index: extra + 1,
        }
      case 2:
        return {
          ...storage.getItemSync(storegeName),
          factory: extra + 1,
        }
      case 3:
        return {
          ...storage.getItemSync(storegeName),
          logitics: extra + 1,
        }
      default:
        return {
          ...storage.getItemSync(storegeName),
          index: extra + 1,
        }
    }
  }

  switch (Number(val)) {
    case 1:
      return '主站'
    case 2:
      return '工厂'
    case 3:
      return '物流'
    default:
      return '主站'
  }
}

/**
 * 原需求中对“客人态助力”流程进行了toast提示，因沟通问题进行了优化
 * 现流程调整为客人态点击助力，进行微信授权注册后，后端返“用户注册”状态参数，小程序当前页面进行相应toast提示
 * ①，如当前用户为新用户，则完成授权后在当前页面toast提示：助力成功，您已成功助力朋友！
 * ②、如当前用户为老用户，则完成授权后在当前页面toast提示：助力失败，您不是鱼泡网新用户，无法为您的朋友完成助力哦！
 */
export const toastZhuliNewUser = () => {
  const nw = storage.getItemSync('newUserToastZhuli')
  if (nw && nw.video_dialog == 1) {
    if (nw.isNewUser == 1) {
      // 新用户
      wx.$.msg('助力成功，您已成功助力朋友！')
      storage.removeSync('newUserToastZhuli')
    } else if (nw.isNewUser == 2) {
      // 老用户
      wx.$.msg('助力失败，您不是鱼泡网新用户，无法为您的朋友完成助力哦！')
      storage.removeSync('newUserToastZhuli')
    }
  }
}

/**
 * @description 资源位跳转站外H5
 * @param {Object} options 资源位参数
 */
export function redirectH5(options) {
  if (options.linkUrl) {
    wx.$.r.push({
      path: '/subpackage/member/jumpWebView/index',
      query: {
        url: encodeURIComponent(options.linkUrl),
        popCode: options.riskPopCode,
        risk: options.risk,
      },
    })
  }
}

/**
 * @description 资源位内部跳转
 * @param {Object} options 资源位参数
 */
export function navigatePage(options) {
  if (options.linkUrl) {
    let newUrl = options.linkUrl.indexOf('/') === 0 ? options.linkUrl : `/${options.linkUrl}`
    if (newUrl.indexOf('subpackage/resume/resume_publish/complete') > -1) {
      // 跳转到三步完善页的页面参数处理
      const { subListResp } = store.getState().storage.myResumeDetails
      const { uuid } = getArrayVal(subListResp)[0] || {}
      newUrl = `${newUrl}${newUrl.indexOf('?') > -1 ? '&' : '?'}resumeSubUuid=${uuid}&publish=0`
    }
    const { path, query: n_query } = common.parsePath(newUrl) || {}
    const query: any = n_query
    query.r_s_code = options.r_s_code
    query.s_t = options.s_t
    query.myepc = getPageCode()

    // 获取当前路由
    let prevRoute = ''
    const pages = getCurrentPages()
    if (pages.length > 0) {
      const prevPage = pages[pages.length - 1]
      prevRoute = prevPage.route
    }
    if (prevRoute && path.indexOf(prevRoute) > -1) {
      // 当前页面
      wx.$.r.reLaunch({
        path,
        [wx.$.r.isTabbar(path) ? 'params' : 'query']: query,
      })
    } else {
      wx.$.r.push({
        path,
        [wx.$.r.isTabbar(path) ? 'params' : 'query']: query,
      })
    }
  }
}

/**
 * @description 资源位跳转小程序
 * @param {Object} options 资源位参数
 */
export const launchMiniApp = (options) => {
  if (!ENV_IS_WEAPP) {
    return
  }
  if (options.appletId === miniConfig.appid) {
    navigatePage(options)
    return
  }
  if (options.appletId && options.linkUrl) {
    wx.navigateToMiniProgram({
      appId: options.appletId,
      path: options.linkUrl,
    })
  } else {
    wx.$.msg('缺少AppID或跳转路径')
  }
}

/**
 * @description 资源位跳转站内H5
 */
export const navigateH5 = (options) => {
  if (options.linkUrl) {
    let url = options.linkUrl
    const location = storage.getItemSync('userLocation')
    if (location) {
      url = `${url}${url.indexOf('?') > -1 ? '&' : '?'}location=${location}`
    }
    if (options.linkUrl.indexOf('?') >= -1) {
      url = encodeURIComponent(options.linkUrl)
    }

    wx.$.r.push({
      path: '/subpackage/web-view/index',
      query: {
        url,
        r_s_code: options.r_s_code,
        s_t: options.s_t,
        myepc: getPageCode(),
      },
    })
  }
}

/**
 * @description 资源位跳转站外小程序
 */
export const launchOutMiniApp = async (options) => {
  if (!ENV_IS_WEAPP) {
    return
  }
  if (!options.appletId) {
    wx.$.msg('缺少AppID')
    return
  }
  if (options.risk == 1 && options.riskPopCode) {
    const popup = await dealDialogByApi(options.riskPopCode)
    if (popup) {
      wx.$.showModal({
        ...popup,
        closeReport: { expand_id: `${options.appletId}_${options.linkUrl}` },
        success: (res) => {
          if (res && res.jumpEventType == 4) {
            wx.navigateToMiniProgram({
              appId: options.appletId,
              path: options.linkUrl || '',
            })
          }
        },
      })
      return
    }
  }
  wx.navigateToMiniProgram({
    appId: options.appletId,
    path: options.linkUrl || '',
  })
}

/**
 * @description 资源跳转
 * @param {Object} item 资源对象
 */
export const resourceJump = (item, params = {}, queryKey = '') => {
  if (!item) {
    return
  }
  // 分享弹窗
  if (item.linkUrl.indexOf('share/newSharePage') > -1) {
    const q = wx.$.u.getUrlAllParams(item.linkUrl)
    wx.$.model.inviteWorkers({
      sharePage: q.page,
      sharePath: q.path,
      shareName: q.shareName,
    })
    return
  }
  switch (item.linkType) {
    /** 站内原生地址 */
    case 1:
      navigatePage({ ...item, s_t: 'RESOURCE', r_s_code: item.resourceCode })
      break
    /** 站外H5地址 */
    case 2:
      redirectH5(item)
      break
    /** 站内H5地址 */
    case 3:
      if (Object.keys(params) && queryKey === 'citySource') {
        const citySource = encodeURIComponent(JSON.stringify(params))
        if (item.linkUrl && item.linkUrl.indexOf('?') >= -1) {
          item.linkUrl = `${item.linkUrl}&citySource=${citySource}`
        } else if (item.linkUrl) {
          item.linkUrl = `${item.linkUrl}?citySource=${citySource}`
        }
      }
      navigateH5({ ...item, s_t: 'RESOURCE', r_s_code: item.resourceCode })
      break
    /** 跳转其他小程序 */
    case 4:
      launchMiniApp(item)
      break
    /** 跳转站外小程序 */
    case 8:
      launchOutMiniApp(item)
      break
    default:
      break
  }
}

/**
 * @description: 获取基础配置信息 另外一个接口
 */
export const getBasicConfig = (() => {
  let promiseState
  /**
   * @param {string} key 配置key
   * @param {string} action 获取配置接口参数（默认*获取所有）
   * @param {boolean} isUpdate 是否更新配置
   * @param {boolean} isAll 是否获取所有配置
   */
  return async (options) => {
    const { key, isUpdate, isAll } = options
    try {
      // 如果有正在请求的配置信息，等待请求完成
      if (promiseState) {
        await messageQueue((state) => Object.keys(state.config.basicConfig).length > 0)
      }
      let { basicConfig } = store.getState().config || {}
      if (isUpdate || Object.keys(basicConfig).length === 0) {
        // 防止重复请求
        promiseState = true
        const { data } = await wx.$.javafetch['POST/job/v2/manage/job/config/basic']({})
        promiseState = false
        if (!data) {
          return isAll ? {} : ''
        }
        basicConfig = { ...basicConfig, ...data }
        dispatch(actions.configActions.setState({ basicConfig }))
      }
      return isAll || !key ? basicConfig : basicConfig[key]
    } catch (err) {
      return isAll ? {} : ''
    } finally {
      dispatch(actions.configActions.setState({ isReqBasicConfig: true }))
    }
  }
})()

/** 全局的 按钮 - 免费状态
 * @params sourcePage 来源页 1-招工大列表 2-管理招工页 3-发布招工1发布招工2发布招工弹窗 4-简历列表页
 * @returns recruitButtonFree -- 是否发布按钮免费
 * @returns resumeButtonStatus -- 找活发布按钮状态 1-免费：未发布 2-管理：已发布&完善度<=60% 3-不展示：已发布&完善度>60%
 */
export const rqCommonButtonFreeStatus = async (sourcePage: Number, cityId?: Number, occIds?: any[]) => {
  const params: any = { sourcePage }
  if (sourcePage == 3) {
    params.cityId = cityId
    params.occIds = occIds
  }
  const results = { recruitButtonFree: false, resumeButtonStatus: 3, isShowPublishButton: false }
  const isLogin = store.getState().storage.userState.login
  if (isLogin) {
    const res: any = await wx.$.javafetch['POST/job/v2/manage/job/publish/buttonStatus'](params)
    if (res && res.code == 0) {
      return {
        recruitButtonFree: res?.data?.whetherPublishButtonFree,
        resumeButtonStatus: res?.data?.resumeButtonStatus || 3,
        isShowPublishButton: res?.data?.isShowPublishButton || false,
      }
    }
  }
  return results
}

/**
 * @description (同步)选择工种后，判断订单类别/ 招聘类别
 *  */

export async function judgeClassifyType(occArr = [], quickOccValue = []) {
  let haveChosenList = [...quickOccValue]
  if (occArr.length > 0) {
    haveChosenList = occArr
  }
  if (haveChosenList?.length > 0) {
    let classifyTrades = haveChosenList
    if (!haveChosenList[0] || haveChosenList[0].mode == null || haveChosenList[0].mode == undefined) {
      const occArr = haveChosenList?.map((item: any) => {
        return item?.id || ''
      })
      classifyTrades = await wx.$.l.getClassifyByIds(occArr)
    }
    // 招聘类：false 订单类：true
    return wx.$.u.isArrayVal(classifyTrades) && classifyTrades[0].mode == 1
  }
  return true
}

type IMediaResource = YModels['POST/job/v2/manage/job/modify/info']['Res']['data']['mediaResource']
type IMediaResourceReturn = IMediaResource[number] & {
  /** 图片资源的url或者视频资源的url */
  path: string
  /** 上传状态为成功状态 */
  uploadState: number
  /** 文件类型 */
  fileType: 'video' | 'image'
}
/** 新java接口处理图片和视频数据，主要目的为了能正常显示在上传图片组件上 */
export function handlerImageAndVideo(mediaResource: IMediaResource) {
  let newMR: IMediaResourceReturn[] = []
  if (!wx.$.u.isArrayVal(mediaResource)) {
    return newMR
  }
  newMR = mediaResource.map((item) => {
    const isVideo = item.type.code != 2
    return {
      ...item,
      /** 图片资源的url或者视频资源的url */
      path: isVideo ? item.videoUrl : item.imageUrl,
      /** 上传状态为成功状态 */
      uploadState: 1,
      /** 文件类型 */
      fileType: isVideo ? 'video' : 'image',
    }
  })
  return newMR
}

type ParamsMediaResource = YModels['POST/job/v2/manage/job/modify/job']['Req']['mediaResource']
/** 新java接口获取图片和视频数据,用于提交数据使用 */
export function getImageAndVideo(mediaResource): ParamsMediaResource {
  const paramsMR: ParamsMediaResource = []
  if (wx.$.u.isArrayVal(mediaResource)) {
    mediaResource.forEach((item) => {
      const isVideo = item.fileType != 'image'
      /** 上传端；1-安卓；2-10S；3-微信小程序；4-百度小程序 */
      let uploadTerminal = 3
      if (ENV_IS_SWAN) {
        uploadTerminal = 4
      }
      paramsMR.push({
        /** 类型 1-视频 2-图片 */
        type: isVideo ? 1 : 2,
        /** 图片资源或者视频封面资源的相对url地址 */
        image: item.originImageUrl,
        video: item.originVideoUrl || undefined,
        videoId: isVideo ? item.videoId || undefined : undefined,
        duration: isVideo ? item.duration || undefined : undefined,
        uploadTerminal,
      })
    })
  }
  return paramsMR
}

/**
 * 获取简历职位联系按钮基础配置
 * @param code 页面code，现在外面还没有使用，仅在自身回调的时候使用
*/
export async function getContactBtnConfig(code?: PageCodeType) {
  const pageCode = code || getPageCode()
  const btnPageCodes = ['recruit_list', 'resume_list', 'search_recruit_detail', 'resume_result_list', 'resume_detail', 'recruit_detail', 'im_conversation', 'Zone_Joblist_Page']
  if (!pageCode || !btnPageCodes.includes(pageCode)) return

  const userChooseRole = storage.getItemSync('userChooseRole')
  const role = userChooseRole == 1 ? 'b' : 'c'
  const btnConfig = storage.getItemSync(`btn_${pageCode}`)
  const { btnConfigStatus } = store.getState().config
  if (!wx.$.u.isArrayVal(btnConfig[`list_${role}`]) || !btnConfigStatus[`${pageCode}_${role}`]) {
    const res = await wx.$.javafetch['POST/cms/v2/button/page']({ pageCode, dataVersion: btnConfig.appVersion != REQUEST_VERSION ? null : btnConfig[`version_${role}`] }).catch((err) => { })
    const { code, data } = res || {}
    if (code == 0 && data && wx.$.u.isArrayVal(data.list)) {
      // 获取data.list中pageCode与当前pageCode相同的数据
      const currConfig = data.list.find((item) => item.pageCode == pageCode)
      const btnConfig_role = role == 'b' ? { list_b: currConfig.buttonConfigList, version_b: data.dataVersion } : { list_c: currConfig.buttonConfigList, version_c: data.dataVersion }
      currConfig && storage.setItemSync(`btn_${pageCode}`, { ...(btnConfig.appVersion == REQUEST_VERSION ? btnConfig : {}), ...btnConfig_role, appVersion: REQUEST_VERSION })
    }
  }
  dispatch(actions.configActions.setState({ btnConfigStatus: { ...btnConfigStatus, [`${pageCode}_${role}`]: true } }))
  // 如果是简历列表，那么就再次把详情的配置也获取一下
  if (pageCode === 'resume_list') {
    getContactBtnConfig('resume_detail')
  }
}

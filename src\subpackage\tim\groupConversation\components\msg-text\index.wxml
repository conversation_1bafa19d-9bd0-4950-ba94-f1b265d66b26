<view class="msg-txt-v">
  <longpress-tk msgInfo="{{msgInfo}}" wx:if="{{isShowTk}}" catch:click="onTkClick" tkWith="{{tkWith}}" />
  <msg-state wx:if="{{msgInfo.isSelf}}" style="height: 100%;" margin="right" msgInfo="{{msgInfo}}" status="{{msgInfo.status}}" bind:statusclcik="onStatusClcik"/>
  <view class="msg-txt {{msgInfo.isSelf?'':'msg-other'}}" catch:longpress="onLongpressMsg" id="{{msgInfo.id}}_tk">
    <view class="dom_em" wx:if="{{!!msgInfo.payload.renderDom && msgInfo.payload.renderDom.length > 0}}">
      <block wx:for="{{msgInfo.payload.renderDom}}" wx:for-item="emItem" wx:key="index">
        <image lazy-load="{{true}}" class="emoji" wx:if="{{emItem.name == 'img'}}" src="{{emItem.src}}" />
        <text class="dom_txt" wx:else>{{emItem.text}}</text>
      </block>
    </view>
    <text class="dom_txt" wx:else>{{msgInfo.payload.text}}</text>
  </view>
</view>
/* eslint-disable @typescript-eslint/no-unnecessary-type-constraint */

import removeTools from './remove'
import modTools from './mod'

/**
 * loading(加载中) more(上拉加载更多) finish(没有更多数据了) error(状态) none(缺省值)
 * @description 具体状态以 components > base > load-more 组件内为准
 */
type TLoadMoreState = 'loading' | 'more' | 'finish' | 'error' | 'none' | ''

/**
 * 通用列表参数
 */
export interface IPageListParams<L> {
  /** 页面列表数据 */
  list: L[]
  /** 当前页面 */
  page: number
  /** 每页获取的数据条数 */
  pageSize: number
  /** 是否正在获取数据中 */
  loading: boolean
  /** 页面数据列表是否已经全部加载完成 */
  finish: boolean
  /** 是否为空状态 */
  isEmpty: boolean
  /** load-more 组件展示状态 */
  loadMoreState?: TLoadMoreState
  [key: string]: any
}

export interface IReqPLOptions {
  /** 是否为刷新状态(page 设置为 1, finish 设置为 false, loadMoreState 置为 '') */
  isRefresh?: boolean
  /** 是否为切换 tab 标签状态, 主要用于切换后, 列表数据为空显示 load-more 加载中状态(提高用户体验) */
  isSwitchTab?: boolean
}

const initPageListInfo: IPageListParams<any> = {
  list: [],
  page: 1,
  pageSize: 15,
  finish: false,
  isEmpty: false,
  loading: true,
  loadMoreState: 'loading',
}

/**
 * @name 获取默认的列表初始参数
 * @param newParams.list          默认列表数据 []
 * @param newParams.page          默认页码 1
 * @param newParams.pageSize      默认每页请求数量 10
 * @param newParams.loading       默认加载状态 true
 * @param newParams.finish        默认完成状态 false
 * @param newParams.isEmpty       默认空状态  false
 * @param newParams.loadMoreState 默认 load-more 组件状态字符 ''
 *
 */
const getDefaultPLData = <L>(newParams: Partial<IPageListParams<L>> = {}) => ({ ...initPageListInfo, ...newParams })

/**
 * @name 获取请求前, 请求列表的 page loading finish loadMoreState 状态
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @param params 统一的页面列表数据格式
 * @param options.isRefresh 必传--(用于确定 下拉刷新 还是 上拉加载更多)
 * @param options.isSwitchTab 可选--用于 tab 切换展示 lode-more 组件 loading 状态(提升用户体验)
 */
function getReqPLStatus<L>(params: IPageListParams<L>, options?: Partial<IReqPLOptions>): IPageListParams<L> {
  const { isRefresh = false, isSwitchTab = false } = options || {}
  // 非刷新状态(比如: 上拉触底加载更多时)
  if (!isRefresh) {
    return { ...params, loadMoreState: 'loading', loading: true, isEmpty: false }
  }

  // 下拉刷新 不改变 loadMoreState 文案状态
  // 避免数据全部加载完成时底部 常驻文案 下拉刷新时的闪动问题
  const { list, loadMoreState: oldLMS } = params
  const useOldLMState = list.length > 0 && oldLMS === 'finish'
  if (useOldLMState) {
    return { ...params, page: 1, loading: true, finish: false, isEmpty: false }
  }

  // load-more 组件展示的状态
  const isFirstLoading = !params.finish && list.length === 0
  const loadMoreState: TLoadMoreState = isFirstLoading || isSwitchTab ? 'loading' : ''
  return { ...params, loadMoreState, page: 1, loading: true, finish: false, isEmpty: false }
}

interface IResPLOptions<L> {
  newList: L[]
  /** 是否为刷新状态(page 设置为 1, finish 设置为 false, loadMoreState 置为 '') */
  isRefresh?: boolean
  /** 格式化 list 数据列表, 单条 item 数据或新增其它数据项 */
  fmtListItem?: (item: L, index?: number) => L & Record<string, any>
  /** 自定义完成上拉加载完成的数量 */
  customFinishPageSize?: number
  /** 最大页数 */
  totalPage?: number
  search_result?: string
}

/**
 * @name 获取请求后,请求列表的 page loading finish loadMoreState 状态
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @param params 统一的页面列表数据格式
 * @param options.newList 必传--后端接口获取的最新数据数组列表(用于合并之前的老数据及判断数据是否全部加载完成)
 * @param options.isRefresh 可选--(用于判断是否为刷新页面, 不传则通过 params.page 为 1 确定为刷新页面)
 */
function getResPLStatus<L>(params: IPageListParams<L>, options: IResPLOptions<L>): IPageListParams<L> {
  const { list: oldList = [], page, pageSize, loadMoreState: oldLMS } = params
  const { newList = [], isRefresh = false, fmtListItem, customFinishPageSize, totalPage = 0, search_result = '' } = options || {}
  // load-more 组件的展示状态(具体以 load-more 组件内为主)
  let loadMoreState: TLoadMoreState = oldLMS === 'loading' ? '' : oldLMS
  // 如果列表为空且新获取的列表也为空则 loadMoreState 状态为 ''
  loadMoreState = oldList.length === 0 && !newList?.length && loadMoreState === 'finish' ? '' : loadMoreState

  try {
    const len = oldList.length
    newList.map((item: any, index) => {
      item.guid = `rq${new Date().getTime()}`
      item.pagination_location = `${index + 1}`
      item.location_id = index + 1 + len
      item.pagination = params.page
      item.search_result = search_result
      return item
    })
    // 判断是否需要对后端返回的数据列表进行 数据清洗
    const fmtList = fmtListItem ? newList.map(fmtListItem) : newList
    const finalList = (isRefresh || page === 1) ? fmtList : [...oldList, ...fmtList]

    let finish = false
    if (typeof customFinishPageSize === 'number' && customFinishPageSize >= 0) {
      finish = fmtList.length <= customFinishPageSize
    } else {
      finish = fmtList.length < pageSize
    }
    if (totalPage) {
      finish = page >= totalPage
    }

    loadMoreState = finish ? 'finish' : loadMoreState
    // 是否为空状态(列表 数据都加载完成且最终的列表长度为 0)
    const isEmpty = finish && finalList.length === 0

    const finalPage = finalList.length ? page + 1 : 1

    return { ...params, list: finalList, page: finalPage, loading: false, finish, loadMoreState, isEmpty }
  } catch (error) {
    // 是否为空状态-上面操作报错后判断(判断列表是否为空)
    const isEmpty = oldList.length === 0
    return { ...params, loadMoreState, isEmpty, loading: false }
  }
}

/**
 * @name 获取请求报错后,loading/loadMoreState初始状态
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @param params 统一的页面列表数据格式
 */
function getInitPLStatus<L>(params: IPageListParams<L>): IPageListParams<L> {
  const { list, loadMoreState: oldLMS } = params
  let loadMoreState: TLoadMoreState = oldLMS === 'loading' ? '' : oldLMS
  loadMoreState = !list.length && loadMoreState === 'finish' ? '' : loadMoreState
  // 页面首次加载且服务器报错时(无列表数据则 finish 为 true 展示列表空状态)
  const finish = !list.length || params.finish
  // 是否空状态-列表接口获取报错时
  const isEmpty = list.length === 0
  return { ...params, loadMoreState, finish, isEmpty, loading: false }
}

/**
 * @name 辅助函数-用于判断是否为 加载中 或 加载完成 状态(用于控制触底是否加载更多)
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 */
function isLoadingOrFinishState<L>(params: IPageListParams<L>): boolean {
  const { loading, finish } = params
  return loading || finish
}

/**
 * @name 辅助函数-用于下拉刷新判断是否正在加载中...状态(则退出不请求)
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 */
function isLoadingState<L>(params: IPageListParams<L>): boolean {
  return params.loading
}

/**
 * @name 辅助函数-用于页面tab切换后判断是否获取列表数据
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 */
function isRefreshAfterSwitchingTab<L>(params: IPageListParams<L>): boolean {
  if (params.list.length > 0) {
    return false
  }
  return true
}

/**
 * @name 辅助函数-判断对应tab下是否存在页面数据
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 * @description 非 IPageListParams 定义的接口类型请不要🙅🏻‍♀️使用此方法
 */
function hasPageListData<L>(params: IPageListParams<L>): boolean {
  if (params.list.length > 0) {
    return true
  }
  return false
}

/** 通用列表数据处理工具 PLTools */
export default {
  ...modTools,
  ...removeTools,
  getDefaultPLData,

  getReqPLStatus,
  getResPLStatus,
  getInitPLStatus,

  isLoadingState,
  isLoadingOrFinishState,
  isRefreshAfterSwitchingTab,

  hasPageListData,
}

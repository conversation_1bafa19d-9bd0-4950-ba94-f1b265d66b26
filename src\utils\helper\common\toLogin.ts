/*
 * @Date: 2022-04-09 14:39:59
 * @Description: 跳转到登录界面，登录完成后执行指定代码，并会覆盖登录页面的返回逻辑
 */

import { storage } from '@/store/index'
import { getLoginAb, loginBack } from '../login/index'

/** 跳转到登录，登录执行后完成回调 */
const currentResolve = { resolve: null, reject: null }
/** 跳转登录参数 */
const options = { back: false }
/** 登录节流,是否已经跳转了登录  */
let isJumpLogin = false
/** 登录弹窗 */
let isPopLogin = false
/** 登录成功后的视图更新回调 */
let _loginCallbacks = {} as Record<string, Array<{
  instance: any;
  callback: Function;
}>>

interface ToLoginQuery {
  /** 登录类型-1、授权登录；2、验证码登录（验证码登录只有进页面） */
  auth_type?: number;
  /** 是否跳过弹窗，直接去登录页 */
  toLoginPage?: boolean;
  [key: string]: any;
}

/**
 * @name 登录，登录执行后完成回调方法
 * @params isBack 登录之后返回
 * @params query 登录页面参数
 * @return Promise
 * */
export const toLogin = async (isBack = false, query: ToLoginQuery = {}) => {
  /** 如果已经在登录页面，就return */
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  if (currentPage?.route?.includes('subpackage/userauth/auth/index')) {
    return new Promise((resolve, reject) => {
      reject()
    })
  }
  let isPopup = false
  if (!isJumpLogin || !isPopLogin) {
    // eslint-disable-next-line no-nested-ternary
    isPopup = query.auth_type == 3 ? true : ((query.auth_type == 2 || query.toLoginPage) ? false : await getLoginAb(['RegLog_Only_Button', 'RegLog_All_New']))
    /** 登录节流,是否已经跳转了登录  */
    if (isPopup && !isPopLogin) {
      isPopLogin = true
      // 登录弹窗
      wx.$.model.login({ isBack })
    } else if (!isJumpLogin && !isPopup) {
      isJumpLogin = true
      // push会走路由拦截，若跳转到登录则清除isJumpLogin这个状态
      wx.$.r.push({ path: '/subpackage/userauth/auth/index', query })
    }
  }
  return new Promise((resolve, reject) => {
    /** 由于更新此代码到分包，导致执行顺序错乱导致清理调了登录的回调函数 */
    currentResolve.resolve = resolve
    currentResolve.reject = reject
    options.back = isPopup ? false : isBack
  })
}

/** 未跳转登录登录情况下清除登录回调状态 */
export const clearLoginCall = () => {
  if (currentResolve.resolve || currentResolve.reject) {
    currentResolve.resolve = null
    currentResolve.reject = null
  }
}

/** 清除拦截跳转登录状态 */
export const clearLoginState = (type = 'page') => {
  // 清除登录节流状态
  if (isJumpLogin && type === 'page') {
    isJumpLogin = false
  }
  if (isPopLogin && type === 'model') {
    isPopLogin = false
  }
}

/** 通知resolve */
toLogin.callResolve = () => {
  if (currentResolve.resolve) {
    currentResolve.resolve('登录成功')
    if (options.back) {
      loginBack()
    }
    // 清空保存的resolve
    currentResolve.resolve = null
    return true
  }
  return false
}

/** 通知reject */
toLogin.callReject = () => {
  if (currentResolve.reject) {
    currentResolve.reject('登录失败')
    currentResolve.reject = null
  }
}

/** 获取currentResolve */
toLogin.getCurrentResolve = () => currentResolve

/** 注册登录回调 */
export const registerLoginCallback = (callback, instance) => {
  const { login } = storage.getItemSync('userState')
  if (login) return
  const path = getCurrentPagePath()
  if (typeof callback === 'function') {
    if (!_loginCallbacks[path]) {
      _loginCallbacks[path] = []
    }
    _loginCallbacks[path] = [
      { callback, instance },
      ..._loginCallbacks[path],
    ]
  }
}

/** 移除登录回调函数 */
export const unLoginCallback = (type = 'page', instance = null) => {
  const { login } = storage.getItemSync('userState')
  if (login) return
  const path = getCurrentPagePath()
  if (type === 'page') {
    _loginCallbacks[path] = []
  } else if (wx.$.u.isArrayVal(_loginCallbacks[path])) {
    _loginCallbacks[path] = _loginCallbacks[path].filter(item => item.instance !== instance)
  }
}

/** 执行回调 */
export const callLoginCallbacks = () => {
  const { login } = storage.getItemSync('userState')
  if (!login) return
  const path = getCurrentPagePath()
  const callbacks = _loginCallbacks[path]
  if (callbacks) {
    callbacks.forEach(({ callback, instance }) => {
      callback.call(instance)
    })
  }
  _loginCallbacks = {}
}

/** 获取当前页path */
export const getCurrentPagePath = () => {
  const currentPage = wx.$.r.getCurrentPage()
  return currentPage ? currentPage.route : wx.getLaunchOptionsSync().path
}

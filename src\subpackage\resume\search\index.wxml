<custom-header customStyle="background:transparent" title="人才库" />

<no-job-empty wx:if="{{!login || isNoData}}" />
<block wx:else>
    <search-head 
        id="search-head" 
        keywords="{{keywords}}"
        bind:selectjob="onSelectJobShow"
        bind:joblist="onJobList"
        bind:change="onChange" 
        bind:confirm="onSearch"
        bind:search="onSearch"
        bind:clear="onClear"
    />
    <search-history bind:click="onHistoryClick"/>
</block>
<search-label 
    wx:if="{{isSearchLabel}}"
    list="{{labelList}}"
    top="{{topHeight + searchHeadHeight}}"
    bind:click="onLabelClick"
/>

<select-job-pop 
    visible="{{isSelectJobShow}}"   
    list="{{jobList}}"
    bind:close="onSelectJobClose"
    bind:click="onSelectJobClick"
/>
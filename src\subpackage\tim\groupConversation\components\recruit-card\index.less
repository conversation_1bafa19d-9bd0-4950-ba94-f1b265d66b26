.out-v {
  display: flex;
  justify-content: flex-end;
  margin: 0 24rpx 40rpx; 
  align-items: flex-end;
}

.out-start {
  justify-content: flex-start;
}

.recuit-content {
  position: relative;
  background: #fff;
  border-radius: 24rpx 24rpx 4rpx 24rpx;
  padding: 24rpx;
  width: 542rpx;
}

.recuit-content-l {
  border-radius: 24rpx 24rpx 24rpx 4rpx;
}

.yzm-icon {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 80rpx;
  height: 80rpx;
}

.recruit-title {
  font-size: 30rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.classly-p {
  display: flex;
  margin-top: 16rpx;
}

.classly-content {
  margin: -4rpx;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
}

.classly-txt {
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.65);
  padding: 0 12rpx;
  border-radius: 8rpx;
  background: rgba(245, 246, 250, 1);
  margin: 4rpx;
}

.address-txt {
  margin-top: 16rpx;
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.detail-txt {
  margin-top: 16rpx;
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.45);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
}

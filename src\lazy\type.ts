/*
 * @Date: 2024-12-10 17:37:10
 * @Description: 定义类型 这个地方定义的类型，必须是路径加方法名，不然会炸掉
 */

import { transformControlInfoList, transformTemplateInfo, getImproveRecruitmentInfo, autoRefresh, getRecruitDetailsPath, getBaiduSeoDate, getCrumb, transformChargesForm, preJobInfo } from './recruit/index'
import { isLimitAuth, getEToken, executeFaceAuth, validateIdentity, getEResult, refreshRefactory } from './realname/index'
import { deleteNullProp, existJobTopSet, formatThoroughAddress, jumpToComplete, jumpToRecruitTopSet, prevCheckWithDraft, publishWithPrevCheck, saveDraft, jumpToCompleteWin } from './recruit/publish'
import { getSubJobs, attachmentPath, isGeneratePdfPrePop } from './resume/attachment'
import { getRecommendQualityWorkerCount<PERSON><PERSON>, getNearbyRecommendWorkerList<PERSON>pi } from './resume/worker'
import { goToChatReport, clickBossReport, callPhoneBtnOfList, callPhoneReportUt, delayUpdateListState, getOccV2, newuserEnterDetail, setLookJobNum } from './recruit/callphone'
import { midPhoneTimeMsg, recall, recallV3, showMidPopup } from './common/midphone/index'
import { callPhone } from './common/midphone/contactCard'
import { getImChat, getImChatPre, newResumeMidPops, operationMidCallV3, resumeMidTelV3, resumeTelV3 } from './common/midphone/callv3'
import { getGuidanceToMini, getGuidanceToDesktop, getGuidanceToCollection } from './common/guidance'
import { getSystemChatMsg, setSystemMsgByCallBackMsg, handleRevoked, handleMsgList, getConversationLastMsg, removeConversationMsg, handleSystemInfoMsgList, saveRevokeMsgList } from './im/messages'
import {
  sendConverReaded, msgReadReceipt, timLogin, reTimLogin, timLogoutAndReLogin, timLogout, initGroup, sendResumeEmail, resendMessage, isShowChat, deleteConversation,
  addToBlacklist, removeFromBlacklist, reGetImData,
  handleTransmitType,
} from './im/im-method'
import { sendTextMessage, sendCustomMessage, sendAudioMessage, sendImageMessage, msgRemindType, getTimObj, handleReceivedOrSendMsg, initTim } from './im/im-init'
import { sortMsgGroup, sortImChatList, msgTypeTransformation, assConverPre, fetchTenCentData, setMyMsgGroupByGroup } from './im/conversion'
import { dateTransformation, transDate } from './im/util'
import { peerReadPtypes, peerReadTypes, EmojiData } from './im/type.d'
import { deliveryAttachResumeCheck, deliveryPreCheck, deliveryWechatCheck } from './recruit/delivery'
import { getClassTreeData, getRecruitClassify, getPartClassTreeData, getClassifyConfig, getFullOrPartClassifyData } from './classify/index'
import { getClassifyById, getClassifyByIds, getClassifyByNames, getClassifyText, getOccScrollPosition, getSelectedClassifyChild, handleClassifyData, nwtransformClassifyValueByIds } from './classify/cmUtils'
import { isShowBlackModel } from './common/index'
import { geTemplate, getTempListV3 } from './common/template'
import { getShowTemplate, sortTemPrefDataList, handleTemData } from './resume/template'
import { commonLocalExposure, fetchAreaByAdCode, fetchAreaData, handleLocationVal, fetchStandbyData } from './helper/location/utils'
import { changeNewAreaData,
  fetchAreaIdByAdCode, formatOutPutLocationData, getAreaByAdName, getAreaByName,
  getAreaByNames, getAreaTreeData, getCityById, getCityByIds, getCityByIdTree,
  getCurrentArea, getLocation,
  getLongitudeAndLatitudeArr,
  getProjectAddressDistance,
  getProjectAddressDistanceTxt,
  getUserIpLocation, handleGps, saveLocationCity,
  setLocationCity, isByAdcodeRegion,
  isByIdRegion,
  getCityByIp,
  getAreaSearch,
  getAreaSearches,
  getAreaByAdcode,
  getAreaById,
  getAreaByIds } from './helper/location/index'
import { getBtmClassifyChild, transformClsIdHidsByOccV2, transformClsIdHidsByStrIds, transformOccV2ToHidClsId } from './classify/utils'
import { startEid, initEid } from './face/index'
import { collectService } from './common/collect'
import { Bury } from './recruit/bury'
import { getDayData, getListValue, getMonthData, getSalaryText, judgePublishLogic, publishResumeResultReport } from './resume/publish'
import { memoized } from './memoized/index'
import { newRecruitMidPops, recruitTelChat } from './recruit/midphone'
import { resumesRefresh } from './resume/resumesRefresh'

export const type = {
  transformChargesForm,
  getTempListV3,
  getAreaByIds,
  getAreaById,
  getAreaByAdcode,
  getAreaSearches,
  getAreaSearch,
  getCityByIp,
  isByIdRegion,
  isByAdcodeRegion,
  setLocationCity,
  handleGps,
  getAreaTreeData,
  formatOutPutLocationData,
  getLongitudeAndLatitudeArr,
  getProjectAddressDistanceTxt,
  getProjectAddressDistance,
  changeNewAreaData,
  getCityByIdTree,
  getCityByIds,
  getAreaByAdName,
  getAreaByNames,
  getAreaByName,
  getCityById,
  saveLocationCity,
  getCurrentArea,
  getLocation,
  getUserIpLocation,
  fetchAreaIdByAdCode,
  commonLocalExposure,
  fetchAreaByAdCode,
  fetchAreaData,
  handleLocationVal,
  fetchStandbyData,
  getGuidanceToCollection,
  getGuidanceToDesktop,
  getGuidanceToMini,
  jumpToCompleteWin,
  transformControlInfoList,
  transformTemplateInfo,
  getImproveRecruitmentInfo,
  isLimitAuth,
  getEToken,
  executeFaceAuth,
  validateIdentity,
  getEResult,
  formatThoroughAddress,
  prevCheckWithDraft,
  publishWithPrevCheck,
  jumpToRecruitTopSet,
  existJobTopSet,
  jumpToComplete,
  saveDraft,
  deleteNullProp,
  isGeneratePdfPrePop,
  getSubJobs,
  attachmentPath,
  getRecommendQualityWorkerCountApi,
  getNearbyRecommendWorkerListApi,
  getBaiduSeoDate,
  getCrumb,
  setLookJobNum,
  goToChatReport,
  clickBossReport,
  callPhoneBtnOfList,
  getOccV2,
  delayUpdateListState,
  callPhoneReportUt,
  newuserEnterDetail,
  /** 中间号-复制真实号码弹窗/评价合作弹窗等---start */
  showMidPopup,
  callPhone,
  recall,
  recallV3,
  midPhoneTimeMsg,
  newResumeMidPops,
  resumeTelV3,
  operationMidCallV3,
  resumeMidTelV3,
  getImChatPre,
  getImChat,
  /** --中间号-复制真实号码弹窗/评价合作弹窗等--end---- */
  getTimObj,
  getSystemChatMsg,
  setSystemMsgByCallBackMsg,
  sortMsgGroup,
  sortImChatList,
  dateTransformation,
  sendConverReaded,
  msgReadReceipt,
  handleRevoked,
  timLogin,
  peerReadPtypes: () => {
    return peerReadPtypes
  },
  peerReadTypes: () => {
    return peerReadTypes
  },
  EmojiData: () => {
    return EmojiData
  },
  reTimLogin,
  timLogoutAndReLogin,
  timLogout,
  initGroup,
  sendResumeEmail,
  resendMessage,
  isShowChat,
  sendTextMessage,
  sendCustomMessage,
  sendAudioMessage,
  sendImageMessage,
  msgRemindType,
  deleteConversation,
  addToBlacklist,
  removeFromBlacklist,
  handleMsgList,
  getConversationLastMsg,
  removeConversationMsg,
  reGetImData,
  msgTypeTransformation,
  assConverPre,
  handleReceivedOrSendMsg,
  transDate,
  fetchTenCentData,
  setMyMsgGroupByGroup,
  handleSystemInfoMsgList,
  saveRevokeMsgList,
  deliveryPreCheck,
  deliveryAttachResumeCheck,
  deliveryWechatCheck,
  initTim,
  getClassTreeData,
  getRecruitClassify,
  getPartClassTreeData,
  getClassifyConfig,
  getFullOrPartClassifyData,
  getClassifyById,
  getClassifyByIds,
  getClassifyByNames,
  nwtransformClassifyValueByIds,
  isShowBlackModel,
  getShowTemplate,
  sortTemPrefDataList,
  geTemplate,
  handleTemData,
  getOccScrollPosition,
  getSelectedClassifyChild,
  getClassifyText,
  handleClassifyData,
  getBtmClassifyChild,
  transformClsIdHidsByStrIds,
  transformClsIdHidsByOccV2,
  transformOccV2ToHidClsId,
  startEid,
  initEid,
  refreshRefactory,
  collectService,
  bury: async () => {
    return Bury
  },
  getListValue,
  judgePublishLogic,
  getDayData,
  getMonthData,
  getSalaryText,
  publishResumeResultReport,
  memoized,
  preJobInfo,
  getRecruitDetailsPath,
  autoRefresh,
  recruitTelChat,
  newRecruitMidPops,
  resumesRefresh,
  handleTransmitType,
} as const

export type Type = typeof type;

// 提取路径最后一部分的类型
type ExtractLastPath<T extends string> = T extends `${infer _}/${infer Rest}`
  ? ExtractLastPath<Rest>
  : T;

export type ReversedTypeRecord = {
  [K in keyof Type as ExtractLastPath<K>]: { value: Type[K] };
};

// 定义最终的 `ReversedType` 类型
export type ReversedType = {
  [K in keyof Type as ExtractLastPath<K>]: Type[K];
};

import { SHARE_BTN_LOCATION, SHARE_CHANNEL } from '@/config/share'
import { actions, dispatch, store } from '@/store/index'
import { getShareInfo, completeShare, getSharePathInfo } from '@/utils/helper/share/index'

/** 发送给朋友的相关分享 */
export const setShareInfo = (message: any = {}, from: string, params_path?: string) => {
  const path = params_path || '/subpackage/web-view/index'
  const { userId } = store.getState().storage.userState
  const payload = { btnId: message.sharePath || SHARE_BTN_LOCATION.WX_CG_CAP_MENU_PATH }
  const shareInfo = getSharePathInfo(payload, userId, path, message.sharePage || SHARE_BTN_LOCATION.WXCGCAPMENU_PAGE)
  const oParem: any = {}
  if (message.active_id || message.active_code) {
    oParem.active_id = message.active_id
    oParem.active_code = message.active_code
  }
  completeShare(SHARE_CHANNEL.SHARE_WECHAT_FRIEND, shareInfo, 1, oParem)
  return getShareInfo({
    title: '',
    desc: '好友邀请您加入鱼泡',
    path: shareInfo.path,
    from,
  })
}

// 删除指定参数的函数
export const removeUrlParameter = (url, parameter) => {
  const urlParts = url.split('?')
  const [base, ...rest] = urlParts
  const queryPart = rest.length ? rest.join('?') : ''
  if (urlParts.length >= 2) {
    const prefix = `${encodeURIComponent(parameter)}=`
    const params = queryPart.split(/[&;]/g)

    // 循环遍历参数数组，删除指定参数
    for (let i = params.length; i-- > 0;) {
      if (params[i].lastIndexOf(prefix, 0) !== -1) {
        params.splice(i, 1)
      }
    }

    // 重新拼接 URL
    return base + (params.length > 0 ? `?${params.join('&')}` : '')
  }

  return url
}

export const replaceUrlParameter = (url, parameter) => {
  const [urlHost, urlParams] = url.split('?')
  if (urlParams) {
    const params = urlParams.split('&')
    for (let i = params.length; i-- > 0;) {
      const replaceKey = Object.keys(parameter).find((key) => params[i] == key)
      if (replaceKey) {
        params.splice(i, 1, `${replaceKey}=${parameter[replaceKey]}`)
        parameter[replaceKey] = undefined
      }
    }
    Object.keys(parameter).forEach((key) => {
      params.push(`${key}=${parameter[key]}`)
    })
    console.log(params)
    return `${urlHost}?${params.join('&')}`
  }
  return url
}

// 来源对应的地址
const UrlTypeData = {
  // 职位更换主叫号码
  19: 'security-number/safe',
  // 简历更换主叫号码
  20: 'security-number/index',
}

// web扫码落地解析参数
export const parseWebShortToken = async function (option) {
  const { scene } = option
  if (!scene) return {}
  const shortToken = decodeURIComponent(scene)
  let token = ''
  const res = await wx.$.javafetch['POST/account/v1/guide2MiniProgramAuth/getPCToken']({ shortToken }, { hideMsg: true }).catch(err => {
    return err
  })
  const { code, data } = res || {}
  const { authId, token: resToken } = data || {}
  if (code == 0) {
    token = resToken
    const resB = await wx.$.javafetch['POST/account/v1/guide2MiniProgramAuth/checkNextStep']({ shortToken, authId }, { headers: { token } })
    const { code: codeB } = resB || {}
    if (codeB == 0) {
      await dispatch(actions.userActions.setWebTokenData({
        isWebFirmAuth: true,
        token,
        authId,
        webFromData: {},
        shortToken,
      }))
      const tokenArr = shortToken.split(':')
      if (wx.$.u.isArrayVal(tokenArr, 2)) {
        const url = UrlTypeData[tokenArr[1]]
        return url ? { url, isLogin: true, isWebFirmAuth: true } : {}
      }
    }
  }
  wx.$.r.replace({ path: '/subpackage/member/webFirmAuth/index', query: { isExpired: true } })
  return {}
}

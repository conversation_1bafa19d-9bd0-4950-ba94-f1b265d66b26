<!-- ------ 页面头部 -->
<header total="{{resumeSubUuids.length}}" currentIndex="{{resumeSubUuids.length?swiperIdx+1:0}}" showBankIcon="{{miniQualityWorkerEnterId!=1}}" />

<!-- ------ 今日推荐 背景图 -->
<view class="today-recommend-bg" />

<!-- ------ 优质师傅轮播图 -->
<swiper wx:if="{{resumeSubUuids.length}}" bind:change="onToSwiper" current="{{swiperIdx}}" indicator-dots="{{indicatorDots}}" snap-to-edge="true" previous-margin="36rpx" next-margin="{{ENV_IS_WEAPP&&swiperIdx===0&&resumeSubUuids.length>1?'0':'36rpx'}}" class="swiper">
  <swiper-item wx:for="{{resumeSubUuids}}" wx:for-index="idx" wx:key="id"
    bind:touchstart="touchStart" bind:touchend="touchEnd"
    class="{{ENV_IS_WEAPP&&swiperIdx===0&&resumeSubUuids.length>1 ? 'padding-left' : ''}} {{ENV_IS_WEAPP&&swiperIdx===resumeSubUuids.length-1&&resumeSubUuids.length>1 ? 'padding-right' : ''}} {{ENV_IS_WEAPP&&swiperIdx===0&&idx===1?'padding-left0':''}}"
  >
    <view class="card-item">
      <skeleton wx:if="{{!workerInfoArr[idx]}}" />
      <card-info wx:else item="{{workerInfoArr[idx]}}" bind:onComplain="onComplain" />
    </view>
  </swiper-item>
</swiper>
<!-- ------ 无数据的情况 -->
<view wx:elif="{{!isPageFirstLoading&&resumeSubUuids.length===0}}" class="empty-box">
  <image class="empty-img" src="https://staticscdn.zgzpsjz.com/miniprogram/images/wyl/yupao_mini_ywug33x.png" lazy-load />
  <view class="empty-text">
    <text>当前暂无适合您的牛人推荐，点击</text>
    <text class="spe">【更多牛人】</text>
    <text>去浏览其他优质牛人</text>
  </view>
  <view bind:tap="viewMoreWorkers" class="empty-btn">更多牛人</view>
</view>

<!-- ------ 底部联系按钮 -->
<view wx:if="{{!isPageFirstLoading&&workerInfoArr[swiperIdx]&&!swiperIdxWorkerInfo.hide}}" class="footer" >
  <view wx:if="{{resumeSubUuids.length<=1}}" bind:tap="viewMoreWorkers" class="footer-btn white">更多牛人</view>
  <view wx:elif="{{resumeSubUuids.length-1==swiperIdx}}" bind:tap="viewMoreWorkers"  class="footer-btn white">更多牛人</view>
  <view wx:else bind:tap="viewNextWorkerInfo" class="footer-btn white">下一个</view>

  <view bind:tap="onCallPhone" class="footer-btn {{swiperIdxWorkerInfo.isFindWork?'have-find-work':''}}">
    <icon-font type="yp-icon_call_12px" size="40rpx" color="#FFF" />
      <text wx:if="{{swiperIdxWorkerInfo.isFindWork}}" class="text">已找到</text>
    </block>
    <text wx:else class="text">
      <block wx:if="{{!login}}">免费联系</block>
      <!-- 已经查看过电话了 -->
      <block wx:elif="{{swiperIdxWorkerInfo.hasShowPhone}}">拨打电话</block>
      <block wx:else>{{swiperIdxWorkerInfo.isFreeCall?'免费联系':'马上联系'}}</block>
    </text>
    <yp-badge wx:if="{{swiperIdxWorkerInfo.freeCallNum>1}}" text="{{swiperIdxWorkerInfo.freeCallNum}}次" />
  </view>
</view>

<!-- ------ 向左滑手势动效 -->
<view wx:if="{{!showMiddleVisible&&goodWorkers.showGestureLeft&&resumeSubUuids.length>1}}" bind:tap="onCloseGestureLeft" class="gesture-bg">
  <image class="arrow-icon arrow-left-icon" src="https://cdn.yupaowang.com/yp_mini/images/zddq/gesture_left_arrow.png" />
  <image class="hand-icon arrow-left-hand" src="https://cdn.yupaowang.com/yp_mini/images/zddq/gesture_hand.png" />
  <view class="tip-title">向左滑动</view>
  <text class="tip-desc">查看下一条优质牛人信息</text>
</view>

<!-- ------ 向右滑手势动效 -->
<view wx:if="{{!showMiddleVisible&&goodWorkers.showGestureRight&&resumeSubUuids.length>=2&&swiperIdx==2}}" bind:tap="onCloseGestureRight" class="gesture-bg">
  <image class="arrow-icon arrow-right-icon" src="https://cdn.yupaowang.com/yp_mini/images/zddq/gesture_right_arrow.png" />
  <image class="hand-icon arrow-right-hand" src="https://cdn.yupaowang.com/yp_mini/images/zddq/gesture_hand.png" />
  <view class="tip-title">向右滑动</view>
  <text class="tip-desc">查看上一条优质牛人信息</text>
</view>
<custom-header title="举报"></custom-header>
<view class="body">
   <view class="tiele">选择举报原因<text>（必选）</text></view>
   <view class="radio">
     <view wx:for="{{options}}" wx:key="value" class="item" bind:tap="onClick" data-item="{{item}}">
        <icon-font type="yp-icon_pay_y" size="40rpx" color="#0092FF" wx:if="{{item.value == value}}" />
        <icon-font type="yp-icon_pay_n" size="40rpx" custom-class="icon" color="rgba(233, 237, 243, 1)" wx:if="{{item.value != value}}" />
        <view class="label">{{item.label}}</view>
     </view>
   </view>
   <view class="tiele">举报内容<text>（必填）</text></view>
   <view class="remarks" bind:tap="onTap">
      <textarea
       bind:input="onTextChange"
       maxlength="{{maxlength}}"
       cursor-spacing="{{50}}"
       disable-default-padding
       focus="{{focus}}"
       bind:blur="onBlur"
       disabled="{{inputDisabled}}"
       placeholder-style="color: rgba(0, 0, 0, 0.45);font-size: 30rpx;"
       class="remark-content"
       placeholder="请简要描述你的问题与意见，以便我们提供更好的帮助"
       value="{{remarks}}"/>
      <view class="length">
        {{remarks.length}} / {{maxlength}}
      </view>
   </view>
   <button class="btn {{!prohibit ? 'disabled' : ''}}" bind:tap="onSubmit">提交</button>
</view>
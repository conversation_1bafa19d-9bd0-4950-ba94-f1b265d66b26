<view class="other-state" wx:if="{{status=='red'}}">
    <view class="msg-red {{margin == 'left'?'mr-l':margin == 'right'?'mr-r':''}}" ></view>
</view>
<view class="other-state" wx:elif="{{status=='fail'}}" >
    <image class="send-fail {{margin == 'left'?'mr-l':margin == 'right'?'mr-r':''}}" catch:tap="onStatusClick" data-type="resend" src="./yp_mini_send_fail.png" />
</view>
<view class="other-state img-v" wx:elif="{{isMid && status=='sending'}}">
    <image class="sending2 animg" src="./yp_mini_shjz2.png"  />
</view>
<view class="other-state" wx:elif="{{status=='sending'}}">
    <image class="sending animg {{margin == 'left'?'mr-l':margin == 'right'?'mr-r':''}}" src="./yp_mini_shjz.png"  />
</view>
<view class="msg-success {{margin == 'left'?'mr-l':margin == 'right'?'mr-r':''}}" wx:elif="{{status=='success'}}">
    <text>{{msgInfo.isPeerRead?'已读':'送达'}}</text>
</view>
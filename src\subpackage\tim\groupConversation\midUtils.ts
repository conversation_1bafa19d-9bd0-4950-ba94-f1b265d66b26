import { storage, store } from '@/store/index'
import { getGroupInfo } from './operUtils'
import { getRdTodayPopkey } from '@/utils/helper/dialog/index'

/** 老板查看工人名片，拨打电话 */
function laobanBaDaGongrenPhone(targetId) {
  const { conversation } = this.data
  const { relatedJobId } = conversation || {}
  // relatedJobId
  wx.$.l.resumeMidTelV3.call(this, { uuid: targetId, scene: 1, sceneV2: '21', jobId: relatedJobId }, { pageName: '会话详情', loading: true }, {
    callPopBack: (pop) => {
      wx.$.resumeMidModel({
        ...pop,
        zIndex: 10011,
        source: '2',
        infoId: targetId,
        pageName: '会话详情',
        call: (e) => {
          const { detail } = e || {}
          if (detail == 2) {
            const showRealTelState = storage.getItemSync('showRealTelState')
            if (showRealTelState) {
              const currentPage = wx.$.r.getCurrentPage()
              storage.setItemSync('showRealTel', currentPage.route)
            }
          }
          this.onCallMidPhone(e)
        },
      })
    },
  })
}

/** 拨打中间号====== */
export async function callMidPhone() {
  const { conversation } = this.data
  imCallPop.call(this, conversation)
}

export async function imCallPop(conversation) {
  const { telRightsInfo, toUserId } = conversation
  if (telRightsInfo.telType == 1) { // 直拨
    if (telRightsInfo.infoType == 1) {
      handleRecruitPhone.call(this, conversation)
    } else {
      laobanBaDaGongrenPhone.call(this, telRightsInfo.infoId)
    }
  } else if (telRightsInfo.telType == 2) { // 加急拨
    const telPrePopupList = getRdTodayPopkey('expeditedDial')
    const popupList = telPrePopupList
    wx.$.l.recallV3(telRightsInfo.infoId, toUserId, false, {
      pageName: '联系记录',
      infoId: telRightsInfo.infoId,
      source: 2,
    }, telRightsInfo.infoType != 1, 1, 1, popupList)
  } else if (telRightsInfo.telType == 3) { // 回拨
    const params = {
      toUserId,
      infoId: telRightsInfo.infoId,
      infoType: telRightsInfo.infoType,
      getPrivacyTel: false,
    }
    wx.$.l.recall(params, {
      pageName: '联系记录',
      infoId: telRightsInfo.infoId,
      source: 2,
    })
  }
}

/** 拨打信息中的中间号 */
export async function callMsgMidPhone() {
  const { userBaseObj } = store.getState().user.userInfo || {}

  if (!userBaseObj || !userBaseObj.userTelObj || !userBaseObj.userTelObj.showTel) {
    this.binnedPhoneType = 'msg'
  }
  const { userImAcc } = store.getState().storage
  const { conversation } = this.data
  const { infoDetail, fromUserImId } = conversation || {}
  const { infoType, infoId, relatedInfoId } = infoDetail || {}
  if (fromUserImId == userImAcc && infoType == 1) {
    handleRecruitPhone.call(this, conversation)
    return
  }
  // 自己发起的找活聊一聊，拨打电话逻辑走java接口
  if (fromUserImId == userImAcc && infoType == 2) {
    laobanBaDaGongrenPhone.call(this, Number(relatedInfoId) ? relatedInfoId : infoId)
  }
}

// 招工并且是自己发起，拨打电话走详情的逻辑
export function handleRecruitPhone(conversation, after_refund?) {
  const { infoDetail } = conversation || {}
  const { infoId, relatedInfoId } = infoDetail || {}
  const param: any = {
    jobId: Number(relatedInfoId) ? relatedInfoId : infoId,
    scene: 4,
    isPrivacy: true,
    lookType: 1,
    hasPopRefundTip: !!after_refund,
  }
  wx.$.l.recruitTelChat(param, {
    query: {
      pageCode: this.data.pageCode,
    },
    midext: {
      infoId,
      source: 1,
      pageName: '聊一聊',
    },
  })
}

/**
 * 判断积分消耗提醒
 * @param paramGroupInfo 群组对象
 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export function jugeIntConRemind(paramGroupInfo = null, ext: any = {}) {
  const { type, chatsend } = ext
  const conversation = paramGroupInfo || this.data.conversation
  const { rightsStatusInfo } = conversation || {}
  const { canCall, sendMsg } = rightsStatusInfo || {}
  const handleRes = type == 'call' ? canCall : sendMsg
  const { value, forbiddenMsg, hasImRight } = handleRes || {}
  if (!value) {
    if (forbiddenMsg && hasImRight == 1) {
      wx.$.msg(forbiddenMsg)
      return false
    }
    handleAction.call(this, conversation, { chatsend, type })
    return false
  }

  return true
}

// 判断积分消耗提醒通用逻辑
export function handleAction(conversation, ext?) {
  const { infoDetail, relatedJobId } = conversation || {}
  const { infoType, infoId } = infoDetail || {}
  const { relatedInfoId } = infoDetail || {}
  const uuid = Number(relatedInfoId) ? relatedInfoId : infoId
  const { type, chatsend } = ext || {}
  if (infoType == 2) {
    if (type == 'msg') {
      wx.showLoading({ title: '请求中...', mask: true })
      wx.$.l.getImChatPre({ uuid, scene: 1, sceneV2: '21', jobId: relatedJobId || '' }).then(async (sucRes) => {
        const { query } = this.data
        const { relatedJobId: nRelatedJobId, isChatSearchPurchase } = sucRes || {} as any
        await wx.$.l.initGroup(uuid, 2, { relatedInfoId: nRelatedJobId || (relatedJobId && relatedJobId != '0' ? relatedJobId : ''), fromType: isChatSearchPurchase ? 10 : '' })
        await getGroupInfo.call(this, query, true)
        wx.hideLoading()
        chatsend && chatsend()
      }).then(() => {
        wx.hideLoading()
      })
    } else {
      resumesDeduction.call(this, conversation, ext)
    }
  } else {
    reDeduction.call(this, conversation, ext)
  }
}

// 找活重新扣费
export async function resumesDeduction(conversation, ext?) {
  const { chatsend } = ext || {}
  const { infoDetail, relatedJobId } = conversation || {}
  const { infoType, infoId, relatedInfoType, relatedInfoId } = infoDetail || {}
  const uuid = Number(relatedInfoId) ? relatedInfoId : infoId
  const type = Number(relatedInfoId) ? relatedInfoType : infoType
  if (uuid > 0 && type == 2) {
    wx.$.l.resumeTelV3({ uuid: `${uuid}`, isPopup: 0, scene: 1, sceneV2: '21', jobId: relatedJobId }, { pageName: '会话详情' })
      .then(async (res) => {
        if (!res.data) {
          return
        }
        const { query } = this.data
        await wx.$.l.initGroup(uuid, 2)
        await getGroupInfo.call(this, query, true)
        chatsend && chatsend()
      })
      .catch(() => {
        wx.$.msg('解锁失败,请稍后重试')
      })
  }
}

// 招工重新扣费
export async function reDeduction(conversation, ext?) {
  const { chatsend } = ext || {}
  const { infoDetail } = conversation || {}
  const { infoType, infoId, relatedInfoType, relatedInfoId } = infoDetail || {}
  const jobId = Number(relatedInfoId) ? relatedInfoId : infoId
  const type = Number(relatedInfoId) ? relatedInfoType : infoType
  if (jobId > 0 && type == 1) {
    const { query } = this.data
    const param: any = {
      jobId,
      scene: 4,
      lookType: 2,
      isPrivacy: true,
      hasPopRefundTip: true,
    }
    wx.$.l.recruitTelChat(
      param,
      {
        query: {
          pageCode: this.data.pageCode,
        },
      },
      {
        chatCall: async () => {
          await wx.$.l.initGroup(jobId, 1)
          await getGroupInfo.call(this, query, true)
          if (conversation.rightsStatusInfo.isPopover) {
            wx.$.alert({
              title: '解锁成功',
              content: '聊天室已解锁，您可以和老板继续聊天了',
            })
          } else {
            chatsend && chatsend()
          }
        },
      },
    )
  }
}

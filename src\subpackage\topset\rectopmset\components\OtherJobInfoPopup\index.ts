import { MapStateToData, connect } from '@/store/index'

const mapStateToData: MapStateToData = (state) => {
  return {}
}

/**
 * 其它招工信息展示弹窗
 */
Component(connect(mapStateToData)({
  // 组件的属性列表
  properties: {
    // 组件是否展示
    show: { type: Boolean, value: false },
    recommendList: { type: Array, value: [] },
    // 是否是手动打开其他信息的弹窗
    isHandleChoose: { type: Boolean, value: false },
  },
  // 监听：如果展示弹窗，则开始加载数据，如果已经加载过，则直接展示
  observers: {
  },
  // 组件的初始数据
  data: {},
  methods: {
    //
    onClosePopup() {
      if (this.data.isHandleChoose) {
        this.triggerEvent('close', {})
        return
      }

      wx.$.r.back()
    },
    /** 到我的招工列表 */
    toPublished() {
      wx.$.r.replace({ path: '/subpackage/recruit/published/index' })
    },
    /** 置顶 */
    setTop(e) {
      const { item } = e.detail
      this.triggerEvent('setTop', { item })
    },
    /** */
    touchMove() {

    },
  },
}))

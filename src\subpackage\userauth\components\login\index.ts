/*
 * @Date: 2022-01-25 10:28:45
 * @Description: 公共登录组件，只是作为桥接
 */

import { storage } from '@/store/index'

Component({
  // 组件的属性列表
  properties: {
    // 判断是否为页面
    isPage: {
      type: Boolean,
      value: false,
    },
    sunCodeToWebAuth: {
      type: String,
      value: '',
    },
  },
  data: {
    // 微信登录认证类型 默认为1为快捷登录，2为手机号登录
    authType: 0,
  },
  lifetimes: {
    ready() {
      const { auth_type } = wx.$.r.getQuery()
      this.setData({
        authType: Number(auth_type) || 1,
      })
    },
  },
  methods: {
    // 为组件时登录回调
    onLogin() {
      this.triggerEvent('login')
    },
  },
})

/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 普通招工详情，底部tabbar
 */

Component({
  data: {
    EmojiList: [],
    obem: {
    },
    // 默认显示区域高度 即:删除按钮到scroll-view顶部的高度
    cHeight: 160,
    emojiHeight: 28.5,
    isOK: false,
  },
  lifetimes: {
    async ready() {
      const EmojiList = await wx.$.l.EmojiData()
      this.setData({ EmojiList })
      this.onJt()
    },
  },
  methods: {
    onEmojiClick(e) {
      const { obem } = this.data
      const { em, index } = e.currentTarget.dataset
      if (obem[index] && obem[index].opacity <= 0) {
        return
      }
      if (em) {
        this.triggerEvent('emojiClick', { em })
      }
    },
    onEmojiDel() {
      this.triggerEvent('emojidel')
    },
    onScroll(event) {
      const { scrollTop } = event.detail
      this.updateImageOpacity(scrollTop)
    },
    updateImageOpacity(scrollTop) {
      const { obem, emojiHeight, cHeight } = this.data
      const kys = Object.keys(obem || {})
      const nObem = { ...obem }
      kys.forEach((ky) => {
        const item = { ...(obem[ky] || {}) }
        const { imageTop } = item || {}
        let opacity = ((scrollTop + cHeight + (emojiHeight / 2)) - imageTop) / emojiHeight
        opacity >= 1 && (opacity = 1)
        opacity <= 0.5 && (opacity = 0)
        opacity > 0.5 && opacity < 1 && (opacity -= 0.5)
        opacity < 0 && (opacity = 0)
        nObem[ky] = { ...item, opacity }
      })
      this.setData({ obem: nObem })
    },

    onJt() {
      wx.createSelectorQuery().in(this).select('.emoji')
        .boundingClientRect((rect) => {
          const { top, height } = rect || {}
          wx.createSelectorQuery().in(this)
            .select('.emoji-btn-v')
            .boundingClientRect((btnRect) => {
              const { height: btnHeigth } = btnRect || {}
              const cHeight = height - btnHeigth
              wx.createSelectorQuery().in(this)
                .selectAll('.emoji-img')
                .boundingClientRect((rects: any) => {
                  const columnsPerRow = 8
                  const totalEmojis = rects.length
                  const rows = Math.ceil(totalEmojis / columnsPerRow)
                  const obem: any = {}
                  let emojiHeight = 0
                  for (let i = 0; i < rows; i++) {
                    if (totalEmojis - i * columnsPerRow >= columnsPerRow - 1) {
                      const item = rects[i * columnsPerRow + columnsPerRow - 2] // 倒数第二列
                      const { dataset, top: iTop, height: iHeight } = item || {}
                      if (!emojiHeight) {
                        emojiHeight = iHeight
                      }
                      const { index } = dataset || {}
                      const imageTop = iTop - top
                      if (imageTop >= cHeight) {
                        obem[index] = { imageTop, opacity: 0 }
                      }
                    }
                    if (totalEmojis - i * columnsPerRow >= columnsPerRow) {
                      const item = rects[i * columnsPerRow + columnsPerRow - 1]// 倒数第一列
                      const { dataset, top: iTop, height: iHeight } = item || {}
                      if (!emojiHeight) {
                        emojiHeight = iHeight
                      }
                      const { index } = dataset || {}
                      const imageTop = iTop - top
                      if (imageTop >= cHeight) {
                        obem[index] = { imageTop, opacity: 0 }
                      }
                    }
                  }
                  this.setData({ obem, cHeight, emojiHeight, isOK: true })
                })
                .exec()
            })
            .exec()
        })
        .exec()
    },
  },
})

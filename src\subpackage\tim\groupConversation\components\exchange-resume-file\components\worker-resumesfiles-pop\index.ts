import { store } from '@/store/index'
import { applyExchange, handleExchange } from '@/subpackage/tim/groupConversation/utils'

Component(class extends wx.$.Component {
  properties = {
    visible: { type: Boolean, value: false },
    fileList: { type: Array, value: [] },
    // 消息ID
    exchangeMsgId: { type: String, value: '' },
    exchangeType: { type: String, value: '' },
  }

  observers = {
  }

  data = {
    suffixImsg: {
      png: 'https://cdn.yupaowang.com/yp_mini/images/xjj/yp-min-im-png.png',
      pdf: 'https://cdn.yupaowang.com/yp_mini/images/xjj/yp-min-im-pdf.png',
      doc: 'https://cdn.yupaowang.com/yp_mini/images/xjj/yp-min-im-doc.png',
      docx: 'https://cdn.yupaowang.com/yp_mini/images/xjj/yp-min-im-docx.png',
      jpg: 'https://cdn.yupaowang.com/yp_mini/images/xjj/yp-min-im-jpg.png',
      other: 'https://cdn.yupaowang.com/yp_mini/images/xjj/yp-min-im-pther.png',
    },
  }

  async onFileClick(e) {
    await wx.$.u.waitAsync(this, this.onFileClick, [e], 1000)
    const { exchangeMsgId, exchangeType } = this.data as any
    const { file } = e.currentTarget.dataset
    const { uuid } = file || {}
    if (!uuid) {
      wx.$.msg('附件异常,请稍后重试')
      return
    }
    if (exchangeMsgId) {
      handleExchange({ exchangeMsgId, exchangeType: exchangeType || 'EXCHANGE_RESUME_FILE', agree: true }, { resumeFileId: uuid }, {
        success: () => {
          this.onClose()
        },
      })
    } else {
      const { conversation } = store.getState().timmsg
      const { conversationId } = conversation || {}
      if (!conversationId) {
        wx.$.msg('会话异常,请稍后重试').then(() => {
          wx.$.r.back()
        })
        return
      }

      applyExchange('EXCHANGE_RESUME_FILE', { resumeFileId: uuid }, {
        success: () => {
          this.onClose()
        },
      })
    }
  }

  onClose() {
    this.triggerEvent('close')
  }

  async onPreview(e) {
    await wx.$.u.waitAsync(this, this.onPreview, [e], 1000)
    const { file } = e.currentTarget.dataset
    const { fileName, fileUrl } = file
    wx.$.l.attachmentPath({
      fileName,
      fileUrl,
      generateType: 3,
    })
  }

  async onManFile() {
    await wx.$.u.waitAsync(this, this.onManFile, [], 1000)
    this.onClose()
    wx.$.r.push({ path: '/subpackage/attachment-resume/manages/index' })
  }
})

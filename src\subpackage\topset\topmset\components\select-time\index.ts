import { connect, MapStateToData } from '@/store/index'
import dayjs from '@/lib/dayjs/index'
import { getUpgradeExpireTime } from './utils'

const mapStateToData: MapStateToData = (state) => {
  const { checkStatus } = state.storage.myResumeDetails.basicResp || {}

  return { checkStatus }
}

/** @name 请选择加急展示事件组件 */
Component(connect(mapStateToData)({
  properties: {
    // 用户加急订单信息
    upgradeOrderInfo: { type: Object, value: {} },
    // 全局配置信息
    topConfig: { type: Object, value: { topRules: [], daysArr: [] } },
    // 监听form选项
    form: { type: Object, value: { city: [], time: '', money: 0 } },
    // 是否修改了加急展示地区
    isModTopArea: { type: Boolean, value: false },
  },
  data: {
    // 是否为修改延长时间状态
    isModUpgradeTimeStatus: false,
    // 置顶结束日期
    topEndTimeStr: '',
    // 当前延长的 天数 对象
    active: {},
    /** 当前选中项的index值 */
    value: '',
    /** 已置顶的选择后生成的新的时间字符串，结构如下：2022-02-24 16:59 */
    newTime: '',
  },
  observers: {
    topConfig() {
      this.initSelectTimeData()
    },
    active(newVal, oldVal) {
      this.activeChange()
    },
    // 加急过期弹窗 点击 修改加急按钮 展示修改状态 UI
    showModUpgradeUI(bool) {
      if (bool) {
        // 显示成功后触发 initShowModUpgradeUI 重新加急->展示修改加急 UI
        this.triggerEvent('initShowModUpgradeUI')
      }
    },
  },

  methods: {
    // 初始化加急找活 加急时间 默认选择天数 消耗积分
    initSelectTimeData() {
      const { upgradeOrderInfo, topConfig } = this.data
      const { topEndTime } = upgradeOrderInfo
      const topEndTimeStr = dayjs(topEndTime).format('YYYY.MM.DD HH:mm')
      // 已置顶或无置顶信息则退出
      if (upgradeOrderInfo.topStatus == 2 || upgradeOrderInfo.topStatus == 4 || !topConfig.daysArr) {
        this.setData({ topEndTimeStr })
        return
      }

      this.initExtendedTimeAndDate({ topEndTimeStr })
      if (ENV_IS_SWAN) {
        this.activeChange()
      }
    },
    // 取消修改到期时间修改状态
    cancelModUpgradeTime() {
      this.setData({ isModUpgradeTimeStatus: false, active: {}, value: '', newTime: '' })
    },
    modUpgradeTime(e) {
      // 修改到期时间
      this.initExtendedTimeAndDate({ isModUpgradeTimeStatus: true })
      this.onChangeUpgradeTime(e)
    },
    // 初始化延长时间
    initExtendedTimeAndDate(otherSetData = null) {
      const { daysArr, defaultDays } = this.data.topConfig
      const findIdx = daysArr.findIndex((it) => it.value == defaultDays)
      if (findIdx === -1) {
        return
      }

      const active = daysArr[findIdx] || {}
      const oneDayTime = 1 * 24 * 60 * 60 * 1000
      const newTime = dayjs(new Date().getTime() + oneDayTime).format('YYYY.MM.DD HH:mm')
      const data = otherSetData?.isModUpgradeTimeStatus ? {} : { active }
      this.setData({ ...data, value: findIdx, newTime, ...otherSetData })
    },
    // 调整最新置顶时间
    onChangeUpgradeTime({ detail: { value } }) {
      const { upgradeOrderInfo, topConfig } = this.data
      const active = topConfig.daysArr[value] || {}
      this.setData({ active, value: String(value), newTime: getUpgradeExpireTime(upgradeOrderInfo, active.value) })
    },

    // active值发生了修改时执行
    activeChange() {
      const { form, active, value, topConfig } = this.data
      this.triggerEvent('setFormData', { form: { ...form, time: active.value || 0 } })
      // 判断是否是内部操作值
      if (!active.value || (value && topConfig.daysArr[value].value === active.value)) {
        return
      }

      // 同步value值
      for (let i = 1; i < topConfig.daysArr.length; i += 1) {
        if (topConfig.daysArr[i] === active.value) {
          this.setData({ value: i })
          return
        }
      }
    },
  },
}))

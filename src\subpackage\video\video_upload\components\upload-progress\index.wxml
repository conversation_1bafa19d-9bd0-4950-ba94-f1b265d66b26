<view class="upload-progress-component">
  <view style="margin-top:{{state === 0 ? '80rpx':'50rpx'}}">
    <!-- 用于canvas占位 -->
    <!-- <view class="canvas-wrap" /> -->
    <!-- <canvas wx:if="{{state !== 2}}" class="upload-progress-canvas" canvas-id="upload-progress-canvas" id="upload-progress-canvas" type="2d"></canvas> -->
    <image
      wx:if="{{state == 0}}"
      class="upload-load"
      src="https://cdn.yupaowang.com/yp_mini/images/jl/yp-mini_uploading.png"
    />
    <icon-font wx:if="{{state == 1}}" type="yp-yp_mini_tan" size="164rpx" color="#F74742" />

    <image wx:if="{{state === 2}}" class="success-icon" src="https://staticscdn.zgzpsjz.com/miniprogram/images/tqm/yp_mini_icon_pay.png" />
  </view>
  <view wx:if="{{state === 0}}" class="upload-state-text">视频上传中，请勿离开...</view>
  <view wx:if="{{state === 1}}" class="upload-state-text error">视频上传失败，请重新上传</view>
  <view wx:if="{{state === 2}}" class="upload-state-text success">视频上传完成</view>
  <view class="upload-tips-wrap" style="margin-top:{{state === 0 ? '120rpx':'40rpx'}}">
    <view class="upload-tips-title" wx:if="{{state !== 2}}">温馨提示</view>
    <view class="upload-tips-content" wx:if="{{state !== 2}}">
      <view class="content-line">上传完成前，请不要退出页面或关闭程序，请保持网络稳定，不要切换程序</view>
      <view class="content-line">提示上传失败时，请点击【继续上传】</view>
    </view>
    <view class="upload-tips-content" wx:if="{{state === 2}}">
      <view class="content-line">您的视频简历将在审核通过后发布，审核详情可在【我的-在线简历】查看！</view>
      <view class="content-line">与老板沟通时，也可直接发送给老板！</view>
    </view>
  </view>
  <view wx:if="{{state === 2}}" class="button-primary-large" bind:tap="onClickSuccess">我知道了</view>
  <view class="action-btn" wx:if="{{state === 1}}">
    <view class="button-ghost" bind:tap="onClickClose">关闭</view>
    <view class="button-primary" bind:tap="onClickRetry">继续上传</view>
  </view>
</view>

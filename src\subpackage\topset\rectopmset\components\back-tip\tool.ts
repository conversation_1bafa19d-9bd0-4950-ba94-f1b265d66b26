import dayjs from '@/lib/dayjs/index'
import { storage } from '@/store/index'

function getRandomNum(min: number, max: number) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

export function getRandomAvatar() {
  const nums = []
  while (nums.length < 5) {
    const randomNum = getRandomNum(1, 30)
    if (!nums.includes(randomNum)) {
      nums.push(randomNum)
    }
  }
  return nums
}

const TOP_USER_NUM_MIN = 12345
const TOP_USER_NUM_MAX = 19876

function updateLocalTopUserNumber() {
  const tmpNum = getRandomNum(TOP_USER_NUM_MIN, TOP_USER_NUM_MAX)
  const outTime = dayjs(dayjs().format('YYYY-MM-DD 24:00:00')).valueOf()
  storage.setItemSync('jobTopRandomUserNumber', tmpNum, { outTime })
  return tmpNum
}

/**
 * 获取今日置顶用户数量
 */
export function getTodayTopUserNumber(isRandom = false) {
  const res = wx.getStorageSync('jobTopRandomUserNumber')
  if (isRandom || !res || !res?.data || typeof res.data !== 'number' || res.data < TOP_USER_NUM_MIN || dayjs().valueOf() > res?.outTime) {
    return updateLocalTopUserNumber()
  }

  return res.data
}

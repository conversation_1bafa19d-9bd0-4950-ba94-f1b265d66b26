/*
 * @Date: 2022-02-09 11:00:45
 * @Description: 找活置顶页面
 * @path-query: origin - 页面来源
 *  origin: factory - 从工厂专区进入到找活置顶页
 */
import { MapStateToData, connectPage } from '@/store/index'

import { fetchResumeExist, isVerifyResumeCardStatusService, refreshMyInfo, statusPopErrJump } from '@/utils/helper/resume/index'
import { getResumeTopConfigInfoApi, getUpgradeOrderInfoApi, toppingResumeInfoApi } from './api-server'
import { onSubmitPreShowModalDialog } from './dialog'
import { calcIntegral, isModTopAreaFn, prePageIsToppingPageAndIsResumeTopping, setDefaultSelectRangeCity, updateFirstSetTopTimeBack } from './utils'
import { getShareInfo, getShareInfoByTypeV1 } from '@/utils/helper/share/index'
import { SHARE_CHANNEL } from '@/config/share'

const mapStateToData: MapStateToData = (state) => {
  const { myResumeDetails, userState } = state.storage
  const { basicResp = {} } = myResumeDetails || {}

  return { myResumeDetails, userState, basicResp, isLogin: userState.login }
}

/**
 * @name 加急找活置顶页面
 */
Page(
  connectPage(mapStateToData)({
    data: {
      // 导航栏标题
      headerTitle: '',
      // 加急权益图片
      urgentImgs: [],
      // 全局配置的找活置顶
      topConfig: {},
      // 加急订单信息
      upgradeOrderInfo: {},
      // 选择的加急范围 加急时间信息
      form: {
        /** 置顶城市 */
        city: [],
        /** 置顶天数，延长天数 */
        time: 0,
        /** 置顶需要的积分 */
        money: 0,
        /** 用于判断用户是否进行了充值操作并充值成功 */
        isRecharge: false,
      },
      isModTopArea: false, // 是否修改了加急展示地区

      /** 修改加急状态->用于调整修改加急信息 */
      showModUpgradeUI: false,
      // 是否展示充值弹窗
      showRechargePopup: false,
      // 充值弹窗头部提示文案数组
      rechargePopupHeaderNodes: '',
    },
    async onLoad() {
      try {
        // wx.showLoading({ title: '加载中', mask: true })
        wx.$.loading()
        const isVerifySuccess = await isVerifyResumeCardStatusService({ isVerifyExist: true })
        if (!isVerifySuccess) {
          return
        }

        // 判断名片是否存在( v4.0.0 提示文案前端写死->具体看需求)
        const resumeExist = await fetchResumeExist(true)

        // 审核失败->前端写死弹窗
        if (resumeExist.checkStatus == 3) {
          statusPopErrJump(resumeExist.checkStatus)
          return
        }

        let tmpUpgradeInfo = null

        tmpUpgradeInfo = await getUpgradeOrderInfoApi()

        // 审核中 且 已预约加急
        if (resumeExist.checkStatus == 1 && tmpUpgradeInfo?.topStatus == 6) {
          wx.hideLoading()
          await wx.$.alert({ content: '您已成功预约加急, 请勿重复提交' })
          wx.$.r.back()
          return
        }

        const hasTopConfigInfo = await this.initUpgradeResumePageInfo()
        if (hasTopConfigInfo) {
          updateFirstSetTopTimeBack(1)
        }
      } catch (error) {
        wx.hideLoading()
      } finally {
        wx.hideLoading()
      }
    },
    /** 生命周期回调—监听页面卸载 */
    onUnload() {
      // 刷新找活详情
      refreshMyInfo('refresh', { isStore: true })
    },

    onPageScroll({ scrollTop }) {
      this.setData({ headerTitle: !scrollTop ? '' : '找活加急' })
    },
    // ------ 页面功能部分
    async initUpgradeResumePageInfo() {
      // 获取加急订单信息
      const upgradeOrderInfo = await getUpgradeOrderInfoApi()
      // 设置默认选中加急范围
      const form = await setDefaultSelectRangeCity(this.data, upgradeOrderInfo)
      // 获取配置信息
      const newSetData = await getResumeTopConfigInfoApi()
      this.setData({ upgradeOrderInfo, form, ...newSetData })
      return newSetData
    },
    // 更新(选择) 加急范围 或 加急时间
    async setFormData({ detail: { form } }) {
      const { upgradeOrderInfo } = this.data
      const isModTopArea = isModTopAreaFn(upgradeOrderInfo.topArea || [], form.city || [])
      try {
        const money = await calcIntegral(form, upgradeOrderInfo)
        this.setData({ form: { ...form, money }, isModTopArea })
      } catch (err) {
        console.error(err)
        this.setData({ form, isModTopArea })
      }
    },
    // 拨打客服电话
    onCallCustomerService() {
      wx.$.u.callCustomerService()
    },
    // 确定加急
    async onSubmit() {
      await wx.$.u.waitAsync(this, this.onSubmit, [], 1000)
      const canRunTopping = await onSubmitPreShowModalDialog(this.data)
      if (canRunTopping) {
        toppingResumeInfoApi.call(this)
      }
    },

    // -------- 重置加急成功展示编辑加急 UI 数据
    initShowModUpgradeUI() {
      this.setData({ showModUpgradeUI: false })
    },

    // -------- 充值弹窗
    // 充值弹窗成功充值反馈
    onRechargeSuccess() {
      const { form } = this.data
      this.setData({ form: { ...form, isRecharge: true }, showRechargePopup: false })
      toppingResumeInfoApi.call(this, { identification: 'recharge' })
    },
    // 充值弹窗默认关闭事件
    onCloseRechargePopup() {
      this.setData({ showRechargePopup: false })
    },

    // -------- 城市选择页==>专用方法
    saveCitys(city: []) {
      this.setFormData({ detail: { form: { ...this.data.form, city } } })
    },
    async onBack() {
      await prePageIsToppingPageAndIsResumeTopping()
    },
    // 分享
    onShareAppMessage(options) {
      // 去分享积分/此卡的h5落地页
      // const path = '/subpackage/web-view/index?url=share-fission'

      const sharePage = 'jiaji/share/page'
      const sharePath = 'jiaji/share/path'

      let equityShareData = {}
      if (options.from == 'button') {
        equityShareData = {
          title: '好友邀请你，一起领求职大礼包！',
          imageUrl: 'https://cdn.yupaowang.com/yupao_mini/yp_mini_share_goods.png',
        }
      }

      const shareInfo = getShareInfoByTypeV1(SHARE_CHANNEL.SHARE_WECHAT_FRIEND, sharePath, sharePage)
      return getShareInfo({ path: `${shareInfo.path}`, ...equityShareData, from: options.from })
    },
  }),
)

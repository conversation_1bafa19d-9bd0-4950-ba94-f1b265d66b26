import dayjs from '@/lib/dayjs/index'
import { toJSON } from '@/utils/tools/formatter/index'
import { assembleID, dateTransformation, judgTimeGt30, jugeShowTime } from './util'
import { actions, dispatch, store } from '@/store/index'
import { CallBackObj, EmojiData, EmojiUrl } from './type.d'

/** 单个处理消息数据
 * @param preMsg 上一个消息数据
 */
export const handleSigleMsg = (nMsg, preMsg?) => {
  if (!nMsg) {
    return []
  }
  const { clientTime, time, from, payload, ID } = nMsg
  let { type } = nMsg // 消息类型
  let pType = 'TimChatMsg' // 消息来源
  const msgList = []
  if (!wx.$.u.isEmptyObject(preMsg)) {
    const start = preMsg.clientTime || Number(preMsg.time)
    const end = clientTime || Number(time)
    if (start && end) {
      const timeIt = jugeShowTime({ start, end, nMsg })
      if (timeIt) {
        msgList.push(timeIt)
      }
    }
  }
  const { userImAcc, userChooseRole } = store.getState().storage
  const { isSdkVer240 } = store.getState().message
  const isSelf = from == userImAcc
  let { data = {} } = payload || {}
  if (!wx.$.u.isEmptyObject(data)) {
    data = toJSON(data)
  }
  let nPayload = { ...(payload || {}), data }
  const { content } = data || {}
  const { subType, text } = content || {}
  let renderDom = []
  if (nMsg.isRevoked) {
    msgList.push(handleRevoked(nMsg))
    return msgList
  } if (nMsg.type === 'TIMTextElem') {
    renderDom = parseText(payload)
  } else if (nMsg.type === 'TIMCustomElem') {
    const noHandle = ['1', '2', '3']
    if (noHandle.includes(`${data.type}`)) { // 不需要额外处理的类型
    } else if (data.type == '11') { // 自定义电话消息
      renderDom = handleChatCallback(data)
    } else if (data.type == '5') {
      if (userImAcc == nMsg.from) {
        type = 'TIMTxtFsHeTongElem'
        pType = 'SystemMsg'
      }
    } else if (data.type == '8') {
      if (!isSdkVer240) {
        type = 'TIMTextElem'
        nPayload.text = '[暂不支持此消息类型，请前往客户端查看]' // 定位
      } else {
        type = 'TIMMapElem'
      }
    } else if (data.type == '6' || data.type == '7') {
      type = 'TIMTextElem'
      nPayload.text = '[暂不支持此消息类型，请前往客户端查看]'
    } else if (data.type == '4') {
      msgList.push(handleSyht(nMsg))
      return msgList
    } else if (data.type == '12') {
      if (data.callType == '1') {
        type = 'TIMTextElem'
        nPayload.text = '[暂不支持此消息类型，请前往客户端查看]'// 语音通话
      } else if (data.callType == '2') type = 'TIMTextElem'
      nPayload.text = '[暂不支持此消息类型，请前往客户端查看]'// 视频通话
    } else if (data.type == '390.1') {
      pType = 'SystemMsg'
      nPayload.content = handleTixingCallback(data, isSelf)
    } else if (data.type == '610.1') { // 交换电话
      if (isSelf) {
        pType = 'SystemMsg'
        if (subType == 1) {
          nPayload.text = '请求交换电话已发送'
        } else if (subType == 2) {
          nPayload.text = '你已经成功拒绝了对方交换电话的请求'
        }
      } else if (subType == 2) {
        pType = 'SystemMsg'
        nPayload.text = text || nPayload.description
      }
    } else if (data.type == '660.1') { // 交微信
      if (isSelf) {
        pType = 'SystemMsg'
        if (subType == 1) {
          nPayload.text = '请求交换微信已发送'
        } else if (subType == 2) {
          nPayload.text = '你已经成功拒绝了对方交换微信的请求'
        }
      } else if (subType == 2) {
        pType = 'SystemMsg'
        nPayload.text = text || nPayload.description
      }
    } else if (data.type == '710.1') {
      if (isSelf) {
        // 您已经成功拒绝了对方发送附件简历的请求
        if (subType == 1 && userChooseRole == 1) {
          pType = 'SystemMsg'
          nPayload.text = '请求附件简历已发送'
        } else if (subType == 2) {
          pType = 'SystemMsg'
          nPayload.text = userChooseRole == 2 ? '你已拒绝向对方发送附件简历' : '你已经成功拒绝了对方发送附件简历的请求'
        }
      } else if (subType == 2) {
        pType = 'SystemMsg'
        nPayload.text = text || nPayload.description
      }
    } else if (data.type == '710.2') {
      pType = 'SystemMsg'
      nPayload.text = text || nPayload.description
    } else if (data.type == '910.1') {
      console.log('data:', data)
    } else if (data.type == '930.1') {
      pType = 'SystemMsg'
      renderDom = handle9301(data)
    } else {
      type = 'TIMTextElem'
      nPayload.text = '[暂不支持此消息类型，请前往客户端查看]'
    }
  }
  if (renderDom.length > 0) {
    nPayload = { ...nPayload, renderDom }
  }
  msgList.push({
    ...nMsg,
    id: assembleID(`${ID}`),
    payload: nPayload,
    isSelf,
    type,
    isAvatar: true,
    time: nMsg.clientTime || Number(nMsg.time),
    pType, // 用户发送的消息
    oId: ID,
  })
  return msgList
}

/** 批量处理消息数据 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export const handleMsgList = (messageList, conversationID, ext?) => {
  let msgList = []
  let isHasOldMsg = false // 是否有30天以前的数据
  if (!messageList || messageList.length == 0) {
    return { msgList }
  }
  const { isNoSaveImg } = ext || {}
  const { curConImages, twoMinutesConvData, messageList: mMessageList } = store.getState().timmsg
  const oldConvData = twoMinutesConvData[conversationID] || []
  let nCurConImages: Array<String> = []
  const oLen = curConImages.length
  messageList.forEach((item, idx) => {
    let list = []
    const { type, isRevoked, payload, clientTime, time } = item || {}
    const isOk = judgTimeGt30(clientTime || Number(time))
    if (!isOk) {
      if (type == 'TIMImageElem' && !isRevoked) {
        const { imageInfoArray = [] } = payload || {}
        if (wx.$.u.isArrayVal(imageInfoArray)) {
          const imgIt = imageInfoArray[0]
          if (!wx.$.u.isEmptyObject(imgIt)) {
            nCurConImages.push(imgIt.url || imgIt.imageUrl)
          }
        }
      }
      let pre = {}
      if (idx > 0) {
        pre = messageList[idx - 1]
      }
      list = handleSigleMsg(item, pre)
      if (mMessageList.length == 0 && oldConvData.length > 0) {
        const oI = oldConvData.find((oitem) => oitem.oId == item.id)
        if (oI) {
          list[list.length - 1] = oI
        }
      }
      msgList = msgList.concat(list)
    } else {
      isHasOldMsg = true
    }
  })
  if (nCurConImages.length > 0 && !isNoSaveImg) {
    if (oLen > 0) {
      nCurConImages = nCurConImages.concat(curConImages)
    }
    dispatch(actions.timmsgActions.setState({ curConImages: nCurConImages }))
  }
  return { msgList, isHasOldMsg }
}

/** 处理系统单聊消息(组装系统单聊消息) */
// eslint-disable-next-line sonarjs/cognitive-complexity
export const getSystemChatMsg = (item) => {
  const { payload } = item || {}
  const { data } = payload || {}
  const customExts = toJSON(data) || {}
  const { content } = customExts || {}
  const { type, subType, subTitle, title, content: description, image } = content || {}
  const ext = content ? toJSON(content.ext) || {} : {}
  const terminalInfo = content ? toJSON(content.terminalInfo) || [] : []

  let message_tabs = {}
  if (terminalInfo && terminalInfo.length > 0) {
    terminalInfo.forEach((citem) => {
      if (citem.terminal == 'wx_mini') {
        const buttonInfo = citem.buttonInfo || {}
        message_tabs = {
          minVersion: citem.minVersion,
          ...buttonInfo,
          type: buttonInfo.jumpType,
          ext,
          reach_msg_id: customExts.reach_msg_id || '',
          tid: customExts.content.tid,
          s_t: customExts.content.s_t,
          epc: customExts.content.epc,
        }
      }
    })
  }

  let { time } = item
  if (`${time}`.length == 10) {
    time *= 1000
  }

  const nItem = {
    nId: `a${item.ID}`,
    id: item.ID,
    type,
    status: subType,
    title: subTitle,
    type_name: title,
    description,
    formal_integral: ext.formal_integral || 0,
    temp_integral: ext.temp_integral || 0,
    job_id: ext.job_id || 0,
    time,
    date_time: dayjs(Number(time)).format('YYYY.MM.DD HH:mm:ss'),
    message_tabs: [message_tabs],
    read_time: item.isRead ? 1 : 0,
    image: image || '',
    ext,

  } as any

  if (nItem.type == 126 && nItem.status == 150) {
    nItem.customTitle = subTitle.replace(ext.reasonInfo, `<span style="color: #F74742"> ${ext.reasonInfo} </span>`)
    nItem.customTitle = nItem.customTitle?.replace(ext.limitTime, `<span style="color: #F74742">${ext.limitTime}</span>`)
    nItem.customTitle = `<div style="font-weight: bold">${nItem.customTitle}</div>`
  }
  return nItem
}

/** 回调单聊信息组装系统消息信息 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export const setSystemMsgByCallBackMsg = async (systemSingleChat, chatMsg: any = {}) => {
  const nSystemSingleChat: any = {
    ...systemSingleChat,
    timestamp: chatMsg.time || 0,
  }
  nSystemSingleChat.message_time = dateTransformation(chatMsg.time)
  const { payload } = chatMsg
  const { data } = payload || {}
  const customExt = toJSON(data) || {}
  const ext = toJSON(customExt.ext) || {}
  const constent: any = toJSON(customExt.content) || {}
  if (customExt.type == 15 && ext && ext.length > 0) {
    const contentArr = constent.content?.split('{message_replace}') || []
    contentArr.forEach((item, i) => {
      nSystemSingleChat.desc = `${nSystemSingleChat.desc}${item}${ext[i]?.content}`
    })
  } else {
    nSystemSingleChat.desc = constent.content
  }
  if (!nSystemSingleChat.desc) {
    return null
  }
  const { conversationID } = systemSingleChat || {}
  const converRes = await wx.$.tim.getConversationList([conversationID])
  const { data: cData } = converRes
  const { conversationList } = cData || {}
  if (conversationList && conversationList.length) {
    const conver = conversationList[0] || {}
    nSystemSingleChat.show_number = conver.unreadCount || 0
  }
  return nSystemSingleChat
}

export const DefaultMsg = '[暂不支持此消息类型，请前往客户端查看]'

/** 处理c端930.1的消息 */
export const handle9301 = (data) => {
  console.log('data:', data)
  const { content } = data || {}
  const { height, content: txt } = content || {}
  const renderDom = []
  renderDom.push({
    name: 'text',
    text: txt.replace(height, ''),
  })
  renderDom.push({
    name: 'btn',
    text: height,
  })
  return renderDom
}

// 处理撤回消息数据
export const handleRevoked = (nMsg) => {
  const { userImAcc } = store.getState().storage
  const { ID, from, payload } = nMsg || {}
  const { text } = payload || {}
  const isSelf = from == userImAcc
  return {
    ...nMsg,
    id: assembleID(`${ID}`),
    payload: { text: isSelf ? '你撤回了一条消息' : '对方撤回了一条消息', otext: text },
    isSelf,
    isAvatar: false,
    time: nMsg.clientTime || Number(nMsg.time),
    pType: 'SystemMsg', // 系统消息
    oId: ID,
  }
}

/** 处理提醒消息 */
export const handleTixingCallback = (data, isSelf) => {
  const { content } = data || {}
  const { from, to } = content || {}
  const msgs = []
  let obj
  if (isSelf) {
    obj = toJSON(from)
  } else {
    obj = toJSON(to)
  }
  if (obj.content && obj.content.length) {
    obj.content.forEach((item) => {
      msgs.push({ ...item, type: 'text' })
    })
  }
  return msgs
}

/** 解析文本中的表情 */
const parseText = (payload) => {
  const renderDom = []
  const { text = '' } = payload || {}
  // 文本消息
  let temp = text
  let left = -1
  let right = -1
  // 判断是否有表情
  let isEmoji = false
  while (temp !== '') {
    left = temp.indexOf('[')
    right = temp.indexOf(']')
    switch (left) {
      case 0:
        if (right === -1) {
          renderDom.push({
            name: 'text',
            text: temp,
          })
          temp = ''
        } else {
          const emojiR = temp.slice(0, right + 1)
          const emojiIndex = EmojiData.indexOf(emojiR)
          if (emojiIndex >= 0) {
            // 如果您需要渲染表情包，需要进行匹配您对应[呲牙]的表情包地址
            renderDom.push({
              name: 'img',
              src: `${EmojiUrl}${emojiIndex}.png`,
            })
            temp = temp.substring(right + 1)
            isEmoji = true
          } else {
            renderDom.push({
              name: 'text',
              text: '[',
            })
            temp = temp.slice(1)
          }
        }
        break
      case -1:
        renderDom.push({
          name: 'text',
          text: temp,
        })
        temp = ''
        break
      default:
        renderDom.push({
          name: 'text',
          text: temp.slice(0, left),
        })
        temp = temp.substring(left)
        break
    }
  }
  if (isEmoji) {
    return renderDom
  }
  return []
}

/** 处理首次自动回复消息数据,即initChatCallback */
const handleChatCallback = (payload) => {
  const renderDom = []
  const { content = '', height } = payload || {}
  renderDom.push({
    name: 'text',
    text: content.replace(height, '') || '我现在有事不在，请电话与我联系',
  })
  renderDom.push({
    name: 'btn',
    text: height || '【拨打电话】',
  })
  return renderDom
}

/** 处理索要合同数据 */
const handleSyht = (nMsg) => {
  const { from, ID } = nMsg || {}
  const isSelf = from == store.getState().storage.userImAcc
  return {
    ...nMsg,
    id: assembleID(`${nMsg.ID}`),
    payload: { text: isSelf ? '你已向老板申请发起合同~' : '对方向你发起了合同申请,请前往客户端查看' },
    isSelf,
    type: 'TIMTxtSyHeTongElem', // TIMTxtTimeElem 时间类型
    isAvatar: false,
    time: nMsg.clientTime || Number(nMsg.time),
    pType: 'SystemMsg', // 系统消息
    isRead: nMsg.isRead,
    oId: ID,
  }
}

// 获取最后一条状态正常的消息记录
export const getConversationLastMsg = (messageList, idx?) => {
  let nIdx = idx
  const len = messageList.length - 1
  if (!idx && idx != 0) {
    nIdx = len
  }
  const item = messageList[nIdx]
  const { payload } = item || {}
  const { data } = payload || {}
  const { type } = data || {}
  if (item?.type == 'TIMTxtTimeElem' || type == '390.1') {
    return getConversationLastMsg(messageList, nIdx - 1)
  }
  if (item?.status == 'sending' || item?.status == 'fail') {
    if (idx == 0) {
      return undefined
    }
    return getConversationLastMsg(messageList, nIdx - 1)
  }
  return item
}

/**
 * 删除会话中某条消息
 * @param msgId 消息id
*/
export const removeConversationMsg = (msgId) => {
  const messageList = [...(store.getState().timmsg.messageList || [])]
  if (messageList && messageList.length > 0) {
    const idx = messageList.findIndex?.(item => item.oId == msgId)
    if (idx >= 0) {
      const msgInfo = { ...messageList[idx] }
      const olen = messageList.length
      messageList.splice(idx, 1)
      if (idx == olen - 1) {
        const preMsg = messageList[idx - 1]
        if (preMsg?.id?.indexOf('Time') >= 0) {
          messageList.splice(idx - 1, 1)
        }
      }
      const sData: any = { messageList, scrollLocId: '' }
      // 若删除的图片消息，需要将预览图片删除
      if (msgInfo.type == 'img') {
        const curConImages = [...(store.getState().timmsg.curConImages || [])]
        const { payload } = msgInfo || {}
        const { imageInfoArray = [] } = payload || {}
        if (wx.$.u.isArrayVal(imageInfoArray)) {
          const nCurConImages = curConImages.filter((item) => item != (imageInfoArray[0].url || imageInfoArray[0].imageUrl))
          sData.curConImages = nCurConImages
        }
      }
      dispatch(actions.timmsgActions.setState(sData))
    }
  }
}

/**
 * 批量处理消系统息数据
 * @param {Array} messageList 新消息列表
 * @param object systemSingleChat 系统消息对象
 * @param number oldMsgList 老消息列表
 */
export const handleSystemInfoMsgList = (messageList) => {
  const msgList = []
  const nLen = messageList?.length || 0
  if (nLen > 0) {
    messageList.forEach((item) => {
      const nItem = getSystemChatMsg(item)
      const isHas = msgList.find(it => it.id == nItem.id)
      if (nItem && !isHas) {
        msgList.push(nItem)
      }
    })
  }
  return { msgList }
}

// 撤回消息
export const saveRevokeMsgList = (msgInfo, call?:CallBackObj) => {
  const { messageList } = store.getState().timmsg
  const nMsgList = []
  messageList.forEach((item) => {
    if (item.id == msgInfo.id) {
      let nItem = { ...item }
      nItem.isRevoked = true
      nItem = handleRevoked(nItem)
      nMsgList.push(nItem)
    } else {
      nMsgList.push(item)
    }
  })
  dispatch(actions.timmsgActions.setState({ messageList: nMsgList, scrollLocId: '' }))
  if (msgInfo.type == 'TIMImageElem') {
    call.success && call.success()
  }
}

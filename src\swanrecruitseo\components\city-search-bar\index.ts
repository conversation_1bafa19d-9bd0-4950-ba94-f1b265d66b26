import { actions, dispatch } from '@/store/index'

/*
 * @Date: 2022-03-15 13:58:24
 * @Description: 搜索展示内容
 */
Component({
  // 组件的属性列表
  properties: {
    cityList: {
      type: Array,
      value: [],
    },
  },
  observers: {
    cityList(cityList) {
      // eslint-disable-next-line array-callback-return
      cityList.map((item) => {
        const cityId = item.url.replace(/\D/g, '').substring(0, item.url.replace(/\D/g, '').length - 1)
        item.cityId = cityId
      })
      this.setData({
        cityData: cityList,
      })
    },
  },
  data: {
    isExpand: false,
    cityData: [],
  },

  // 组件的方法列表
  methods: {
    /** 展开收起 */
    clickExpandBtn() {
      this.setData({
        isExpand: !this.data.isExpand,
      })
    },
    /** 点击城市 */
    async clickCity({ target: { dataset: { item } } }) {
      if (item) {
        /** 通过平铺城市根据id获取城市对象 */
        const pItem = await wx.$.l.getCityById(item.cityId) || {
          ad_code: '100000',
          ad_name: '中国',
          id: '1',
          letter: 'quanguo',
          name: '全国',
          pid: '0',
        }
        dispatch(actions.storageActions.setItem({ key: 'userLocationCity', value: pItem }))
        wx.$.r.push({ path: '/pages/index/index' })
      }
    },
  },
})

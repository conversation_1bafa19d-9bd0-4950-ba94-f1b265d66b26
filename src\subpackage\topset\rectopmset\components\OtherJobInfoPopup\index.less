.box {
  position: relative;
  width: 100%;
  height: 1200rpx;
  padding: 0 32rpx 32rpx;
  background-color: #f5f6fa;
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;

  .bg {
    position: absolute;
    left: 0;
    right: 0;
    height: 400rpx;
    background: linear-gradient(180deg, #0092ff 0%, #f5f6fa 100%);
  }

  .header {
    .flexRCSB();

    position: relative;
    height: 118rpx;
    padding: 32rpx 0;
    color: #fff;
    font-size: 38rpx;
    font-weight: 700;

    .closeIcon {
      .flexRCC();
    }
  }
}

.scroll {
  height: 1082rpx;

  ::-webkit-scrollbar {
    display: none;
  }
}

.bottom {
  width: 100%;
  height: 82rpx;
  line-height: 82rpx;

  color: @text45;
  font-size: 30rpx;
  text-align: center;
}

.PH {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
}

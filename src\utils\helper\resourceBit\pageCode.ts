/** 路由对应页面标识 */
export const pageCodeArr = [
  // {
  //   pageCode: 'mini_webview',
  //   path: 'subpackage/web-view/index',
  //   name: 'web-view',
  //   pageRole: 2,
  // },
  {
    pageCode: 'recruit_list',
    path: 'pages/index/index',
    name: '找工作列表',
    pageRole: 2,
  },
  {
    pageCode: 'resume_list',
    path: 'pages/resume/index',
    name: '找牛人列表',
    pageRole: 1,
  },
  {
    pageCode: 'contact_list',
    path: 'subpackage/member/myContactHistory/index',
    name: '拨打记录',
  },
  {
    pageCode: 'my_find_worker',
    path: 'subpackage/recruit/published/index',
    name: '管理招工',
    pageRole: 1,
  },
  {
    pageCode: 'msg_list',
    path: 'pages/msg-page/index',
    name: '消息',
  },
  {
    pageCode: 'ucenter',
    path: 'pages/ucenter/index',
    name: '会员中心',
  },
  {
    pageCode: 'recruit_publish_case1',
    path: 'subpackage/recruit/fast_issue/index/index',
    name: '发布招工', // 流程1
    pageRole: 1,
  },
  {
    pageCode: 'recruit_publish_case2',
    path: 'subpackage/recruit/fast_issue/fast_pulish_second/index',
    name: '发布招工', // 流程2
    pageRole: 1,
  },
  {
    pageCode: 'recruit_issue',
    path: 'subpackage/recruit/jisu_issue/index',
    name: '修改招工',
  },
  {
    pageCode: 'recruit_detail',
    path: 'subpackage/recruit/details/index',
    name: '招工详情',
    pageRole: 2,
  },
  {
    pageCode: 'my_recruit_detail',
    path: 'subpackage/recruit/my_detail/index',
    name: '我的职位详情',
  },
  {
    pageCode: 'search_recruit',
    path: 'subpackage/search/index',
    name: '搜索职位信息',
    pageRole: 2,
  },
  {
    pageCode: 'search_recruit_detail',
    path: 'subpackage/recruit/listSearchResultsPage/index',
    name: '职位搜索结果',
  },
  {
    pageCode: 'resume_result_list',
    path: 'subpackage/resume/listSearchResultsPage/index',
    name: '找活搜索结果',
    pageRole: 1,
  },
  {
    pageCode: 'recruit_assistant_screen',
    path: 'subpackage/subscribe/work/index',
    name: '订阅好活筛选',
  },
  {
    pageCode: 'recruit_assistant_home',
    path: 'subpackage/subscribe/list/index',
    name: '订阅好活列表',
  },
  {
    pageCode: 'recruit_set_top',
    path: 'subpackage/topset/rectopmset/index',
    name: '招工置顶',
  },
  {
    pageCode: 'publish_resume',
    path: 'subpackage/resume/resume_publish/index',
    name: '发布简历',
  },
  {
    pageCode: 'perfect_resume_one',
    path: 'subpackage/resume/resume_publish/complete/one/index',
    name: '简历第一步完善',
  },
  {
    pageCode: 'perfect_resume_two',
    path: 'subpackage/resume/resume_publish/complete/two/index',
    name: '简历第二步完善',
  },
  {
    pageCode: 'my_resume_detail',
    path: 'subpackage/resume/publish/index',
    name: '我的简历',
    pageRole: 2,
  },
  {
    pageCode: 'my_resume_perfect',
    path: 'subpackage/resume/perfect/index',
    name: '完善简历',
  },
  {
    pageCode: 'set_top_find_job',
    path: 'subpackage/topset/topmset/index',
    name: '简历加急',
  },
  {
    pageCode: 'resume_auto_refresh',
    path: 'subpackage/resume/continuous_refresh/index',
    name: '简历连续刷新',
  },
  {
    pageCode: 'look_me_resume',
    path: 'subpackage/resume/seeme/index',
    name: '谁看过我的简历',
  },
  {
    pageCode: 'interview_simple',
    path: 'subpackage/video/video_upload/index',
    name: '面试视频',
  },
  {
    pageCode: 'show_project_experience',
    path: 'subpackage/resume/project_list/index',
    name: '项目经验列表',
  },
  {
    pageCode: 'add_project_experience',
    path: 'subpackage/resume/project_add/index',
    name: '添加/修改项目经验',
  },
  {
    pageCode: 'show_skills_certificate',
    path: 'subpackage/resume/certificate_list/index',
    name: '技能证书列表',
  },
  {
    pageCode: 'add_skills_certificate',
    path: 'subpackage/resume/certificate_add/index',
    name: '添加/修改技能证书',
  },
  {
    pageCode: 'quality_worker_recommend',
    path: 'subpackage/today/index',
    name: '优质牛人',
  },
  {
    pageCode: 'resume_detail',
    path: 'subpackage/resume/detail/index',
    name: '简历详情页',
    pageRole: 1,
  },
  {
    pageCode: 'personal_data',
    path: 'subpackage/member/info/index',
    name: '个人资料',
  },
  {
    pageCode: 'my_integral',
    path: 'subpackage/member/integral/officialPoints/index',
    name: '我的积分',
  },
  {
    pageCode: 'glance_list',
    path: 'subpackage/member/who_see/index',
    name: '浏览记录',
  },
  {
    pageCode: 'recharge_integral',
    path: 'subpackage/recharge/recharge/index',
    name: '积分充值',
  },
  {
    pageCode: 'get_integral',
    path: 'subpackage/member/getintegral/index',
    name: '获取积分',
  },
  {
    pageCode: 'real_name',
    path: 'subpackage/member/realname/index',
    name: '实名认证',
  },
  {
    pageCode: 'real_name_company',
    path: 'subpackage/member/firmAuth/index',
    name: '企业认证',
  },
  {
    pageCode: 'company_pages',
    path: 'subpackage/company/edit/index',
    name: '企业主页',
  },
  {
    pageCode: 'integral_record',
    path: 'subpackage/member/integral/list/index',
    name: '消费记录',
  },
  {
    pageCode: 'security_number',
    path: 'subpackage/midphone/guide/index',
    name: '鱼泡安全号',
  },
  {
    pageCode: 'mission_center',
    path: 'subpackage/member/taskcenter/index',
    name: '任务中心',
  },
  {
    pageCode: 'invite_list',
    path: 'subpackage/member/invitation_record/index',
    name: '邀请记录',
  },
  {
    pageCode: 'card_holder',
    path: 'subpackage/member/card_voucher/index',
    name: '卡券列表',
  },
  {
    pageCode: 'watermark_camera',
    path: 'subpackage/common/watermark_camera/index',
    name: '水印相机',
  },
  {
    pageCode: 'user_query',
    path: 'subpackage/member/check/index',
    name: '用户查询',
  },

  {
    pageCode: 'system_message',
    path: 'subpackage/member/system_info/index',
    name: '系统消息',
  },
  {
    pageCode: 'about_us',
    path: 'subpackage/other/notice/index',
    name: '关于鱼泡',
  },
  {
    pageCode: 'account_management',
    path: 'subpackage/member/change-account/index',
    name: '更换账号',
  },
  {
    pageCode: 'user_guide',
    path: 'subpackage/other/course/index',
    name: '新手指南',
  },
  {
    pageCode: 'give_counsel',
    path: 'subpackage/other/yp_news/index',
    name: '鱼泡资讯',
  },
  {
    pageCode: 'complaint',
    path: 'subpackage/common/complaint/index',
    name: '投诉第二页',
  },
  {
    pageCode: 'change_account',
    path: 'subpackage/member/evaluation/index',
    name: '评价',
  },
  {
    pageCode: 'follow_official_account',
    path: 'subpackage/attention/public/index',
    name: '关注公众号落地页',
  },
  {
    pageCode: 'changyongyu_oper',
    path: 'subpackage/tim/comWordsOper/index',
    name: '添加/编辑常用语',
  },
  {
    pageCode: 'PersonalHomepage',
    path: 'subpackage/recruit/individual/index',
    name: '个人主页',
  },
  {
    name: '简历列表筛选页',
    pageCode: 'resume_list_select',
    path: 'subpackage/tools-page/screen-picker/index',
    pageRole: 1,
  },
  {
    name: '添加职位',
    pageCode: 'add_job',
    path: 'subpackage/resume/job_list/index',
    pageRole: 2,
  },
  {
    name: '添加职位',
    pageCode: 'add_job',
    path: 'subpackage/recruit/details/index',
    pageRole: 2,
  },
  {
    name: 'im会话',
    pageCode: 'im_conversation',
    path: 'subpackage/tim/groupConversation/index',
    pageRole: 2,
  },
  {
    name: '收藏职位/老板',
    pageCode: 'collect_b',
    path: 'subpackage/member/b_collect/index',
    pageRole: 2,
  },
  {
    name: '收藏牛人',
    pageCode: 'collect_c',
    path: 'subpackage/member/c_collect/index',
    pageRole: 1,
  },
  {
    name: '面试信息编辑页',
    pageCode: 'msxxbjyg',
    path: 'subpackage/invite-interview/interview-info-edit-b/index',
    pageRole: 1,
  },
  {
    name: '面试信息详情页',
    pageCode: 'msyyxqlb',
    path: 'subpackage/invite-interview/interview-info-b/index',
    pageRole: 1,
  },
  {
    name: '面试信息详情页',
    pageCode: 'msyyxqnr',
    path: 'subpackage/invite-interview/interview-info-c/index',
    pageRole: 1,
  },
  {
    name: '职位列表专区落地页',
    pageCode: 'Zone_Joblist_Page',
    path: 'subpackage/recruit/activity-zone-page/index',
    pageRole: 2,
  },

] as const

/** 资源位兜底数据 */
export const defaultResource = {
  recruit_list_2: [
    {
      refresh: 0,
      locationCode: 'icon_job_diamond',
      type: 1,
      list: [
        {
          resourceCode: '24292',
          resourceUrl: 'https://static107.cdqlkj.cn/r/d0dc/107/pb/p/20250421/a3ed64f0034847b1bd467f859ed36bfa.png',
          funcCode: '',
          linkUrl: "/subpackage/classify/bottom-full-screen/index?sourceId=11&isSelectToPage=true&toUrl=%2Fsubpackage%2Frecruit%2FlistSearchResultsPage%2Findex%3Fenter_id%3D1%26fromPage%3DrecruitIndex&hideSelectAll=false&sourcePageName='首页金刚区'&isShowRecommend=true&epc=recruit_list",
          linkType: 1,
          risk: 0,
          id: 14943,
          title: '全部职位',
        },
        {
          resourceCode: '24289',
          resourceUrl: 'https://static107.cdqlkj.cn/r/bc9a/107/pb/p/20250421/416cc27dc95f4ed692766765ba0d9beb.png',
          funcCode: '',
          linkUrl: '/subpackage/recruit/listSearchResultsPage/index?cache_occupation=true&classify_id=1@1&enter_id=search_jianzhuzhaogong&epc=recruit_list',
          linkType: 1,
          risk: 0,
          id: 14935,
          title: '建筑招工',
        },
        {
          resourceCode: '24290',
          resourceUrl: 'https://static107.cdqlkj.cn/r/830d/107/pb/p/20250417/58960542a00b458c875dd04cdef01058.png',
          funcCode: '',
          linkUrl: '/subpackage/recruit/listSearchResultsPage/index?cache_occupation=true&classify_id=25@25&enter_id=home_rsxz_x_w&epc=recruit_list',
          linkType: 1,
          risk: 0,
          id: 14936,
          title: '人事行政',
        },
        {
          resourceCode: '24643',
          resourceUrl: 'https://static107.cdqlkj.cn/r/e180/107/pb/p/20250421/75081c725149490bbfdebf09cce07203.png',
          funcCode: '',
          linkUrl: '/subpackage/recruit/listSearchResultsPage/index?cache_occupation=true&classify_id=340@1,333@1,923@30,314@1,325@1&enter_id=home_icon_rijie10&cache_occupation_new=true&epc=recruit_list',
          linkType: 1,
          risk: 0,
          id: 15291,
          title: '日结专区',
        },
        {
          resourceCode: '24385',
          resourceUrl: 'https://static107.cdqlkj.cn/r/f472/107/pb/p/20250417/e3f435ff302e41babf7bb57e6ea59296.png',
          funcCode: '',
          linkUrl: '/subpackage/recruit/listSearchResultsPage/index?cache_occupation=true&classify_id=923,642,565,635,1149,560&enter_id=home_shfw_x_w&epc=recruit_list',
          linkType: 1,
          risk: 0,
          id: 14938,
          title: '生活服务',
        },
      ],
    },
  ],
  ucenter_2: [
    {
      locationCode: 'icon_ticker_tool650',
      type: 1,
      refresh: 0,
      list: [
        {
          resourceCode: '19831',
          resourceUrl: 'https://static107.cdqlkj.cn/r/6422/107/pb/p/20240624/e2db5bbd2eac4466809c24998e425fd8.png',
          funcCode: '',
          linkUrl: '/subpackage/member/getintegral/index?epc=ucenter',
          linkType: 1,
          risk: 0,
          id: 8613,
          title: '获取积分',
        },
        {
          resourceCode: '16746',
          resourceUrl: 'https://static107.cdqlkj.cn/r/ed62/107/pb/p/20240624/e7691b21741e42a8abd3e8f3beeb634d.png',
          funcCode: '',
          linkUrl: '/subpackage/member/realname/index?epc=ucenter',
          linkType: 1,
          risk: 0,
          id: 7757,
          title: '实名认证',
        },
        {
          resourceCode: '19934',
          resourceUrl: 'https://static107.cdqlkj.cn/r/6ed6/107/pb/p/20240812/24e8339baa4a47ff9f5af89c1d46a444.png',
          funcCode: '',
          linkUrl: '/subpackage/recharge/recharge/index?epc=ucenter',
          linkType: 1,
          risk: 0,
          id: 8549,
          title: '积分充值',
        },
      ],
    },
    {
      refresh: 0,
      list: [
        {
          resourceCode: '16763',
          resourceUrl: 'https://static107.cdqlkj.cn/r/0f5a/107/pb/p/20240624/be5ffcbd1ca44a03ac117744b7f45ed6.png',
          funcCode: '',
          linkUrl: '/feedback/add?miniIsLogin=true&configId=31&appId=102&epc=ucenter',
          linkType: 3,
          risk: 0,
          id: 7842,
          title: '帮助与反馈',
        },
        {
          resourceCode: '23097',
          resourceUrl: 'https://static107.cdqlkj.cn/r/c937/107/pb/p/20240902/314a8273eab943f4aa1191d44a23eaf0.png',
          funcCode: '',
          linkUrl: '/rule-center/index?isLogin=true&epc=ucenter',
          linkType: 3,
          risk: 0,
          id: 13281,
          title: '规则中心',
        },
      ],
      locationCode: 'icon_other_functions650',
      type: 1,
    },
  ],
  resume_list_1: [
    {
      refresh: 0,
      locationCode: 'icon_resume_diamond',
      type: 1,
      list: [
        {
          resourceCode: '20316',
          resourceUrl: 'https://static107.cdqlkj.cn/r/1fc4/107/pb/p/20240806/f47a7c09cce34aa7a7564c265e2a786d.png',
          funcCode: '',
          linkUrl: '/pages/resume/index?industry=1&classify_id=348,301,1008,314,325,333,340&epc=resume_list',
          linkType: 1,
          risk: 0,
          id: 8538,
          title: '建筑招工',
        },
        {
          resourceCode: '19822',
          resourceUrl: 'https://static107.cdqlkj.cn/r/0028/107/pb/p/20240806/4155e03616a24fff81c54f4c25ae8d1a.png',
          funcCode: '',
          linkUrl: '/pages/resume/index?industry=2&classify_id=1003,417,301,1008,325,333,426&epc=resume_list',
          linkType: 1,
          risk: 0,
          id: 8537,
          title: '装修招工',
        },
        {
          resourceCode: '20314',
          resourceUrl: 'https://static107.cdqlkj.cn/r/1abb/107/pb/p/20240806/9c92861b9787423c8f4d70689116b6d5.png',
          funcCode: '',
          linkUrl: '/pages/resume/index?industry=-1&classify_id=340&epc=resume_list',
          linkType: 1,
          risk: 0,
          id: 8532,
          title: '小工力工',
        },
        {
          resourceCode: '19821',
          resourceUrl: 'https://static107.cdqlkj.cn/r/b9d9/107/pb/p/20240806/da78ca40169b4ef184fda812c21072f9.png',
          funcCode: '',
          linkUrl: '/pages/resume/index?industry=13&classify_id=779&epc=resume_list',
          linkType: 1,
          risk: 0,
          id: 8536,
          title: '工厂普工',
        },
        {
          resourceCode: '19818',
          resourceUrl: 'https://static107.cdqlkj.cn/r/8c26/107/pb/p/20240806/ab617902ab5b48c092fc7888fc074669.png',
          funcCode: '',
          linkUrl: '/pages/resume/index?industry=15,31&classify_id=594,635,1018,1036,630,1149,649&epc=resume_list',
          linkType: 1,
          risk: 0,
          id: 8531,
          title: '餐饮酒旅',
        },
      ],
    },
  ],
  ucenter_1: [
    {
      locationCode: 'icon_ticker_tool_boss650',
      type: 1,
      refresh: 0,
      list: [
        {
          resourceCode: '19654',
          resourceUrl: 'https://static107.cdqlkj.cn/r/7dfd/107/pb/p/20240724/6b103c045e3249f9be39e58c32cb8d28.png',
          funcCode: '',
          linkUrl: '/subpackage/member/realname/index?epc=ucenter',
          linkType: 1,
          risk: 0,
          id: 8317,
          title: '实名认证',
        },
        {
          resourceCode: '19755',
          resourceUrl: 'https://static107.cdqlkj.cn/r/4297/107/pb/p/20240724/8765aaf22c0f4df4a56ecc720f057680.png',
          funcCode: '',
          linkUrl: '/subpackage/member/firmAuth/index?epc=ucenter',
          linkType: 1,
          risk: 0,
          id: 8320,
          title: '公司认证',
        },
        {
          resourceCode: '20719',
          resourceUrl: 'https://static107.cdqlkj.cn/r/6df5/107/pb/p/20240912/bd5c4ebdf5ad4873901d7680f7a8709a.png',
          funcCode: '',
          linkUrl: 'security-number/index?miniIsLogin=true&epc=ucenter',
          linkType: 3,
          risk: 0,
          id: 9889,
          title: '主叫号管理',
        },
      ],
    },
    {
      refresh: 0,
      locationCode: 'icon_other_functions_boss650',
      type: 1,
      list: [
        {
          resourceCode: '18647',
          resourceUrl: 'https://static107.cdqlkj.cn/r/1d48/107/pb/p/20240618/5c77aa7fdd37491eb2ef8b6741e6c408.png',
          funcCode: '',
          linkUrl: '/subpackage/company/edit/index?epc=ucenter',
          linkType: 1,
          risk: 0,
          id: 7431,
          title: '公司主页',
        },
        {
          resourceCode: '19923',
          resourceUrl: 'https://static107.cdqlkj.cn/r/ec01/107/pb/p/20240724/42b9245a395d41acb4747381adb5ed5a.png',
          funcCode: '',
          linkUrl: '/feedback/add?miniIsLogin=true&configId=31&appId=102&epc=ucenter',
          linkType: 3,
          risk: 0,
          id: 8457,
          title: '帮助与反馈',
        },
        {
          resourceCode: '19752',
          resourceUrl: 'https://static107.cdqlkj.cn/r/f710/107/pb/p/20240724/5ae71947df9d4def8962830fb949193e.png',
          funcCode: '',
          linkUrl: '/subpackage/member/system/index?epc=ucenter',
          linkType: 1,
          risk: 0,
          id: 8315,
          title: '隐私保护',
        },
        {
          resourceCode: '23419',
          resourceUrl: 'https://static107.cdqlkj.cn/r/3d97/107/pb/p/20240902/8fe94289666d451b8c3fb777742c8a06.png',
          funcCode: '',
          linkUrl: '/rule-center/index?isLogin=true&epc=ucenter',
          linkType: 3,
          risk: 0,
          id: 13286,
          title: '规则中心',
        },
        {
          resourceCode: '24735',
          resourceUrl: 'https://static107.cdqlkj.cn/r/d284/107/pb/p/20250409/d064f299493e422a90253178064aab19.png',
          funcCode: '',
          linkUrl: '/complaint-report/info?miniIsLogin=true&epc=ucenter',
          linkType: 3,
          risk: 0,
          id: 15186,
          title: '举报中心',
        },
      ],
    },
  ],
}

/* 从资源表的源数据，用来做筛选
export const initPageCodeList = [
  'recruit_list',
  'resume_list',
  'contact_list',
  'my_find_worker',
  'msg_list',
  'ucenter',
  'recruit_publish_case1',
  'recruit_publish_case2',
  'recruit_issue',
  'recruit_detail',
  'search_recruit',
  'search_recruit_detail',
  'recruit_published',
  'recruit_assistant_screen',
  'recruit_assistant_home',
  'recruit_set_top',
  'publish_resume',
  'perfect_resume_one',
  'perfect_resume_two',
  'perfect_resume_three',
  'my_resume_detail',
  'edit_basic_info',
  'share_my_resume_card',
  'set_top_find_job',
  'resume_auto_refresh',
  'look_me_resume',
  'interview_simple',
  'show_project_experience',
  'add_project_experience',
  'show_skills_certificate',
  'add_skills_certificate',
  'magic_weapon',
  'quality_worker_recommend',
  'resume_detail',
  'resume_near_by_list',
  'quality_worker_special_recommend',
  'personal_data',
  'my_integral',
  'my_collect',
  'glance_list',
  'recharge_integral',
  'get_integral',
  'real_name',
  'real_name_company',
  'integral_record',
  'security_number',
  'report_center',
  'security_center',
  'user_guide',
  'give_counsel',
  'electronic_contract',
  'second_hand',
  'mission_center',
  'invite_list',
  'card_holder',
  'rwa_home',
  'watermark_camera',
  'system_setting',
  'user_query',
  'customer_service',
  'factory_recruit_list',
  'factory_resume_list',
  'factory_recruit_detail',
  'factory_recruit_publish',
  'factory_recruit_issue',
  'logistics_recruit_list',
  'logistics_resume_list',
  'logistics_recruit_publish',
  'logistics_recruit_issue',
  'logistics_recruit_detail',
  'about_us',
  'my_recruit_detail',
  'new_recommended',
  'im_conversation',
  'look_me_list',
  'share_shirt',
  'insurance',
  'insurance_list',
  'system_message',
  'complaint',
  'account_management',
  'cancel_account',
  'factory_top_page',
  'logistics_top_page',
  'driver_recruit',
  'budget_package',
  'purchase_record',
  'service_home',
  'contact_list_boss',
  'insurance',
  'feedback',
  'evaluate_us',
  'refund',
  'account_appeal',
  'turntablePre',
  'logresumelist',
  'logecruitlist',
  'special_pop_customer_service',
  'earn_points',
  'mall',
  'page_main_android',
  'page_wake_up_android',
  'special_pop_share',
  'follow_mini',
  'evaluate_mini',
  'change_account',
  'privacy',
  'dizhixuanzeye',
  'xiugaixinxi',
  'jichuziliao',
  'paisheshipin',
  'dingyuehaohuo',
  'zhaogongfabuchenggong',
  'event_notification',
  'member_homepage',
  'order_center',
  'special_pop_rwa',
  'cooperate',
  'follow_official_account',
  'additive_group',
]
*/

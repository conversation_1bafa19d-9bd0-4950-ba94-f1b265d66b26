/*
 * @Date: 2022-01-05 16:33:35
 * @Description: 定位函数
 *
 * @Adcode: 地区的adcode代码从左至右的含义：
 * 第一、二位：表示省（自治区、直辖市、特别行政区）。
 * 第三、四位：表示市（地区、自治州、盟及国家直辖市所属市辖区和县的汇总码）。
 *            01-20，51-70表示省直辖市；
 *            21-50表示地区（自治州、盟）。
 * 第五、六位：表示县（市辖区、县级市、旗）。
 *            01-18表示市辖区或地区（自治州、盟）辖县级市；
 *            21-80表示县（旗）；
 *            81-99表示省直辖县级市。
 */
import { MAP_KEY } from '@/config/app'
import AMapWX from '@/lib/amap/ext.js'
import miniConfig from '@/miniConfig/index'
import { actions, dispatch, storage, store } from '@/store/index'
import { asyncRequest } from '@/utils/request/utils'
import { getLoginAb } from '../login/index'
import { AllAreasDataItem, ITreeArea, LocationDataType, CityUseData } from './type'

export const DIRECT_CITY_IDS = ['2', '25', '27', '32', '33', '34', '35']

/** 处理直辖市和特别行政区的名字 */
export function formatMunicipalityDirectlyArea(id) {
  // 依次对应：北京，上海，天津，重庆，香港，澳门，台湾
  const municipalityAreaArray = [
    {
      id: '2',
      name: '北京市',
    },
    {
      id: '25',
      name: '上海市',
    },
    {
      id: '27',
      name: '天津市',
    },
    {
      id: '32',
      name: '重庆市',
    },
    {
      id: '33',
      name: '香港',
    },
    {
      id: '34',
      name: '澳门',
    },
    {
      id: '35',
      name: '台湾',
    },
  ]
  const data = municipalityAreaArray.filter((item) => item.id === String(id))
  if (data.length) {
    return data[0].name
  }
  return ''
}

/**
 * @description: 用户启动app进行定位和是否切换城市提示弹框
 */
export const citySwitchAndLocal = async () => {
  // TODO zddq 隐私协议 true-代表需要弹隐私弹窗
  const isShowServicePrivacyV5 = storage.getItemSync('isShowServicePrivacyV5')
  if (isShowServicePrivacyV5) {
    return
  }
  /** 是否弹出过角色弹窗-true-false */
  const { rollPopupFlag } = store.getState().storage.common
  const isLoginAb = !rollPopupFlag ? await getLoginAb(['RegLog_No_location', 'RegLog_All_New']) : false
  const isGoToRole = isLoginAb && await roleSwitch()
  if (isGoToRole) {
    return
  }
  let is_success = '0'
  /** 获取定位 */
  try {
    const res = await getLocation().catch((err) => err)
    const { rollPopupFlag } = store.getState().storage.common
    if (res && res.longitude && res.latitude) {
      // 启动时更新经纬度, 获取定位经纬度，才更新本地定位缓存，否则清除
      if (res.longitude && res.latitude) {
        dispatch(actions.storageActions.setItem({ key: 'userLocation', value: `${res.longitude},${res.latitude}` }))
      }
      const adCode = res.adcode ? Number(res.adcode) : 0
      /** 请求获取定位 */
      const { data: resp } = await fetchAreaIdByAdCode({ adCode: `${adCode}` })
      // 储存本地定位信息
      await getGpsAreaInfo({ ...res, ...resp })
    } else {
      await getUserIpLocation()
    }
    is_success = res && res.longitude && res.latitude ? '1' : '0'
    !rollPopupFlag && roleSwitch()
  } catch (error) {
    console.error('定位失败=>', error)
    await getGpsAreaInfo({}, false)
  }
}

/** 获取用户ip地址 */
export const getUserIpLocation = async (isUpdate = false) => {
  const { userLocationCity } = store.getState().storage
  const stoUserLocationCity = storage.getItemSync('userLocationCity')
  const { isGetUserIp } = userLocationCity || {}
  const { isGetUserIp: stoIsGetUserIp } = stoUserLocationCity || {}
  if ((!isGetUserIp && !stoIsGetUserIp) || isUpdate) {
    const city = await getCityByIp()
    const { id } = city || {}
    if (id) {
      const cityValue = await getCityById(id)
      // 若第一次未储存缓存情况，定位后储存缓存
      await dispatch(actions.storageActions.setItem({
        key: 'userLocationCity',
        value: {
          ...stoUserLocationCity,
          ...userLocationCity,
          isGetUserIp: true,
          ...cityValue,
          recruitCityObj: { ...cityValue, citys: [cityValue].filter(Boolean), cityLen: 1 },
          resumeCityObj: { ...cityValue, citys: [cityValue].filter(Boolean), cityLen: 1 },
          isFirst: false,
        },
      }))
    }
  }
}

/** 角色选择 */
export const roleSwitch = async () => {
  const options = wx.getLaunchOptionsSync()
  if (options && options.path === 'pages/index/index') {
    const isPopup = await getLoginAb(['RegLog_Only_identity', 'RegLog_All_New'])
    if (isPopup) {
      dispatch(actions.storageActions.setCommonItem({ rollPopupFlag: true }))
      setTimeout(() => {
        const pages = getCurrentPages()
        const perpage = pages[pages.length - 1]
        perpage && perpage.setData({ showRoleDialog: true })
      }, 1500)
      return true
    }
    // 获取城市id
    const { userLocation_city } = store.getState().storage
    try {
      if (!userLocation_city?.cityId) {
        await getUserIpLocation()
      }
    } catch (error) {
      console.log(error)
    }
    // 获取定位
    const city = store.getState().storage.userLocationCity
    const newCityId = DIRECT_CITY_IDS.includes(String(city.pid)) ? city.pid : city.id
    try {
      const cityId = userLocation_city && userLocation_city.provinceId && DIRECT_CITY_IDS.includes(String(userLocation_city.provinceId)) ? userLocation_city.provinceId : userLocation_city?.cityId
      const { data: { showBanner } } = await wx.$.javafetch['POST/account/v1/cityCheck/showBannerCheck']({
        cityId: cityId || newCityId,
        sceneType: 1,
      })
      // 如果是活动开启需要调用一个角色选择配置
      if (showBanner) {
        await wx.$.javafetch['POST/account/v1/contentSetting/newRoleSwitchPopup']().then(async ({ data }) => {
          await dispatch(actions.configActions.setState({ newRoleSwitchPopup: { ...data, showBanner } }))
        })
      }
    } catch (error) {
      console.log(error)
    }
    wx.$.r.push({ path: '/subpackage/common/switch-roles/index' })
    return true
  }
  dispatch(actions.storageActions.setCommonItem({ rollPopupFlag: true }))
  return false
}

/**
 * @param: showErrMsg 是否展示错误提示，默认true
 * location: 不穿入拿当前地址作的经纬度进行查询, 格式：101.706548,32.902387
 * @return: Promise<IssueAreaData>
 * @description: 获取当前定位
 */
export const getLocation = (showErrMsg = true, location = ''): Promise<LocationDataType> => {
  const timeLocation = location || storage.getItemSync('timeLocation') || ''
  return new Promise(async (resolve, reject) => {
    const MapRes = await AMapWX
    const GDMAP = new MapRes({ key: MAP_KEY })
    GDMAP.getRegeo({
      location: timeLocation,
      success: async (res: any) => {
        try {
          const data = res[0]
          const locInfo = data.regeocodeData.addressComponent || {}
          const { province } = locInfo
          const city = locInfo.city?.length > 0 ? locInfo.city : province
          const cityName = replaceLastStr(city)
          const location = `${data.longitude},${data.latitude}`
          // 通过高德地图获取的adcode有时候不准确，所以需要通过后端接口获取
          const { adcode } = locInfo
          // 添加地址信息
          wx.$.collectEvent.config({
            user_province: province,
            user_city: city,
            user_area: locInfo.district,
            user_address: locInfo.streetNumber.street,
            user_location: location,
          })
          const result: LocationDataType | any = {
            streetName: locInfo.streetNumber.street,
            province,
            city,
            cityName,
            district: locInfo?.district?.length > 0 ? locInfo.district : '',
            adcode,
            oadcode: adcode,
            citycode: locInfo.citycode,
            address: Array.isArray(locInfo.neighborhood?.name) ? data.desc : locInfo.neighborhood?.name,
            longitude: `${data.longitude}`,
            latitude: `${data.latitude}`,
            location,
            wardenryid: locInfo.city,
            regionone: '',
            formattedAddress: data.regeocodeData.formatted_address,
          }
          resolve(result)
          commonLocalExposure('定位成功', {
            location: locInfo.streetNumber?.location,
            timeLocation,
            addressComponent: locInfo,
          })
        } catch (err) {
          reject(err)
          commonLocalExposure('定位失败1', { location: '', timeLocation, err })
        }
      },
      fail: (err) => {
        const bool = `${err.errMsg}`.indexOf('频繁调用会增加电量损耗，可考虑使用')
        if (err.errCode == '0' && bool >= 0 && showErrMsg) {
          wx.$.msg('定位异常,请稍后再试')
        }
        reject(err)
        commonLocalExposure('定位失败2fail', { location: '', timeLocation, err })
      },
    })
  })
}

/**
 * 根据adCode获取省市区ID-通过接口获取
 *  */
export async function fetchAreaIdByAdCode(params: { adCode: string | number }) {
  const adCode = wx.$.u.getObjVal(params, 'adCode', '')
  if (adCode && `${adCode}`.length < 6 && `${adCode}`.length > 0) {
    return { code: 500, data: '', message: '无地址编号' }
  }
  const res: any = await wx.$.javafetch['POST/lbs/v1/location/getRelatedAreaIdByAdCode']({
    adCode: `${params.adCode}`,
  }).catch((err) => {
    return { code: 500, data: '', err }
  })
  if (res.data) {
    const { data } = res
    data.provinceId = data.provinceId || 0 // 省份处理
    data.cityId = data.cityId || 0 // 城市处理
    data.distinctId = data.distinctId || 0 // 区域处理
    if (!data.cityId && data.distinctId) {
      data.cityId = data.distinctId
      data.distinctId = 0
    }
    data.countyId = data.distinctId // 区域处理, 这里的区域id是countyId，为保持和php接口字段一致
  }
  return res
}

/** 根据返回的定位信息，得到当前的省市区 */
export const getGpsAreaInfo = async (resp = {}, auth = true) => {
  try {
    if (auth) {
      const pages = getCurrentPages()
      await saveLocationCity(resp)
      dispatch(actions.storageActions.setItem({ key: 'L_IS_GEO_AUTH', value: 1 }))
      if (pages.length) {
        const lastPage = pages[pages.length - 1]
        if (lastPage.route === 'pages/index/index') {
          lastPage.onImmediateRefresh()
        }
      }
    } else {
      dispatch(actions.storageActions.setItem({ key: 'L_IS_GEO_AUTH', value: 2 }))
    }
  } catch (err) {
    dispatch(actions.storageActions.setItem({ key: 'L_IS_GEO_AUTH', value: 2 }))
  }
}

/** 根据IP获取城市 */
export const getCityByIp = async () => {
  try {
    const res = await wx.$.javafetch['POST/lbs/v1/location/getAreaByIp']({}, { hideMsg: true })
    return res.data || {}
  } catch (e) {
    return {}
  }
}

/**
 * @params: id: string
 * @return: Promise<AllAreasDataItem | undefined>
 * @description: 根据id获取城市信息
 */
export const getCityById = async (_id: number | string): Promise<AllAreasDataItem | undefined> => {
  const areaDataList = await getAreaTreeData()
  const { city, province } = await getAreaById(_id)
  const id = wx.$.u.getObjVal(city, 'id', '') || wx.$.u.getObjVal(province, 'id', '')
  // ! 自己封装方法代替老接口index/index-area 获取到新的areaDataList
  const nAreaDataList = changeNewAreaData(areaDataList) || []
  let reRes
  nAreaDataList.forEach((element) => {
    const result = element.find((val) => val.id == id)
    if (result && !reRes) {
      /** 该城市的父级元素 */
      const parents = []
      if (result.level != 1) {
        const parent = element.find((val) => val.id == result.pid)
        if (parent) {
          parents.push(parent)
        }
      } else {
        const parent = JSON.parse(JSON.stringify(element.find((val) => val.id == result.id)))
        delete parent.children
        if (parent) {
          parents.push(parent)
        }
      }
      reRes = { ...result, parents }
    }
  })
  return reRes
}

// 正则字符串最后一个字符是"省","市"时替换为空
function replaceLastStr(str = '', regStr = '省|市') {
  // eslint-disable-next-line no-param-reassign
  str = str ? `${str}` : ''
  const reg = new RegExp(regStr)
  const lastStr = str.substr(str.length - 1, 1)
  if (reg.test(lastStr)) {
    return str.replace(lastStr, '')
  }
  return str
}

/** 定位的通用埋点 */
export async function commonLocalExposure(name: string, pointEvent: { location: string, [key: string]: any } = { location: '' }) {
  // eslint-disable-next-line no-param-reassign
  pointEvent = pointEvent || { location: '' }
  // 获取上一个页面地址
  let prevPage = ''
  const pages = getCurrentPages()
  if (pages.length > 1) {
    prevPage = pages[pages.length - 2].route
  }
  /** 获取手机型号 */
  try {
    const systemInfo = await wx.getSystemInfo()
    wx.$.collectEvent.event('location_mini', {
      name: `定位地址埋点_${name}`,
      prevPage,
      /** 设备信息 */
      systemInfo,
      ...pointEvent,
    })
  } catch (e) {
    console.error(e)
  }
}

/** 储存定位信息 */
export async function saveLocationCity(resp, isSelect = true) {
  if (!resp?.cityId && !resp?.provinceId) {
    // 储存国家
    if (resp?.countyId === 1) {
      const locItem: any = { provinceId: resp?.countyId, provinceName: '中华人民共和国', address: resp.formatted_address }
      dispatch(actions.storageActions.setItem({ key: 'userLocation_city', value: locItem }))
      return locItem
    }
    return {}
  }
  // 储存省市
  const area = await getCityById(resp.cityId)
  const province = await getCityById(resp?.provinceId)

  const locItem = {
    adcode: resp.adcode,
    cityId: resp.cityId,
    location: resp.location,
    latitude: resp.latitude,
    longitude: resp.longitude,
    cityName: area?.name || province?.name,
    cityCompleteName: resp.city,
    provinceId: resp?.provinceId,
    provinceName: province?.name,
    provinceCompleteName: resp.province,
    district: resp.district,
    address: resp.formattedAddress,
    name: resp.address,
  }
  dispatch(actions.storageActions.setItem({ key: 'userLocation_city', value: locItem }))
  if (isSelect) {
    const cityValue = await getCityById(resp.cityId || resp?.provinceId)
    // 储存渲染的城市
    const userLocationCityStore = store.getState().storage.userLocationCity
    // 若第一次未储存缓存情况，定位后储存缓存
    if (userLocationCityStore && userLocationCityStore.isFirst) {
      dispatch(actions.storageActions.setItem({
        key: 'userLocationCity',
        value: {
          ...userLocationCityStore,
          ...cityValue,
          recruitCityObj: { ...cityValue, citys: [cityValue].filter(Boolean), cityLen: 1 },
          resumeCityObj: { ...cityValue, citys: [cityValue].filter(Boolean), cityLen: 1 },
          isFirst: false,
        },
      }))
    }
  }
  return locItem
}

/** 获取城市列表总数据-深拷贝之后返回数据-避免有的地方改动到原型
 * @param {boolean} isNewData 是否重新获取最纯的数据
 */
export const getAreaTreeData = (() => {
  // 第一次获取的时间
  let firstTime = 0
  // promise 缓存，在一定的时间内返回同一个promise，不重新执行获取逻辑
  let promise
  return async (isNewData = false) => {
    if (firstTime === 0) {
      firstTime = Date.now()
    }
    function getPromise() {
      return new Promise(async (resolve) => {
        let treeData = []
        const n_isRefreshCDN = storage.getItemSync('isRefreshCDN')
        if (n_isRefreshCDN) {
          const areaData = (await getAreaData()) || []
          storage.setItemSync('areaTree', areaData)
          storage.setItemSync('isRefreshCDN', false)
          treeData = areaData
          // 保存地图数据
          dispatch(actions.mapActions.setAreaMap(treeData))
          resolve(treeData)
          return
        }
        treeData = storage.getItemSync('areaTree')
        if (!Array.isArray(treeData) || treeData.length < 10) {
          const areaData = (await getAreaData()) || []
          storage.setItemSync('areaTree', areaData)
          treeData = areaData
        }
        dispatch(actions.mapActions.setAreaMap(treeData))
        resolve(treeData)
      })
    }
    if (isNewData) {
      promise = getPromise()
    } else if ((Date.now() - firstTime) < 10000) {
      // 10 秒以内调用这个方法都返回这个结果
      if (!promise) {
        promise = getPromise()
      }
    } else {
      // 重置时间
      firstTime = 0
      promise = getPromise()
    }
    return new Promise(async (resolve) => {
      const areaData = wx.$.u.deepClone(await promise)
      dispatch(actions.mapActions.setAreaMap(areaData))
      resolve(areaData)
    }) as Promise<ILocation.TAreaData[]>
  }
})()

/** 获取所有城市列表 */
async function getAreaData() {
  try {
    const res = await wx.$.javafetch['POST/lbs/v2/area/tree']()
    const areaData = organizeAreaData(res.data.data)
    storage.setItemSync('areaTree', areaData)
    return areaData
  } catch {
    // 加个时间戳让cdn强刷
    const timer = Date.now()
    const areaData = await fetchAreaData(`https://cdn.yupao.com/json/area_v1.json?time=${timer}`)
    // 【迭代】地区修改YP-16278:处理一级地址的没有children或者children长度为0的时候的情况
    if (areaData && areaData.length) {
      areaData.forEach((item) => {
        if (item.id !== '1' && (!item.children || item?.children?.length < 1)) {
          // eslint-disable-next-line no-param-reassign
          item.children = [
            {
              id: '',
              pid: '',
              name: '',
              ad_name: '',
              letter: '',
              level: 2,
              children: [],
            },
          ]
        }
      })
    }
    storage.setItemSync('areaTree', areaData)
    return areaData
  }
}

/** 根据地区id，返回省市区
 * @description 注意:id为省的id只返回省的信息,id为市的id只返回省和市的信息，id为区的id则返回省市区信息
 * @param areaId string|number
 * */
export const getAreaById = async (areaId: string | number) => {
  return getAreaSearch(areaId, 'id')
}

/**
 * @description 根据地址信息，返回省市区
 * @param areaVal string|number 地址id或者地址的编码
 * @returns {Promise<ITreeArea>} areInfo 省市区对象
 * */
export async function getAreaSearch(
  areaVal: string | number,
  type: 'ad_code' | 'id' | null = null,
) {
  const areInfo = { province: '', city: '', district: '', current: '', special: '' } as ITreeArea

  if (!areaVal) {
    return areInfo
  }

  let { areaAdCodeMap, areaIdMap } = store.getState().map
  if (!wx.$.u.isObjVal(areaAdCodeMap, 20) || !wx.$.u.isObjVal(areaIdMap, 20)) {
    await getAreaTreeData()
    areaAdCodeMap = store.getState().map.areaAdCodeMap || {}
    areaIdMap = store.getState().map.areaIdMap || {}
  }

  function setAreaInfo(areaData: ITreeArea['current']) {
    if (!areaData) {
      return
    }
    if (areaData.level == 1 || areaData.id == 1) {
      areInfo.province = { ...areaData, level: 1 }
      return
    }
    if (areaData.level == 2) {
      areInfo.city = { ...areaData }
      const area = areaIdMap[areaData.pid] || ''
      if (!area) {
        return
      }
      setAreaInfo(area)
    }
    if (areaData.level == 3) {
      areInfo.district = { ...areaData, gid: '' }
      const area = areaIdMap[areaData.pid] || ''
      if (!area) {
        return
      }
      setAreaInfo(area)
    }
  }

  let current
  if (type === 'ad_code') {
    current = areaAdCodeMap[areaVal] || ''
  } else if (type === 'id') {
    current = areaIdMap[areaVal] || ''
  } else {
    current = areaIdMap[areaVal] || areaAdCodeMap[areaVal] || ''
  }
  if (current) {
    areInfo.current = { ...current }
    areInfo.special = getRegion(current.ad_code)
    setAreaInfo(areInfo.current)
  }

  /** 给district加上gid */
  if (areInfo.district && areInfo.current) {
    areInfo.district.gid = areInfo.province.id
    areInfo.current.gid = areInfo.province.id
  }

  return areInfo
}

/** 自己封装方法代替老接口index/index-area 获取到新的areaDataList */
export const changeNewAreaData = (areaDataList) => {
  if (!areaDataList) {
    return []
  }
  // 储存区域
  const newAreaArr = []
  // 单独存储港澳台
  const newCityArr = []
  areaDataList.forEach(item => {
    const newAreaItemArr = []
    if (item.children && item.children.length && item.children[0].id) {
      newAreaItemArr.push(item)
      item.children.forEach((child) => {
        newAreaItemArr.push(child)
      })
      newAreaArr.push(newAreaItemArr)
    } else {
      newCityArr.push(item)
    }
  })
  return newAreaArr.concat([newCityArr])
}

/**
 * 地址数据清洗
 */
const organizeAreaData = (areaData) => {
  const arr = []
  const areaArr = wx.$.u.deepClone(areaData)
  const quanguo = {
    ...areaData[0],
    ad_name: areaData[0].adName,
    ad_code: areaData[0].adCode,
  }
  delete quanguo.subAreaList
  arr.push(quanguo)

  function transformSubAreaList(subAreaListProp, area, level) {
    const subAreaList = area[subAreaListProp] || []
    return subAreaList.map(subArea => {
      const { areaType,
        areaStatus,
        cityCode,
        subAreaList,
        adName: ad_name, adCode: ad_code, id, pid, letter, name, ...itemArea } = subArea
      const transformedChildren = transformSubAreaList('subAreaList', subArea, level + 1)
      return {
        ...itemArea,
        id: String(id),
        pid: String(pid),
        letter,
        name,
        ad_name,
        ad_code,
        level,
        children: transformedChildren.length ? transformedChildren : [],
        [subAreaListProp]: undefined,
      }
    })
  }

  const area = areaArr[0]
  const transformedSubAreaList = transformSubAreaList('subAreaList', area, 1)
  area.subAreaList = transformedSubAreaList
  return arr.concat(transformedSubAreaList)
}

/** 获取cdn的地址 */
export async function fetchAreaData(url): Promise<Array<any>> {
  let areaData = []
  const result: any = await asyncRequest({
    url,
    data: { wechat_token: miniConfig.token }, // 暂时
    header: { 'content-type': 'application/json' },
    method: 'GET',
    dataType: 'json',
    responseType: 'text',
  }).catch(() => {
    return { data: [] }
  })
  if (result && result.data) {
    if (wx.$.u.isArrayVal(result.data)) {
      areaData = result.data
    } else {
      try {
        areaData = JSON.parse(result.data)
      } catch (err) {
        areaData = []
      }
    }
  }
  return areaData
}

/** 判断是否是直辖市、港澳台等
 * @returns {ITreeArea['special']}
 * - `region`: 直辖市: 北京，上海，天津，重庆
 * - `hmt`: 港澳台
 */
export function getRegion(adCode: string | number): ITreeArea['special'] {
  const str = `${adCode}`.substring(0, 2)
  if (['81', '82', '71'].indexOf(str) > -1) {
    return 'hmt'
  }
  if (['11', '12', '31', '50'].indexOf(str) > -1) {
    return 'region'
  }
  return ''
}

/**
 * @params: type 'recruit' 招工 | 'resume' 找活 | 'used' 二手
 * @return: 城市数据 {id: city_id,name: city_name}
 * @description: 列表中获取当前储存城市 如果是找活优先2级 招工优先3级
 */
export const getLocalCity = (type: 'recruit' | 'resume' | 'used' | 'logistics' | string): CityUseData | undefined => {
  const { userLocationCity } = store.getState().storage
  const { resumeCityObj, recruitCityObj, resumeSearchCityObj, recruitSearchCityObj } = userLocationCity || {}
  let historyCity = null
  if (type == 'resume') {
    historyCity = resumeSearchCityObj || resumeCityObj || userLocationCity
  } else if (type == 'recruit') {
    historyCity = recruitSearchCityObj || recruitCityObj || userLocationCity
  } else {
    historyCity = userLocationCity
  }
  if (historyCity) {
    // 处理成新的城市数据
    const cityInfo = []
    if (historyCity.parents && historyCity.parents.length) {
      historyCity.parents.forEach((item) => {
        cityInfo.push(item)
      })
      const nHistoryCity = { ...historyCity }
      delete nHistoryCity.parents
      cityInfo.push(nHistoryCity)
    } else {
      cityInfo.push(historyCity)
    }

    let { level } = historyCity
    if (type !== 'recruit' && level === 3) {
      level = 2
    }
    try {
      let data: CityUseData
      if (level === 1 || !level) {
        data = { id: cityInfo[0].id, name: cityInfo[0]?.name, letter: cityInfo[0].letter }
      } else {
        const city = cityInfo[level - 1]
        data = { id: city.id, name: city?.name, letter: city.letter }
      }
      return data.id ? data : undefined
    } catch (err) {
      return undefined
    }
  } else {
    return undefined
  }
}

// ! 获取用户与项目地址之间的距离文本
/**
 * @name 格式化输出的单位
 * @param distance 距离 m
 * @param decimalPlace 小数位数 1
 * @param {Boolean} isHalfAdjust 是否四舍五入 默认：false
 * @return string 格式化的距离文本
 */
export function formatDistanceUnits(distance: number, decimalPlace = 1, isHalfAdjust = false) {
  if (distance < 0) {
    return ''
  }
  if (distance <= 100) {
    return '<100m'
  }
  if (distance < 999) {
    return `${Math.ceil(distance)}m`
  }
  if (isHalfAdjust) {
    const roundedNum = Math.round(distance / 1000)
    return `${roundedNum}km`
  }
  return `${(distance / 1000).toFixed(decimalPlace < 0 ? 2 : decimalPlace)}km`
}

/**
 * @name 获取两个经纬度之间的距离
 * @return {number} distance 例如: 1234.5
 */
export function getLongitudeAndLatitudeDistance(lng1 = 0, lat1 = 0, lng2 = 0, lat2 = 0) {
  // 计算两点位置距离
  const rad1 = (lat1 * Math.PI) / 180.0
  const rad2 = (lat2 * Math.PI) / 180.0
  const a = rad1 - rad2
  const b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0
  const r = 6378137 // 地球半径
  /*
    if (distance > 1000){
      distance = Math.round(distance / 1000);
    } */
  return r * 2 * Math.asin(Math.sqrt(Math.sin(a / 2) ** 2 + Math.cos(rad1) * Math.cos(rad2) * Math.sin(b / 2) ** 2))
}

/**
 * @name 获取两个经纬度之间的距离文本
 * @return 获取格式化后的距离文本
 */
export function getLongitudeAndLatitudeText(lng1 = 0, lat1 = 0, lng2 = 0, lat2 = 0) {
  return formatDistanceUnits(getLongitudeAndLatitudeDistance(lng1, lat1, lng2, lat2), 1, true)
}

/**
 * @name 获取用户与项目地址之间的距离文本
 * @param {StringConstructor} location 项目经纬度
 * @return 获取格式化后的距离文本
 */
export function getProjectAddressDistanceTxt(projectLocation?: string | null, userLocation?: string, isHalfAdjust = false): string {
  // 请求过的不再请求
  if (projectLocation && String(projectLocation).includes('m')) {
    return projectLocation
  }
  if (!projectLocation || !String(projectLocation).trim()) {
    return ''
  }
  const [lng1, lat1] = getLongitudeAndLatitudeArr(projectLocation) || [] // 获取项目经纬度数组
  if (!lng1 && !lat1) {
    return ''
  }
  const sUserLocation = storage.getItemSync('userLocation')
  const LUserLocation = userLocation || sUserLocation // 获取本地用户经纬度
  if (!LUserLocation) {
    return ''
  }
  const { latitude: lat2, longitude: lng2 } = formatOutPutLocationData(LUserLocation)// 格式化本地经纬度
  return formatDistanceUnits(getLongitudeAndLatitudeDistance(lng1, lat1, +lng2, +lat2), 1, isHalfAdjust)
}

interface ILocation {
  latitude: string
  longitude: string
}

/**
 * @name 获取项目地址经纬度
 * @param location 项目经纬度文本
 * @returns {number[]} number[] 例如: [经度,纬度] 看情况后端返回的数据位置可能会变
 */
export function getLongitudeAndLatitudeArr(location?: string | ILocation | null): number[] {
  if (!location || !String(location).trim()) {
    return []
  }
  const { latitude, longitude } = formatOutPutLocationData(location)

  const lat = parseInt(latitude, 10)
  if (lat > 90 || lat < -90) {
    return []
  }
  return [+longitude || 0, +latitude || 0]
}

export function formatOutPutLocationData(location: string | ILocation | null) {
  let latitude = ''
  let longitude = ''
  if (typeof location === 'string') {
    const [longitudeStr, latitudeStr] = location.split(',')
    latitude = latitudeStr
    longitude = longitudeStr
  } else {
    latitude = location.latitude
    longitude = location.longitude
  }
  return { latitude, longitude }
}

/**
 * 根据adcode判断是否为直辖市或者为特别行政区和特殊的省份
 * 只要adcode前两个数字在adcodeRegion数据中，就是直辖市或者特殊的省份,那么返回true
 * a. 4个直辖市:北京市(110000)、天津市(120000)、上海市(310000)、重庆市(500000)。
 * b. 2个特别行政区:香港特别行政区(810000)、澳门特别行政区(820000)。
 * c. 1个特殊的省:台湾省(710000)。
 * @param adcode: 地区编码
 * @param type: 'peculiar'为只判断是否是港澳台; 'region' 为只判断是否是直辖市
 * @returns {boolean}
 */
export function isByAdcodeRegion(adcode: string | number, type = '') {
  const adcodeThree = `${adcode}`.substring(0, 2)
  let adcodeRegion = ['11', '12', '31', '50', '81', '82', '71']
  if (type == 'peculiar') {
    adcodeRegion = ['81', '82', '71']
  } else if (type == 'region') {
    adcodeRegion = ['11', '12', '31', '50']
  }
  return adcodeRegion.indexOf(adcodeThree) > -1
}

/**
 * 根据id判断是否为直辖市或者为特别行政区和特殊的省份
 * 只要id前三个数字在idRegion数据中，就是直辖市或者特殊的省份,那么返回true
 * a. 4个直辖市:北京市(2)、天津市(27)、上海市(25)、重庆市(32)。
 * b. 2个特别行政区:香港特别行政区(33)、澳门特别行政区(34)。
 * c. 1个特殊的省:台湾省(35)。
 * @param id: 地区id
 * @returns {boolean}
 */
export function isByIdRegion(id: string | number, arr = []) {
  const idThree = `${id}`
  const idRegion = ['2', '27', '25', '32', '33', '34', '35']
  return idRegion.concat(arr).indexOf(idThree) > -1
}

/*
 * @Date: 2022-03-16 10:48:06
 * @Description: 工具库
 */
import { tryPromise } from '@/utils/tools/common/index'

/** 获取招工和找活 置顶的热门城市 */
export async function getRecruitOrResumeHotAreas() {
  return tryPromise(
    wx.$.javafetch['POST/lbs/v1/area/hot']().then((res) => {
      if (res.error) {
        return []
      }
      return res.data.map((item) => {
        const is_hot = item.hot.code || 0
        return {
          id: item.id,
          letter: item.letter,
          name: item.name,
          ad_name: item.name,
          pid: item.pid,
          is_hot,
          // item,
        }
      })
    }),
    [],
  )
}

/** 招工置顶、找活置顶专用处理逻辑 */
export function setCityTop(citys: []) {
  const pages = getCurrentPages()
  const filterPageArr = pages.filter((page) => ['subpackage/topset/topmset/index', 'subpackage/topset/rectopmset/index'].includes(page.route))
  if (filterPageArr && filterPageArr.length > 0) {
    filterPageArr[0].saveCitys(citys)
  }
}

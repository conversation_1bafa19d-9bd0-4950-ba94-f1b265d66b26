.v4-recommend-workers {
  padding-bottom: 16rpx;
  position: relative;
  z-index: 1;

  >.content {
    background: linear-gradient(180deg, #e8f6ff 0%, #f5f6fa 480rpx);
    position: relative;
    z-index: 1;
  }

}



.title {
  padding: 24rpx 24rpx 8rpx 24rpx;
  color: rgba(0, 0, 0, 0.85);
  font-size: 34rpx;
  font-weight: bold;

  >.highlight {
    color: #0092FF;
  }
}

.view-more {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background: #fff;
  padding: 20rpx 24rpx;
  color: #0092FF;
  font-size: 30rpx;
  border-radius: 16rpx;
  margin: 0 24rpx;
}

.not-have-more {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  padding: 20rpx 24rpx;
  color: #999999;
  font-size: 30rpx;
  border-radius: 16rpx;
  margin: 0 24rpx;
}

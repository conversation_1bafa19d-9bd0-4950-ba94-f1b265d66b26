/*
 * @Date: 2022-07-11 11:33:20
 * @Description: 换微信
 */

import { handleExchange, reportBuryingPoint } from '../../utils'
import { RootState, storage, store } from '@/store/index'

Component(class extends wx.$.Component {
  useStore(state: RootState) {
    const { storage } = state
    return {
      role: storage.userChooseRole,
    }
  }

  properties = {
    msgInfo: { type: Object, value: {} },
    top: { type: Number, value: 0 },
    bottom: { type: Number, value: 0 },
  }

  observers = {
  }

  data = {
  }

  lifetimes = {
  }

  async onRefuse() {
    await wx.$.u.waitAsync(this, this.onRefuse, [], 1000)
    this.exChangeResumeFile(false)
  }

  async onAgree() {
    await wx.$.u.waitAsync(this, this.onAgree, [], 1000)
    this.exChangeResumeFile(true)
  }

  exChangeResumeFile(agree) {
    const { msgInfo } = this.data as DataTypes<typeof this>
    const role = storage.getItemSync('userChooseRole')
    const exchangeMsgId = wx.$.u.getObjVal(msgInfo, 'payload.data.content.exchangeMsgId', '')
    const isCompositeCard = wx.$.u.getObjVal(msgInfo, 'payload.data.content.isCompositeCard', '')
    let exchangeType:any = 'EXCHANGE_RESUME_FILE'
    if (isCompositeCard) {
      exchangeType = 'SEARCH_CHAT_CARD_ATTACHMENT'
    }
    if (role == 1) {
      const fileInfo = wx.$.u.getObjVal(msgInfo, 'payload.data.content.fileInfo', '')
      const { fileId } = fileInfo || {}
      if (!exchangeMsgId || !fileId) {
        wx.$.msg('消息异常,请重新发送消息或联系客服')
        return
      }
      handleExchange({ exchangeMsgId, agree, exchangeType }, { resumeFileId: fileId })
    } else if (agree) {
      // 老板请求简历，工人需要发送简历卡片
      this.triggerEvent('bossreqfile', { exchangeMsgId, exchangeType })
    } else {
      handleExchange({ exchangeMsgId, agree, exchangeType })
    }
  }

  async onCompositeTel() {
    await wx.$.u.waitAsync(this, this.onCompositeTel, [], 1000)
    const { msgInfo } = this.data as DataTypes<typeof this>
    const exchangeMsgId = wx.$.u.getObjVal(msgInfo, 'payload.data.content.exchangeTelMsgId', '')
    handleExchange({ exchangeMsgId, agree: true, exchangeType: 'SEARCH_CHAT_CARD_TEL' })
  }

  async onCompositeWechat() {
    await wx.$.u.waitAsync(this, this.onCompositeWechat, [], 1000)
    const { msgInfo } = this.data as DataTypes<typeof this>
    const exchangeMsgId = wx.$.u.getObjVal(msgInfo, 'payload.data.content.exchangeWechatMsgId', '')
    const { userInfo } = store.getState().user
    const { userBaseObj } = userInfo || {} as any
    const { wechatNumber = '' } = userBaseObj || {}
    const exchangeType = 'SEARCH_CHAT_CARD_WECHAT'
    if (!wechatNumber) {
      this.triggerEvent('addwechat', { exchangeWxMsgId: exchangeMsgId, exchangeType })
      return
    }
    handleExchange({ exchangeMsgId, agree: true, exchangeType })
  }

  async onPreview() {
    await wx.$.u.waitAsync(this, this.onPreview, [], 1000)
    const { msgInfo } = this.data as DataTypes<typeof this>
    const exchangeMsgId = wx.$.u.getObjVal(msgInfo, 'payload.data.content.exchangeMsgId', '')
    const file = wx.$.u.getObjVal(msgInfo, 'payload.data.content.fileInfo', '')
    const { fileId, fileName: oFileName } = file || {}
    reportBuryingPoint(msgInfo, 'attachment_resume_card_click', { button_name: '点击预览附件简历' })
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/reach/v2/im/exchange/lookResumeFile']({ exchangeMsgId, fileId }).then((res) => {
      wx.hideLoading()
      const { data, code, message } = res || {}
      if (code != 0) {
        wx.$.msg(message || '请求失败,请稍后重试')
        return
      }
      const { fileName, fileUrl } = data
      wx.$.l.attachmentPath({
        fileName: oFileName || fileName,
        fileUrl,
        generateType: 3,
        exchangeMsgId,
        fileId,
      })
    }).catch((err) => {
      wx.hideLoading()
      const { error, message } = err || {}
      let msg = '请求异常,请稍后重试'
      if (error && message) {
        msg = message
      }
      wx.$.msg(msg)
    })
  }
})

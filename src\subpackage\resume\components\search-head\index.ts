import { actions, dispatch, store } from '@/store/index'

Component(class extends wx.$.Component {
  useStore(state: StoreRootState) {
    return {
      searchPageSltedJob: state.storage.searchPageSltedJob,
    }
  }

  properties = {
    keywords: { type: String, value: '' },
    /** 是否显示输入框 */
    isInput: { type: Boolean, value: true },
  }

  externalClasses = ['custom-box']

  data = {
    // 是否选中输入框
    keyBoardFocus: false,
    value: '',
    // 职位数据
    jobList: [],
    job: {},
  }

  observers = {
    keywords(v) {
      this.setData({ value: v })
    },
    searchPageSltedJob(v) {
      this.setData({ job: v })
    },
  }

  lifetimes = {
    attached() {
      this.initJobData()
    },
  }

  async initJobData() {
    const { login } = store.getState().storage.userState
    if (!login) {
      return
    }
    const res = await wx.$.javafetch['POST/job/v3/manage/job/tab/tabListV2']()
    const { searchPageSltedJob, userLocationCity } = store.getState().storage
    const { data } = res || {}
    const { tabList } = data || {}
    const sData:any = { jobList: [] }
    if (wx.$.u.isArrayVal(tabList)) {
      sData.jobList = tabList.filter((tab) => tab.checkStatus == 2 && !tab.isDraft)
    }
    if (wx.$.u.isArrayVal(sData.jobList)) {
      const { jobId, title } = searchPageSltedJob || {}
      const jobIdx = sData.jobList.findIndex((job) => job.jobId == jobId)
      if (jobIdx < 0 && title != '不限职位') {
        dispatch(actions.storageActions.setItem({ key: 'searchPageSltedJob', value: sData.jobList[0] }))

        const { jobId, cityId } = sData.jobList[0] || {}
        if (jobId && cityId) {
          const item = (await wx.$.l.getAreaById(cityId)).current
          await dispatch(actions.storageActions.setItem({ key: 'userLocationCity', value: { ...userLocationCity, ...(item || {}), children: [], resumeSearchCityObj: { ...(item || {}), citys: [item], cityLen: 1 } } }))
        }
      }
    }
    if (wx.$.u.isArrayVal(sData.jobList)) {
      const jobIdArr = sData.jobList.map(item => `${item.jobId}`).concat(['0'])
      const { searchHistory } = store.getState().storage
      const { resumeobj } = searchHistory || {}
      if (wx.$.u.isArrayVal(resumeobj)) {
        const nresumeobj = resumeobj.filter((item:any) => jobIdArr.includes(`${item.jobId}`))
        dispatch(actions.storageActions.setItem({ key: 'searchHistory', value: { ...searchHistory, resumeobj: nresumeobj } }))
      }
    }
    this.setData(sData)
    this.triggerEvent('joblist', { jobList: sData.jobList })
  }

  async onSelectJob() {
    await wx.$.u.waitAsync(this, this.onSelectJob, [], 1000)
    this.triggerEvent('selectjob')
  }

  onFocus() {
    this.setData({ keyBoardFocus: true })
  }

  onBlur() {
    this.setData({ keyBoardFocus: false })
  }

  onClear() {
    this.setData({ value: '' })
    this.triggerEvent('clear')
  }

  async onClick() {
    await wx.$.u.waitAsync(this, this.onClick, [], 1000)
    this.triggerEvent('click')
  }

  onChange(e) {
    const { value } = e.detail
    this.setData({ value })
    this.triggerEvent('change', value)
  }

  async onConfirm() {
    await wx.$.u.waitAsync(this, this.onConfirm, [], 1000)
    this.triggerEvent('confirm')
  }

  async onSearch() {
    await wx.$.u.waitAsync(this, this.onSearch, [], 1000)
    this.triggerEvent('search')
  }
})

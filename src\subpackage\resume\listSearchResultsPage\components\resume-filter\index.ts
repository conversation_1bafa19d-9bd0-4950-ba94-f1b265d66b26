/*
 * @Date: 2022-01-29 09:06:45
 * @Description: 找活大列表选择器
 */

import { connect, dispatch, actions, MapStateToData, store } from '@/store/index'
import { helper } from '@/utils/index'
import { subscribeComponent } from '@/lib/mini-component-page/index'
import { getDom, getHeaderHeight, throttle } from '@/utils/tools/common/index'
import { checkScrollTop } from '@/utils/helper/list/index'

const { location } = helper
const mapStateToData: MapStateToData = (state) => {
  const { classify, listFilter, storage } = state
  const { userLocationCity, selectPositionTabId } = storage
  const { resumeFilterBubbleShow } = storage.common
  const { resumeSearchCityObj } = userLocationCity || {} as any
  const resumeAreaIdFilter = resumeSearchCityObj || userLocationCity || {}
  const { cityLen, name } = resumeAreaIdFilter || {}
  let frontName = name || '城市'
  const lastName = cityLen > 1 ? `・${cityLen}` : ''
  if (name.length >= 5) {
    frontName = `${name.slice(0, 4)}...`
  }
  return {
    isNewUser: state.user.isNewUser,
    login: state.storage.userState.login,
    resumeFilterBubbleShow,
    // 过滤条件
    resumeAreaIdFilter: { ...resumeAreaIdFilter, frontName, lastName },
    resumeSearchFilterScreen: listFilter.resumeSearchFilterScreen,
    findJobSearchOccCnt: classify.classifyConfig.findJobSearchOccCnt, // 列表可选工种数量
    resumeSearchFilterItems: listFilter.resumeSearchFilterItems,
    selectPositionTabId,
    resumeSearchCityObj,
  }
}

Component(connect(mapStateToData)(subscribeComponent({
  // 监听页面滚动是否显示气泡
  onPageScroll: throttle(function (e) {
    const { scrollTop } = e
    this.scrollTop = scrollTop
    this.scrollFliterShow(scrollTop)
  }),
})({
  properties: {
    // 是否需要滚动到顶部
    isMoveToTop: {
      type: Boolean,
      value: false,
    },
    sourcePageName: {
      type: String,
      value: '',
    },
    fixed: {
      type: Boolean,
      value: false,
    },
    // 1:列表 2.搜索结果页
    source: {
      type: Number,
      value: 1,
    },
    /** 选择工种之后的底部悬浮title */
    selectTit: { type: String, value: '我选择的职位' },
  },
  data: {
    fliterBubbleShow: false, // 筛选气泡是否显示
    currentName: '', // 当前选择的
    top: '0',
    bannerHeight: 0,
    cityObj: {
      frontName: '',
      lastName: '',
    },
    tabClassText: '选择工种',
  },
  lifetimes: {
    attached() {
      this.initFilterData()
    },
    ready() {
      dispatch(actions.listFilterActions.fetchResumesSearchFilterItems())
      const { sourcePageName } = this.data
      let ntop = '14rpx'
      if (sourcePageName == '') {
        ntop = '2rpx'
      }
      this.setData({ top: `calc(80rpx + ${getHeaderHeight(ntop)})` })
    },
  },
  pageLifetimes: {
    // 组件显示
    show() {
      this.getBubbleText()
    },
  },
  observers: {
    async selectPositionTabId(val) {
      if (val.selectItem?.jobId) {
        const type = wx.$.u.getRegion(val.selectItem.provinceId)
        const isRegion = type == 'region' || type == 'hmt'
        const oneCityName = val.selectItem.selectAreas.length ? (await wx.$.l.getAreaById(val.selectItem.selectAreas[0])).current.name : val.selectItem.cityName
        const tabAreaText = val.selectItem.selectAreas.length == 1 ? oneCityName : `${isRegion ? val.selectItem.provinceName : val.selectItem.cityName}·${val.selectItem.selectAreas.length}`
        this.setData({
          tabAreaText,
        })
      }
    },
    resumeAreaIdFilter() {
      const { resumeOption } = store.getState().storage.common
      const { city } = resumeOption || {}
      if (!city) {
        dispatch(actions.storageActions.setCommonItem({ resumeOption: { ...resumeOption, city: true } }))
      }
    },
  },
  methods: {
    // 滚动 筛选选择器气泡显示隐藏
    async scrollFliterShow(scrollTop) {
      const { resumeFilterBubbleShow, fliterBubbleShow } = this.data
      const bannerHeight = await this.getBannerHeigth()

      // 滚动判断如果关闭了就不在判断后面逻辑
      // eslint-disable-next-line sonarjs/no-redundant-boolean
      if (resumeFilterBubbleShow == false) {
        return
      }
      // 滚动大于112气泡隐藏
      if (scrollTop >= bannerHeight && fliterBubbleShow) {
        this.setData({
          bannerHeight,
          fliterBubbleShow: false,
        })
      }
      if (scrollTop <= bannerHeight && !fliterBubbleShow) {
        this.setData({
          bannerHeight,
          fliterBubbleShow: true,
        })
      }
    },

    // 获取气泡文案配置
    async getBubbleText() {
      // 获取是否显示新用户-招工/找活列表气泡提示缓存
      const { resumeFilterBubbleShow } = this.data
      const { scrollTop } = this
      const bannerHeight = await this.getBannerHeigth()
      if ((!scrollTop || scrollTop < bannerHeight) && resumeFilterBubbleShow) {
        this.setData({
          bannerHeight,
          fliterBubbleShow: true,
        })
      }
    },
    // 获取banner高度
    async getBannerHeigth() {
      let { bannerHeight } = this.data
      if (bannerHeight == 0) {
        const banner = await getDom('#banner')
        if (banner && banner.height > 0) {
          bannerHeight = banner.height - 5
          this.setData({ bannerHeight })
        } else {
          bannerHeight = 100
        }
      }
      return bannerHeight
    },
    //
    reSetFindJobSearchOccCnt() {
      const { resumeSearchFilterItems, resumeSearchFilterScreen } = this.data
      const obj = wx.$.u.deepClone(resumeSearchFilterScreen.filter)
      const nValue = {}
      let count = 0
      let label = ''
      resumeSearchFilterItems.forEach(i => {
        if (obj && obj[i.key] && !i.isAdvanced) {
          nValue[i.key] = obj[i.key]
          // 年龄筛选没有值或者是默认值不计数
          if (!(i.key === 'age' && (!nValue[i.key].length || (nValue[i.key].length == 2 && nValue[i.key][0] == 16 && nValue[i.key][1] == 46)))) {
            count += 1
          }
        }
      })
      const value = { filter: [] }
      Object.keys(nValue || {}).forEach(ky => {
        const itemObj = nValue[ky]
        if (itemObj && !wx.$.u.isEmptyObject(itemObj) && ky !== 'age') {
          value.filter.push({ filterKey: ky, filterValue: Object.keys(itemObj) })
        }
      })

      if (count > 0) {
        label += `・${count}`
      }
      dispatch(actions.listFilterActions.setState({
        resumeSearchFilterScreen: {
          label: `筛选${label}`,
          value,
          filter: nValue,
        },
      }))
    },
    // 筛选气泡点击事件
    onTipsShxClick() {
      this.setData({ fliterBubbleShow: false })
      dispatch(actions.storageActions.setCommonItem({ resumeFilterBubbleShow: false }))
    },
    // 切换筛选的显示/隐藏
    async onTogglePicker(e) {
      await wx.$.u.waitAsync(this, this.onTogglePicker, [e], 300)
      const { name } = e.currentTarget.dataset
      const { currentName: cn, isMoveToTop, sourcePageName, selectPositionTabId } = this.data

      // 点击筛选时，隐藏气泡显示
      if (name == 'resumeSearchFilterScreen' && name !== cn) {
        this.onTipsShxClick()
        const { resumeSearchFilterItems, resumeSearchFilterScreen } = this.data
        wx.$.nav.push(
          '/subpackage/tools-page/search-screen-picker/index',
          {},
          (changeToolsPage) => {
            const { search: currentChangeToolsPage } = changeToolsPage || {}
            this.triggerEvent('filterscreenchange', { filterscreen: currentChangeToolsPage })
            this.onScreePickerChange({ detail: currentChangeToolsPage, currentTarget: { dataset: { name: 'resumeSearchFilterScreen' } } })
          },
          {
            tree: resumeSearchFilterItems,
            value: resumeSearchFilterScreen.filter,
            sourcePageName,
          },
        )
      }
      // 点击地址
      if (name == 'resumeAreaIdFilter') {
        const { frontName, lastName } = this.data.resumeAreaIdFilter || {}
        const { userLocationCity } = store.getState().storage
        const { resumeCityObj, id, resumeSearchCityObj } = userLocationCity || {} as any
        const { citys, id: resId } = resumeSearchCityObj || resumeCityObj || {} as any
        let areas = []
        if (resId != null) {
          if (wx.$.u.isArrayVal(citys)) {
            areas = citys.map(item => item.id)
          } else {
            areas = [resId || id]
          }
        }
        if (!resumeSearchCityObj && selectPositionTabId.selectItem?.jobId) {
          areas = selectPositionTabId.selectItem.selectAreas
        }
        wx.$.openAddress({
          areas,
          maxNum: 9,
          level: 3,
          hideNation: false,
          selectType: 'district',
          headType: 'all',
          type: 'job',
          point: {
            source_id: '9',
            button_name: `${frontName || ''}${lastName || ''}`,
          },
        }, async ({ value }) => {
          let item = null
          if (wx.$.u.isArrayVal(value, 2)) {
            item = (await wx.$.l.getAreaById(value[0].pid)).current
            delete item.children
          } else if (wx.$.u.isArrayVal(value)) {
            item = { ...(value[0] || {}) }
          }
          const nValue = { ...userLocationCity, ...(item || {}), children: [], resumeSearchCityObj: { ...(item || {}), citys: value, cityLen: value.length } }
          this.triggerEvent('filterscreenchange', { city: nValue })
          await dispatch(actions.storageActions.setItem({ key: 'userLocationCity', value: nValue }))
          this.triggerEvent('refresh', { name })
        })
      }
      // 筛选关闭
      if (name === cn) {
        console.log('-1')
      } else if (isMoveToTop) {
        /** 未吸顶状态下滚动吸顶 */
        const banner = await getDom('#banner') // 缓存 dom
        checkScrollTop(this.scrollTop || 0, (banner?.height || 0), ({ isScroll }) => {
          this.setData({ disableAnimeIfScroll: !!isScroll }) // 滚动则关闭动画 // 无动画状态开启组件后再开启动画
        })
      }
      const currentName = name === cn ? '' : name
      this.triggerEvent('togglepicker', { currentName })
      setTimeout(() => {
        this.setData({ currentName })
      }, 200)
    },
    // 关闭
    onClose() {
      this.setData({ currentName: '' })
    },
    /** 当用户点击了筛选后 */
    async onFilterChange(e) {
      const { name } = e.currentTarget.dataset
      const { label, value } = e.detail
      const { common } = store.getState().storage
      const { perRecommendationSet } = common || {}
      const { resumeList } = perRecommendationSet || {}
      if (value == 'recommend' && !resumeList) {
        wx.$.confirm({
          content: '您需要打开个性化推荐功能，才能继续查看“智能推荐”简历',
          cancelText: '取消',
          confirmText: '打开推荐',
        }).then(() => {
          wx.$.r.push({ path: '/subpackage/member/per_recommendation/index' })
        })
        return
      }
      // 更新本地存储
      if (name != 'class') {
        await dispatch(actions.storageActions.setItem({ key: name, value: { label, value } }))
      }
      this.triggerEvent('refresh', { name })
    },
    async onScreePickerChange(e) {
      const { name } = e.currentTarget.dataset
      const screeFilter = e.detail
      if (!screeFilter) {
        this.onClose()
        return
      }
      const value = { filter: [] }
      Object.keys(screeFilter || {}).forEach(ky => {
        const itemObj = screeFilter[ky]
        if (itemObj && !wx.$.u.isEmptyObject(itemObj) && ky !== 'age') {
          value.filter.push({ filterKey: ky, filterValue: Object.keys(itemObj) })
        }
      })
      let label = '筛选'
      const count = Object.keys(screeFilter || {}).map((key) => {
        const item = screeFilter[key]
        if (key === 'age' && !item.length) {
          return ''
        }
        return key
      }).filter(item => !wx.$.u.isEmptyObject(screeFilter[item] || {})).length
      if (count > 0) {
        label += `・${count}`
      }
      await dispatch(actions.listFilterActions.setState({ [name]: { filter: screeFilter, value, label } }))
      this.onClose()
      this.triggerEvent('refresh')
    },
    /** 初始化城市、工种、排序等筛选组件的信息 */
    async initFilterData() {
      await location.getLocalCity('resume')
    },
  },
})))

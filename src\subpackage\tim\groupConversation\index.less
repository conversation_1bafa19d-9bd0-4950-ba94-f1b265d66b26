.header-out {
  justify-content: space-between !important;
  margin: 0 !important;
}

.home-class {
  left: 85rpx !important;
}

.header-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 24rpx;
  font-size: 34rpx;
  font-weight: bold;
}

.header-icon {
  border-radius: 50rpx;
  border: 1rpx solid rgba(151, 151, 151, 0.2);
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 28rpx;
}

.title-class {
  margin-right: 8rpx !important;
  max-width: 290rpx !important;
}

.body {
  display: flex;
  flex-direction: column;
  position: relative;
}

.sv-v-w-height {
  height: 40rpx;
  width: 1rpx;
}

.c-txt {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.45);
  display: flex;
  justify-content: center;
  margin: 0 0 40rpx;
}

.chat_warning {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.45);
  margin: 0 32rpx 40rpx;
  text-align: center;
  line-height: 40rpx;
}

.btmbtn-body {
  position: fixed;
  bottom: 0;
  min-height: 112rpx;
  background: #fff;
  width: 100vw;
}

.btmbtn-body-o {
  opacity: 0;
}

.scroll_view_btm {
  height: 1rpx;
}

.msg-main {
  display: flex;
  justify-content: flex-end;
  margin: 0rpx 24rpx 40rpx;
}

.main-start {
  justify-content: flex-start;
}

.msg-sys {
  justify-content: center;
}

.destroy_v {
  position: fixed;
  bottom: 0;
  background: #fff;
}

.destroy_txt {
  width: 100vw;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.longpress-tk-v {
  position: relative;
}

.recharge-tip {
  height: 84rpx;
  background: #f5f6fa;
  width: 100vw;
  font-size: 30rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
  padding: 0 32rpx;
  display: flex;
  align-items: center;
}

.recharge-tip-text {
  & + .recharge-tip-text {
    margin-left: 4rpx;
  }
}

.btnBg {
  background: #f5f6fa;
}

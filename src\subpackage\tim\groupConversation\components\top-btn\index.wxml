<floating-card-block wx:if="{{toUserBlock}}" conversation="{{conversation}}" />
<block wx:else>
  <view class="btn-v" catch:touchmove="onDisableMove">
    <view wx:if="{{role == 1}}" class="btn-item" data-type="call" catch:tap="onExChangeResumeFileClick">
      <image class="img" wx:if="{{rightsStatusInfo.exchangeResumeFile.status == 1 || rightsStatusInfo.exchangeResumeFile.status == 3}}" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_send_jl.png" />
      <image wx:else class="img" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_oo_jl.png" />
      <view class="txt {{rightsStatusInfo.exchangeResumeFile.status == 1 || rightsStatusInfo.exchangeResumeFile.status == 3?'':'txt-no'}}">
        {{rightsStatusInfo.exchangeResumeFile.status == 2 ? '请求中' : '求简历'}}
      </view>
    </view>
    <view class="btn-item" data-type="exchangetel" catch:tap="onExChangeTelClick">
      <image wx:if="{{rightsStatusInfo.exchangeTel.status == 1 || rightsStatusInfo.exchangeTel.status == 3}}" class="img" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_call_huan.png" />
      <image wx:else class="img" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_exchangetel_no.png" />
      <view class="txt {{rightsStatusInfo.exchangeTel.status == 1 || rightsStatusInfo.exchangeTel.status == 3?'':'txt-no'}}">
        {{rightsStatusInfo.exchangeTel.status == 2?'请求中':'换电话'}}
      </view>
    </view>
    <view class="btn-item" data-type="exchangewechat" catch:tap="onExChangeWechatClick">
      <image wx:if="{{rightsStatusInfo.exchangeWechat.status == 1 || rightsStatusInfo.exchangeWechat.status == 3}}" class="img" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_change_wechat.png" />
      <image wx:else class="img" src="https://cdn.yupaowang.com/yp_mini/images/xjj/exchange_wechat_no.png" />
      <view class="txt {{rightsStatusInfo.exchangeWechat.status == 1 || rightsStatusInfo.exchangeWechat.status == 3?'':'txt-no'}}">
        {{rightsStatusInfo.exchangeWechat.status == 2?'请求中':'换微信'}}
      </view>
    </view>
    <view wx:if="{{role == 2}}" class="btn-item" data-type="call" catch:tap="onExChangeResumeFileClick">
      <image class="img" wx:if="{{rightsStatusInfo.exchangeResumeFile.status == 1 || rightsStatusInfo.exchangeResumeFile.status == 3}}" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_send_jl.png" />
      <image wx:else class="img" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_oo_jl.png" />
      <view class="txt {{rightsStatusInfo.exchangeResumeFile.status == 1 || rightsStatusInfo.exchangeResumeFile.status == 3?'':'txt-no'}}">
        {{rightsStatusInfo.exchangeResumeFile.status == 2 ? '请求中' : '发简历' }}
      </view>
    </view>
    <block wx:if="{{role == 1 && inviteStatus.showCreateIcon}}">
      <view wx:if="{{!inviteStatus.status || inviteStatus.status == '-999'}}" class="btn-item" data-name="约面试" catch:tap="onBossInterviewClick">
        <image wx:if="{{!inviteStatus.isReply}}" class="img" src="https://cdn.yupaowang.com/yupao_common/856a8730.png" />
        <image wx:else class="img" src="https://cdn.yupaowang.com/yupao_common/c0e1285a.png" />
        <view class="txt {{!inviteStatus.isReply ?'txt-no' :''}}">约面试</view>
      </view>
      <view wx:elif="{{inviteStatus.status == '1'}}" class="btn-item" data-name="面试待接受" catch:tap="onBossInterviewClick">
        <image class="img" src="https://cdn.yupaowang.com/yupao_common/8355bd31.png" />
        <view class="txt">面试待接受</view>
      </view>
      <view wx:elif="{{inviteStatus.status == '2'}}" class="btn-item" data-name="即将面试" catch:tap="onBossInterviewClick">
        <view class="img-bg">
          <view class="uiv-txt">{{upcgIntvw.day}}</view>
          <view class="uiv-time-txt">{{upcgIntvw.time}}</view>
        </view>
        <view class="txt">即将面试</view>
      </view>
      <view wx:elif="{{inviteStatus.status == '3' || inviteStatus.status == '4' || inviteStatus.status == '5'}}" class="btn-item" data-name="{{inviteStatus.status == 3 ? '面试已拒绝' : (inviteStatus.status == 5 ? '面试已取消' : '面试已超时') }}" catch:tap="onBossInterviewClick">
        <image class="img" src="https://cdn.yupaowang.com/yupao_common/747e66c5.png" />
        <view wx:if="{{inviteStatus.status == '3'}}" class="txt">面试已拒绝</view>
        <view wx:elif="{{inviteStatus.status == '5'}}" class="txt">面试已取消</view>
        <view wx:else class="txt">面试已超时</view>
      </view>
      <view wx:elif="{{inviteStatus.status == '6'}}" class="btn-item" data-name="面试已开始" catch:tap="onBossInterviewClick">
        <image class="img" src="https://cdn.yupaowang.com/yupao_common/c0e1285a.png" />
        <view class="txt">面试已开始</view>
      </view>
      <view wx:elif="{{inviteStatus.status == '7'}}" class="btn-item" data-name="面试已完成" catch:tap="onBossInterviewClick">
        <image class="img" src="https://cdn.yupaowang.com/yupao_common/a7d8747c.png" />
        <view class="txt">面试已完成</view>
      </view>
    </block>
    <block wx:if="{{role == 2 && inviteStatus.showCreateIcon}}">
      <view wx:if="{{inviteStatus.status == '1'}}" class="btn-item" data-name="面试待接受" catch:tap="onWorkerInterviewClick">
        <image class="img" src="https://cdn.yupaowang.com/yupao_common/8355bd31.png" />
        <view class="txt">面试待接受</view>
      </view>
      <view wx:elif="{{inviteStatus.status == '2'}}" class="btn-item" data-name="即将面试" catch:tap="onWorkerInterviewClick">
        <view class="img-bg">
          <view class="uiv-txt">{{upcgIntvw.day}}</view>
          <view class="uiv-time-txt">{{upcgIntvw.time}}</view>
        </view>
        <view class="txt">即将面试</view>
      </view>
      <view wx:elif="{{inviteStatus.status == '3' || inviteStatus.status == '4' || inviteStatus.status == '5'}}" class="btn-item" data-name="{{inviteStatus.status == 3 ? '面试已拒绝' : (inviteStatus.status == 5 ? '面试已取消' : '面试已超时') }}" catch:tap="onWorkerInterviewClick">
        <image class="img" src="https://cdn.yupaowang.com/yupao_common/747e66c5.png" />
        <view wx:if="{{inviteStatus.status == '3'}}" class="txt">面试已拒绝</view>
        <view wx:elif="{{inviteStatus.status == '5'}}" class="txt">面试已取消</view>
        <view wx:else class="txt">面试已超时</view>
      </view>
      <view wx:elif="{{inviteStatus.status == '6'}}" class="btn-item" data-name="面试已开始" catch:tap="onWorkerInterviewClick">
        <image class="img" src="https://cdn.yupaowang.com/yupao_common/c0e1285a.png" />
        <view class="txt">面试已开始</view>
      </view>
      <view wx:elif="{{inviteStatus.status == '7'}}" class="btn-item" data-name="面试已完成" catch:tap="onWorkerInterviewClick">
        <image class="img" src="https://cdn.yupaowang.com/yupao_common/a7d8747c.png" />
        <view class="txt">面试已完成</view>
      </view>
    </block>
  </view>
  <floating-card-vie-banner-info wx:if="{{role == 1 && !conversation.isSelf && otherStatusInfo.vieBannerInfo.showBanner}}" conversation="{{conversation}}" />
  <floating-card-block-resumes wx:if="{{role == 2 && otherStatusInfo.companyBlockInfo.isBlock}}" conversation="{{conversation}}" bind:refresh="onRefreshConversation" />
  <block wx:else>
    <floating-card-recruit conversation="{{conversation}}" wx:if="{{infoDetail.relatedInfoType == 1}}" bind:change="onChange" />
    <floating-card-resumes conversation="{{conversation}}" wx:if="{{infoDetail.relatedInfoType == 2}}" bind:change="onChange" />
  </block>
</block>
<!-- 换电话 -->
<exchange-tel visible="{{isExChangeTelShow}}" bind:close="onExChangeTelClose" />
<!-- 换微信 -->
<exchange-wechat visible="{{isExChangeWechatShow}}" bind:close="onExChangeWechatClose" bind:sign="onExWechatChange" />
<!-- 附件简历 -->
<exchange-resume-file visible="{{isExChangeResumeFileShow}}" fileList="{{fileList}}" exchangeMsgId="{{exchangeMsgId}}" exchangeType="{{exchangeType}}" bind:close="onExChangeResumeFileClose" />
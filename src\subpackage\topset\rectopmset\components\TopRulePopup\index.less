.wrap {
  width: 100%;
  box-sizing: border-box;
  background: #f5f6fa;
  overflow: hidden;
  height: 56vh;
  border-radius: 16rpx 16rpx 0 0;
  position: relative;
  display: flex;
  flex-direction: column;

  .wrapHeader {
    height: 112rpx;
    padding-top: 24rpx;
    background: linear-gradient(to bottom, #cae8ff, #f5f6fa);
  }

  .topCloseIcon {
    position: absolute;
    top: 24rpx;
    right: 24rpx;
    width: 40rpx;
    height: 40rpx;
    z-index: 999;
  }

  .warpContent {
    width: 100%;
    display: flex;
    flex: 1;
    position: relative;
    flex-direction: column;
    overflow: auto;
  }
}

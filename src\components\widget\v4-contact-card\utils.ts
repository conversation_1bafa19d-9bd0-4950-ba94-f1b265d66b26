import { dispatch, actions, store } from '@/store/index'
import { helper } from '@/utils/index'

/** @name 获取埋点数据 */
function getBuryingPointData(item, otherInfoData: any = null) {
  const info = {
    source: item.source || '',
    source_id: item.source_id || '',
    location_id: `${item.location_id || ''}`,
    pagination: item.pagination || '',
    pagination_location: item.pagination_location || '',
    ...(item.buriedPointData || {}),
    ...(otherInfoData || {}),
  }
  return { id: item.infoId, info }
}

/** @name 获取招工或找活详情预备数据 */
function getRecruitOrResumeDetailPrepareData(item, cardType) {
  const { userChooseRole } = store.getState().storage
  // 为详情页准备数据
  const evaluateType = cardType === 'myContacted' ? 'my_contact' : 'contact_me'
  return { cardType, evaluateType, info_id: item.infoId, user_id: userChooseRole == 1 ? item?.userInfo.userId : item.userId, expense_id: userChooseRole == 1 ? item.expenseOrRightId : item.expenseId }
}

/** @name 跳转到招工详情页面 */
export async function goToRecruitDetailPage(pageData, item) {
  // 我联系的人-老板卡片 信息属于代发布外呼在审核中...
  if (item.contactStatus == 2) {
    wx.$.alert({ title: '人工客服正在核实该条好活信息', isPadTop: true })
    return
  }

  const { cardType, pageOrigin } = pageData

  // 为详情页准备数据
  const dataCont = getRecruitOrResumeDetailPrepareData(item, cardType)
  dispatch(actions.myContactActions.setDataCont(dataCont))

  // 跳转到招工详情 showPopType = '' | 'evaluate' 进入详情弹出的弹框框，目前是有评论弹框
  const origin = pageOrigin === 'myContactHistory' ? pageOrigin : ''
  const query = { origin, source: '联系记录', type: cardType, id: item.infoId, showPopType: '' }

  /** 详情页埋点使用字段 */
  dispatch(actions.recruitDetailActions.setState({ buryingPoint: getBuryingPointData(item) }))
  delete query.source

  const { userId, login: isLogin } = store.getState().storage.userState
  const isMyRecruitCard = item.userId == userId && item.userId != 0
  const isShowModBtn = isMyRecruitCard && isLogin ? 1 : 0
  const isMyPublished = isMyRecruitCard ? 1 : 0
  const path = await wx.$.l.getRecruitDetailsPath(item.userId)
  wx.$.r.push({ path, query: { ...query, isShowModBtn, isMyPublished } })
}

/** @name 跳转到找活详情页面 */
export function goToResumeDetailPage(pageData, item) {
  const { cardType, pageOrigin, nearbyWorkerListApiSource, sourceType, sourceId } = pageData
  // 1:待审核,2:审核通过,3:审核不通过
  const { auditStatus } = item
  // 谁联系过我-师傅卡片 信息审核中或未审核 或 无子名片 uuid 则提示信息
  if (cardType === 'whoContacted' && (auditStatus == 1 || !item.uuid)) {
    wx.$.msg('用户还未发布简历')
    return
  }

  // 谁联系过我-师傅卡片 信息审核未通过状态 则提示信息
  if (cardType === 'whoContacted' && auditStatus != 2) {
    wx.$.msg('该信息暂未过审，无法查看')
    return
  }
  // 为详情页准备数据
  const dataCont = getRecruitOrResumeDetailPrepareData(item, cardType)
  dispatch(actions.myContactActions.setDataCont(dataCont))

  // 准备数据并获取名片检索详情信息
  const { subUuid, infoId: id, isReturn, pullBackSwitch } = item
  const specialArea = item.specialType || '1'
  dispatch(actions.resumeActions.setState({ info: { uuid: subUuid, id } }))

  const origin = getQueryOrigin(pageOrigin, specialArea)
  /** 详情理点使用字段  */
  const buryingPoint = {
    id: item.id,
    info: {
      request_id: item.guid,
      location_id: `${item.location_id || ''}`,
      pagination: item.pagination,
      pagination_location: item.pagination_location,
      source_id: sourceId || '',
      ...(item.buriedPointData || {}),
    },
  }
  const params = { origin,
    type: cardType,
    isReturn: isReturn || 0,
    id: item.infoId,
    uuid: item.subUuid,
    showPopType: '',
    nearbyWorkerListApiSource,
    pullBackSwitch,
    buryingPoint: JSON.stringify(buryingPoint),
    sourceType,
    sceneV2: cardType == 'whoContacted' ? '8' : '6' }
  // 跳转到找活详情 showPopType '' | 'evaluate' 进入详情弹出的弹框框，目前是有评论弹框
  wx.$.r.push({ path: '/subpackage/resume/detail/index', params })

  function getQueryOrigin(pageOrigin, specialArea): String {
    const tempObj = { 2: 'factory', 3: 'logistics' }
    const specialAreaStr = tempObj[specialArea] || ''
    const pageOriginStr = pageOrigin === 'myContactHistory' ? pageOrigin : ''
    return `${pageOriginStr}${specialAreaStr ? '_' : ''}${specialAreaStr}`
  }
}

/** @name 前往投诉页面 */
export function goToComplainPage(cardInfo) {
  const { identityType, infoId } = cardInfo
  // 1-老板 2-师傅
  const isBoss = identityType?.code == 1
  const projectId = isBoss ? '1100' : '1101'
  const complaintSource = isBoss ? '1004' : '1005'
  helper.common.isToComplaintOrClassify({ id: infoId, projectId, targetUserId: cardInfo.userId, targetId: '', complaintSource })
}

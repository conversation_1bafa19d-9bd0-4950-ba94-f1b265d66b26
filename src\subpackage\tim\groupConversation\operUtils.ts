/*
 * @Author: xia<PERSON><PERSON><PERSON><PERSON>
 * @FilePath: /yp-mini/src/subpackage/tim/groupConversation/operUtils.ts
 * @Description:
 */

import { actions, dispatch, messageQueue, store } from '@/store/index'
import { applyExchange, getChatBottomHeightOnly, getChatTopHeightOnly, getScrollLocId, reportBuryingPoint, setMsgStatus } from './utils'
import { addGuidingTips, getStartTime } from './timUtils'
import { tryPromise } from '@/utils/tools/common/index'
import { changeUserRole } from '@/utils/helper/member/communicate'
import { listExposure } from '@/utils/helper/list/index'

let timer

export const awaitTimLogin = async (conversationId?) => {
  if (wx.$.tim && wx.$.tim.isReady()) {
    dispatch(actions.messageActions.setState({ isImPageOk: true }))
  } else {
    setTimeout(() => {
      dispatch(actions.messageActions.setState({ isImPageOk: true }))
    }, 3000)
  }

  await messageQueue((state) => state.message.isImPageOk)
  /** 判断im是否已登录 1-登录 */
  const { imlogin } = store.getState().message
  let isReady = false
  if (wx.$.tim) {
    isReady = wx.$.tim && wx.$.tim.isReady()
  }
  if (!imlogin || !isReady) {
    await wx.$.l.reTimLogin()
  }
  const arr = (conversationId || '').split('_')
  const { userImAcc } = store.getState().storage
  const imAccArr = (userImAcc || '').split('_')
  if (wx.$.u.isArrayVal(arr, 4) && wx.$.u.isArrayVal(imAccArr, 2)) {
    const [acc, role] = imAccArr/*  */
    const idx = arr.findIndex((it) => it && acc && it == acc)
    if (idx >= 0) {
      const converRole = arr[idx + 1]
      if (['1', '2'].includes(`${converRole}`) && role && converRole != role) {
        await dispatch(actions.messageActions.setState({ isSyncCompleted: false, imlogin: false }))
        await changeUserRole(converRole)
      }
    }
  }

  await messageQueue((state) => state.message.imlogin)
  await messageQueue((state) => state.storage.common.timLoginState == 1)
  await messageQueue((state) => state.message.isSyncCompleted)
}

/** 获取对方给自己的备注，继续获取群消息 （正常执行业务流程-renderUI）
 * @params isNowReadReceipt-- 老板角色：需要进页面后就将未读消息设置为已读。
 */
export async function initData(options, isNowReadReceipt = true) {
  const { conversationId } = options || {}
  await awaitTimLogin(conversationId)
  wx.$.javafetch['POST/reach/v2/im/chat/getOtherUserSetRemarkToMe']({ conversationId }, { isNoToken: true })
    .then((res) => {
      const { code, data } = res || {}
      if (code == 0) {
        dispatch(actions.timmsgActions.setState({ remarkToMe: data }))
      }
    })
  await getGroupInfo.call(this, options, false, isNowReadReceipt)
}

/**
 * 获取群信息
 * @param isClickBtn:是否是点击按钮进入的
 *  */
export function getGroupInfo(options, isClickBtn = false, isNowReadReceipt = true) {
  const sData: any = { query: options }
  const { conversationId, ptype } = options
  return wx.$.javafetch['POST/reach/v2/im/chat/detailV2']({ conversationId })
    .then(async ({ data, code }) => {
      if (code == 0 && !wx.$.u.isEmptyObject(data)) {
        const { toUserImId, rightsStatusInfo, payUserImId, conversationSuffix } = data
        const conversationID = `C2C${toUserImId}`
        const nRightsStatusInfo = {}
        if (wx.$.u.isArrayVal(rightsStatusInfo)) {
          rightsStatusInfo.forEach((rsinfo) => {
            nRightsStatusInfo[rsinfo.type] = rsinfo
          })
        }
        const { userImAcc } = store.getState().storage
        const conversation = { ...data, conversationID, rightsStatusInfo: nRightsStatusInfo, isSelf: payUserImId == userImAcc }
        await dispatch(actions.timmsgActions.setState({ conversation, curCvsId: conversationID }))
        const { myMsgGroupOjb = {} } = store.getState().message
        const ogroup = myMsgGroupOjb[conversationID] || {}
        if (wx.$.u.isEmptyObject(ogroup)) {
          await wx.$.l.setMyMsgGroupByGroup({ ...ogroup, ...conversation })
        } else if (ogroup.conversationSuffix != conversationSuffix) {
          dispatch(actions.messageActions.setState({ myMsgGroupOjb: { ...myMsgGroupOjb, [conversationID]: { ...ogroup, conversationSuffix } } }))
        }
        if (!isClickBtn) {
          if (isNowReadReceipt) {
            setTimeout(() => {
              wx.$.l.sendConverReaded(conversationID)
            }, 300)
          }
          await getImMessageList.call(this, conversationID, isNowReadReceipt)
          this.setData({ ...sData })
          if (ptype == 'imlist') {
            setTimeout(() => {
              addGuidingTips.call(this)
            }, 50)
          }
        }
        this.contactBtnText()
        return { conversation, data }
      }
      if (!isClickBtn) {
        wx.$.msg('查询会话信息失败，请稍后重新').then(() => {
          wx.$.r.back()
        })
      }
      return { conversation: null, data: null }
    })
    .catch((err) => {
      if (!isClickBtn) {
        const { code } = err || {}
        if (code == 401) {
          this.setData({ toLogin: true })
        } else {
          wx.$.msg('查询会话组信息失败，请稍后重新').then(() => {
            wx.$.r.back()
          })
        }
      }
      return { conversation: null, data: null }
    }).finally(() => {
      dispatch(actions.messageActions.setState({ isCreateSession: false }))
    })
}

// 获取im消息列表
// eslint-disable-next-line sonarjs/cognitive-complexity
export async function getImMessageList(conversationID, isNowReadReceipt = true) {
  const { nextReqMessageID, messageList: oMessageList, moreMes, isShowSkeleton, page } = this.data
  if (!conversationID || !moreMes) {
    setTimeout(() => {
      dispatch(actions.timmsgActions.setState({ refresher: false }))
    }, 1000)
    return
  }
  const param: any = { conversationID, count: 15 }
  if (nextReqMessageID) {
    this.setData({ page: page + 1 })
    param.nextReqMessageID = nextReqMessageID
  } else {
    this.setData({ page: 1 })
  }
  try {
    // 获取聊天记录
    const imResponse = await wx.$.tim.getMessageList(param)
    const { data } = imResponse || {}
    const { messageList = [], isCompleted } = data || {}

    const cloneMessageList = wx.$.u.deepClone(messageList)
    // 未读消息(不包含音频消息)，已读回执
    const noSoundReadMsgList = cloneMessageList.filter(mit => !mit.isRead && mit.type != 'TIMSoundElem'
      && mit.flow == 'in' && mit.needReadReceipt && !mit.isRevoked)
    // 如果有数据 & 需要立即处理已读回执
    if (wx.$.u.isArrayVal(noSoundReadMsgList) && isNowReadReceipt) {
      await wx.$.l.msgReadReceipt(noSoundReadMsgList)
    }

    // 处理聊天记录，组装自己需要的数据
    const { msgList, isHasOldMsg } = await wx.$.l.handleMsgList(cloneMessageList, conversationID)
    console.log('msgList:', msgList)
    const sData: any = {
      messageList: [],
      nextReqMessageID: '',
      moreMes: true,
      isHasOldMsg,
    }
    if (cloneMessageList.length > 0) {
      sData.nextReqMessageID = cloneMessageList[0].ID
    }
    if (!nextReqMessageID) {
      sData.messageList = msgList
    } else {
      sData.messageList = [...msgList, ...oMessageList]
    }
    if (nextReqMessageID && msgList.length > 0) {
      sData.scrollLocId = msgList[msgList.length - 1].id
    }
    if (isCompleted || isHasOldMsg) {
      sData.moreMes = false
      await getStartTime.call(this, sData.messageList)
    }
    dispatch(actions.timmsgActions.setState({ ...sData, refresher: false }))
    setTimeout(() => {
      dispatch(actions.timmsgActions.setState({ scrollLocId: '' }))
      setTimeout(() => {
        listReport.call(this, page)
      }, 200)
    }, 500)
    if (!nextReqMessageID) {
      const len = sData.messageList.length
      const lastId = len > 0 ? sData.messageList[len - 1].id : ''
      setTimeout(() => {
        getChatBottomHeight.call(this, 'group_msg_scroll_view_btm')
        setTimeout(() => {
          getChatBottomHeight.call(this, lastId)
        }, 50)
      }, 50)
    }
    if (isShowSkeleton && !timer) {
      timer = setTimeout(() => {
        clearTimeout(timer)
        timer = null
        this.setData({ isShowSkeleton: false })
      }, 50)
    }
    this.setData({ initPullNotReadImData: noSoundReadMsgList })
  } catch (err) {
    console.log('err:', err)

    wx.$.msg('获取会话数据失败,请稍后重试').then(() => {
      wx.$.r.back()
    })
  }
}

export function listReport(nPage) {
  const { headHeight, chatTopHeight, page } = this.data
  listExposure.call(this, {
    page: nPage || page,
    elementId: '.msg-exchange',
    top: -(headHeight + chatTopHeight),
    isOnceCallShow: true,
    callshow: (res) => {
      const { item } = res || {}
      const { pType, payload } = item || {}
      const { data } = payload || {}
      const { type } = data || {}
      if (pType == 'TimChatMsg') {
        if (type == '610.1') {
          reportBuryingPoint(item, 'get_phone_number_card_exposed')
        } else if (type == '660.1') {
          reportBuryingPoint(item, 'get_wechat_card_exposed')
        } else if (type == '710.1') {
          reportBuryingPoint(item, 'attachment_resume_card_exposed')
        }
      }
    },
  })
}

// 处理2页的 未读消息，并且处理完后才 将未读变成已读
export async function handleTwoPageUnreadMsg() {
  const { conversation } = this.data

  // 会话必须是招工侧relatedInfoType-1 招工  (不需要这个判断了)
  // if (conversation?.infoDetail?.relatedInfoType != 1) {
  //   return
  // }

  const { toUserImId, isSelf } = conversation || {}

  const conversationID = `C2C${toUserImId}` || ''

  let jobId

  if (conversation?.infoDetail?.relatedInfoType == 1) {
    jobId = conversation?.infoDetail?.relatedInfoId
  }

  /** 首先进页面请求 --查看职位详情风险提示(弹窗优先级 > IM沟通风险弹窗)
   * isSelf : 需要牛人方发起的
  */
  if (isSelf && jobId && jobId != '0') {
    const results = await wx.$.javafetch['POST/griffin/v1/riskTips/viewJob']({ jobId }).catch(() => {})
    if (results.code == 0 && results?.data?.popupTitle && !this.data.isShowImChatRisk) {
      dispatch(actions.timmsgActions.setState({
        riskContent: { ...results.data },
        isShowImChatRisk: false, // IM沟通风险弹窗-- 不能弹
        isShowRisk: true, // 牛人风险弹窗  -- 弹
        havePopImChatRisk: false,
        isShowRiskAndImChatRisk: true,
      }))

      // 上报-埋点
      riskTipsReport(results.data?.riskTipsId, 1, { jobId })

      // 需要将首次拉取到的未读消息设置为已读，且处理已读回执
      await wx.$.l.sendConverReaded(conversationID)
      if (this.data.initPullNotReadImData.length > 0 && wx.$.u.isArrayVal(this.data.initPullNotReadImData)) {
        await wx.$.l.msgReadReceipt(this.data.initPullNotReadImData)
      }

      return
    }
  }

  try {
    const param: any = { conversationID, count: 15 }

    // 获取聊天记录
    const imResponse = await wx.$.tim.getMessageList(param)
    const { data } = imResponse || {}
    const { messageList = [] } = data || {}

    const formatList = wx.$.u.deepClone(messageList)

    // 马上再请求第二页
    if (messageList?.length > 0 && messageList[0].ID) {
      const twiceData = await wx.$.tim.getMessageList({ ...param, nextReqMessageID: messageList[0].ID })
      if (twiceData?.data?.messageList?.length > 0) {
        formatList.unshift(...twiceData?.data?.messageList)
      }
    }

    // 将所有未读信息过滤出来（仅限文本）
    const noReadMsgList = formatList.filter(mit => !mit.isRead && mit.type == 'TIMTextElem'
        && mit.flow == 'in' && mit.needReadReceipt && !mit.isRevoked)

    // 将未读消息的文本组装成字符串数组
    const msgContent = (noReadMsgList || []).map(mit => {
      const { payload = {} } = mit
      const { text = '' } = payload
      return text
    }) || []

    if (msgContent.length > 0 && conversation?.toUserId && conversationID) {
      // 将组装好的 文本，调用 IM沟通风险弹窗的 风控接口。
      const res = await wx.$.javafetch['POST/griffin/v1/riskTips/imChat']({ bossUserId: conversation?.toUserId || '', imId: conversationID, content: msgContent }).catch(e => {
      })

      // 如果接口返回有值 & 牛人查看职位的风险弹窗没弹起 & （实时消息接收场景 弹的IM沟通风险弹窗没有正在弹出）, 就可以弹。
      if (res.code == 0 && res?.data?.popupTitle && !this.data.isShowRisk && !this.data.isShowImChatRisk) {
        dispatch(actions.timmsgActions.setState({
          riskContent: { ...res.data },
          isShowImChatRisk: true, // IM沟通风险弹窗（拉取未读消息场景）-- 弹
          isShowRisk: false, // 牛人风险弹窗  -- 不能弹
          havePopImChatRisk: true, // 已经弹出IM沟通风险弹窗--如果再有实时消息场景，就不再弹
          isShowRiskAndImChatRisk: true,
        }))

        // 上报-埋点
        riskTipsReport(res.data?.riskTipsId, 2, { imId: conversationID, bossUserId: conversation?.toUserId })
      }
    }

    // 未读消息(不包含音频消息)，已读回执
    if (this.data.initPullNotReadImData.length > 0 && wx.$.u.isArrayVal(this.data.initPullNotReadImData)) {
      await wx.$.l.msgReadReceipt(this.data.initPullNotReadImData)
    }

    // （后置） 将当前会话的所有未读消息  设置为  已读
    await wx.$.l.sendConverReaded(conversationID)
  } catch (err) {
    console.log('会话详情的消息拉取失败')
  }
}

/**
 * 牛人风险弹窗-上报埋点
 * @param riskTipsId
 * @param riskType  1-牛人查看详情风险弹窗 2-IM会话沟通风险弹窗
 * @param extData 其他必要传参
 * @returns
 */
export function riskTipsReport(riskTipsId, riskType = 2, extData: any = {}) {
  if (!riskTipsId) {
    return
  }
  if (extData?.jobId && riskType == 1) {
    wx.$.javafetch['POST/griffin/v1/riskTips/report']({ riskTipsId, jobId: extData.jobId }).catch(() => {})
  } else if (extData?.imId && riskType == 2) {
    wx.$.javafetch['POST/griffin/v1/riskTips/report']({ riskTipsId, imId: extData.imId, bossUserId: extData?.bossUserId }).catch(() => {})
  }
}

// 获取底部聊天功能高度并更新srcollLocId
export function getChatBottomHeight(lastId?) {
  let id = lastId
  getChatBottomHeightOnly.call(this, () => {
    if (!lastId) {
      const { messageList, scrollLocId } = this.data
      id = getScrollLocId(messageList, scrollLocId)
    }
    dispatch(actions.timmsgActions.setState({ scrollLocId: id }))
    setTimeout(() => {
      dispatch(actions.timmsgActions.setState({ scrollLocId: '' }))
    }, 500)
  })
  getChatTopHeightOnly.call(this)
}

/** 处理收到或者发送的消息 */
export async function handleReceivedOrSendMsg(msg) {
  // eslint-disable-next-line @typescript-eslint/no-this-alias
  const that = this
  await wx.$.l.handleReceivedOrSendMsg({ name: 'onMessageReceived', data: [msg] }, () => {
    setTimeout(() => {
      getChatBottomHeight.call(that)
    }, 50)
  })
}

// 处理点击头像，跳转用户信息页面
export function handleAvaterClick() {
  const { conversation } = this.data
  const { conversationId } = conversation || {}
  wx.$.r.push({
    path: '/subpackage/tim/otherInfo/index',
    query: {
      conversationId,
    },
  })
}
/** 工具栏-发送图片 */
export async function toolSendImg(e) {
  const { value, noCreate } = e.detail || {}
  const { tempFilePaths, errMsg, tempFiles } = value || {}
  // eslint-disable-next-line @typescript-eslint/no-this-alias
  const that = this
  const { conversation } = this.data
  const { toUserImId } = conversation || {}
  tempFilePaths.forEach((item, idx) => {
    const imgFile: any = {
      errMsg,
      tempFilePaths: [item],
      tempFiles: [tempFiles[idx]],
    }
    wx.$.l.sendImageMessage(
      {
        to: toUserImId,
        value: imgFile,
      },
      {
        pre: (msgres) => {
          const nMsg = wx.$.u.deepClone(msgres)
          if (!noCreate) {
            nMsg.status = 'nostatus'
          }
          handleReceivedOrSendMsg.call(that, nMsg)
        },
        success: (msgres) => {
          setMsgStatus(msgres.ID, 'success')
        },
        fail: (msgres) => {
          setMsgStatus(msgres.ID, 'fail')
        },
      },
    )
  })
}
/** 工具栏-更换职位 */
export async function toolChangePosition() {
  await getRecruitDetails.call(this, true)
  const { jobToast, jobList } = this.data
  if (jobToast) {
    wx.$.msg(jobToast)
    return
  }
  if (!wx.$.u.isArrayVal(jobList)) {
    wx.$.msg('暂无正在招职位，不能更换')
    return
  }
  this.setData({ spVisible: true })
}

// 请求沟通职位接口
export async function getRecruitDetails(isLoad = false) {
  const { conversation } = store.getState().timmsg
  const { conversationId } = conversation
  let isOk = false
  isLoad && wx.showLoading({ title: '请求中...' })
  const res = await tryPromise(wx.$.javafetch['POST/reach/v2/im/chat/bossTop100JobList']({ conversationId }), {})
  isLoad && wx.hideLoading()
  const { data } = res || {}
  const { isFirstChoose, jobList, jobToast } = data || {}
  const sData:any = { jobList, jobToast }
  if (wx.$.u.isArrayVal(jobList) && jobList.length > 1) {
    if (isFirstChoose) {
      const [firstJob] = jobList
      sData.spJobId = firstJob.jobId
    }
    isOk = true
  }
  if (!jobToast && !wx.$.u.isArrayVal(jobList)) {
    sData.jobToast = '暂无正在招职位，不能更换'
  }
  this.setData(sData)
  return isOk
}

// 更换职位
export async function changeSpJob(e) {
  const { jobId } = e.detail
  this.setData({ spJobId: jobId })
  const { conversation } = store.getState().timmsg
  const { conversationId } = conversation
  wx.showLoading({ title: '请求中...' })
  const res = await tryPromise(wx.$.javafetch['POST/reach/v2/im/chat/bossChangeJob']({ conversationId, jobId }), {})
  wx.hideLoading()
  const { error, message } = res || {}
  if (error) {
    wx.$.msg(message)
    return
  }
  const { userChooseRole: role } = store.getState().storage
  if (role == 1) {
    dispatch(actions.timmsgActions.reAgainCvsInfo(conversationId))
  }
  this.setData({ spVisible: false })
}

/** 工具栏-发送位置 */
export async function toolSendLocation(e) {
  const { selectArea } = e.detail || {}
  const { name, location, district, address, houseNumber, cityName, countyName } = selectArea || {}
  const { userChooseRole: role } = store.getState().storage
  if (role == 1) {
    const extData:any = {
      location: {
        coordinates: location,
        address: `${district || ''}${address || ''}`,
        locationName: name,
      },
    }
    if (houseNumber) {
      extData.location.houseNumber = houseNumber
    }
    if (cityName) {
      extData.location.cityName = cityName
    }
    if (countyName) {
      extData.location.countyName = countyName
    }
    applyExchange('ADDRESS_LOCATION', extData)
  } else {
    const { conversation } = store.getState().timmsg
    const { toUserImId } = conversation
    const content:any = {
      detail: `${district}${address}`,
      title: name,
      location,
    }

    if (cityName) {
      content.cityName = cityName
    }
    if (countyName) {
      content.countyName = countyName
    }
    const data:any = { content, type: '8' }
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this
    wx.$.l.sendCustomMessage(
      toUserImId,
      { data: JSON.stringify(data), description: `[位置]${cityName || ''}${countyName || ''}${name || ''}`, extension: '' },
      {
        pre: (msgres) => {
          const nMsg = wx.$.u.deepClone(msgres)
          nMsg.status = 'nostatus'
          handleReceivedOrSendMsg.call(that, nMsg)
        },
        success: (msgres) => {
          setMsgStatus(msgres.ID, 'success')
        },
        fail: (msgres) => {
          setMsgStatus(msgres.ID, 'fail')
        },
      },
    )
  }
}

import CryptoJS from '@/lib/crypto/index'
import { storage, store } from '@/store/index'
import miniConfig from '@/miniConfig/index'
import { VERSION, REQUEST_VERSION } from '@/config/app'
import { common } from '@/utils/tools/index'
import { ApplyUploadParams } from './type'

const runtimeList = {
  /** 微信小程序 */
  weapp: 'WX_MINI',
  /** 百度小程序 */
  swan: 'BAIDU_MINI',
  /** 字节小程序 */
  tt: 'BD_MINI',
  /** QQ小程序 */
  qq: 'QQ_MINI',
}

/** 服务器的签名秘钥 */
const SECRET_SIGN = '*js1(Uc_m12j%hsn#1o%cn1'

/** appId集合 */
const APPID_LIST = {
  DEVELOP: '102',
  DEV: '102',
  PRE: '102',
  REL: '102',
  PRO: '102',
}

/** 申请资源上传-默认参数 */
export const applyUploadParams: ApplyUploadParams = {
  entryId: 100001,
  type: 0,
  format: 'png',
  httpMethod: 1,
}
/** json对象key排序 */
// eslint-disable-next-line consistent-return
function sortObj(obj) {
  if (obj === null) {
    return obj
  }
  if (Array.isArray(obj)) {
    return [...obj].sort().reduce((acc, item) => {
      if (typeof item === 'object') {
        acc.push(sortObj(item))
      } else {
        acc.push(item)
      }
      return acc
    }, [])
  }
  if (typeof obj === 'object') {
    return Object.keys(obj)
      .sort()
      .reduce((acc, key) => {
        if (typeof obj[key] === 'object') {
          // eslint-disable-next-line no-param-reassign
          acc[key] = sortObj(obj[key])
        } else {
          // eslint-disable-next-line no-param-reassign
          acc[key] = obj[key]
        }
        return acc
      }, {})
  }
}

function isObject(obj: any) {
  return Object.prototype.toString.call(obj) === '[object Object]'
}

function stringifyObj(t: any) {
  if (t === null) {
    return t
  }
  // 数组
  if (Array.isArray(t)) {
    const list = t.reduce((acc, item) => {
      if (isObject(item)) {
        acc.push(sortObj(item))
      } else {
        acc.push(item)
      }
      return acc
    }, [])
    return JSON.stringify(list)
  }
  // 是对象 非数据
  return Object.keys(t)
    .sort()
    .reduce((acc, key) => {
      // 数组和对象直接进入判断，开始进行排序，在sortObj判断数组就不改变顺序
      if (typeof t[key] === 'object') {
        // eslint-disable-next-line no-return-assign, no-param-reassign
        return (acc += `${key}=${JSON.stringify(sortObj(t[key]))}&`)
      }
      // eslint-disable-next-line no-return-assign, no-param-reassign
      return (acc += `${key}=${t[key]}&`)
    }, '')
}

/** 参数加密 */
async function getSign(params, timestamp, nonce) {
  const newParams = sortObj({ ...params, timestamp, nonce })
  const signString = stringifyObj(newParams) + SECRET_SIGN
  const c = await CryptoJS()
  return c.SHA256(signString).toString()
}

/** headers 上传的请求头-https://w3nu1yaadv.feishu.cn/wiki/wikcn8lkzpxwGLyI9QlFmZReaFg */
export async function uploadHeaders(params, header = {}) {
  const timestamp = +new Date()
  const nonce = Math.round(Math.random() * 999999)
  const sign = await getSign(params, timestamp, nonce)
  const systemInfo = common.getSystemName()
  const userInfo = storage.getItemSync('userState')
  const { token } = store.getState().user.webTokenData
  const headers = {
    'content-type': 'application/json',
    /** 操作系统 ios, android, windows, macos, linux */
    os: systemInfo.system,
    /** 系统版本 */
    osversion: systemInfo.systemVersion,
    /** 运行环境 WX_MINI: 微信小程序, BAIDU_MINI: 百度小程序, BD_MINI: 字节小程序, QQ_MINI: QQ小程序 */
    runtime: runtimeList[ENV_MINI_TYPE],
    /** 业务领域 鱼泡招聘业务 */
    business: 'YPZP',
    /** 包名 */
    packagename: miniConfig.appid,
    /** 后端请求接口的版本-必传 */
    packageversion: REQUEST_VERSION,
    /** 应相同的package下，对于不同参数带来的渠道差异，全部小写 */
    channel: systemInfo.model,
    /** 签名时间戳 */
    timestamp,
    /** 随机数，[1, 999999]之间的整数 */
    nonce,
    /** 业务应用id，每个业务独立进行申请。 */
    appId: APPID_LIST[ENV_DEVELOPMENT],
    /** 多媒体SDK版本 */
    sdkVersion: '1.0.1',
    /** 应用版本，应用版本。 */
    appVersion: VERSION,
    /** 用户登录态token */
    token: token || userInfo.token || '',
    // uid: userInfo.userId || '',
    /** 签名 */
    sign,
    ...header,
  }

  return headers
}

type TopReportData = {
  /** 名称 */
  name?: string
  /** 页面名 */
  page_name?: string
  /** 点击行为 */
  click?: string
}

/** 埋点 */
export const buriedPoint = (eventName: string, reportData: TopReportData = {}) => {
  /** minitopUserPopupExposure | minitopUserPopupClick */
  // 置顶招工弹框曝光 | 置顶招工弹框点击
  const newReportData = reportData || {}
  wx.$.collectEvent.event(eventName, { page_name: '招工置顶', name: '置顶挽留', ...newReportData })
}

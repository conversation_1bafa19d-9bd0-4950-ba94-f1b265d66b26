<view class="msg-txt-v">
  <view class="msg-txt">
    <view class="send-hetong-v" wx:if="{{msgInfo.customEvent == '5'}}">
      <view>您已给对方发送合同</view>
      <view>
        请前往客户端
        <text class="go-see-hetong">会员中心-电子合同管理</text>
        查看
      </view>
    </view>
    <view class="tbt-v" wx:elif="{{msgInfo.payload.renderDom.length > 0}}">
      <block  wx:for="{{msgInfo.payload.renderDom}}"  wx:key="*this">
        <text wx:if="{{item.name == 'text'}}" class="txt-def">{{item.text}}</text>
        <text wx:elif="{{item.name == 'btn'}}" catch:tap="onRenderClick" data-item="{{msgInfo.payload}}" class="txt-def txt-f">{{item.text}}</text>
      </block>
    </view>
    <view class="tbt-v" wx:elif="{{msgInfo.payload.content.length > 0}}">
      <block  wx:for="{{msgInfo.payload.content}}"  wx:key="*this">
        <text wx:if="{{item.type == 'text'}}" class="txt-def">{{item.text}}</text>
        <text wx:elif="{{item.type == 'btn'}}" catch:tap="onClick" data-item="{{item}}" class="txt-def txt-f">{{item.text}}</text>
      </block>
    </view>
    <view wx:else class="{{msgInfo.isRevoked && msgInfo.isSelf && msgInfo.type == 'TIMTextElem' ? '' : 'system-txt'}}">
      <block wx:if="{{!!msgInfo.payload.name}}">
        "       
        <text class="name">{{msgInfo.payload.name}}</text>
        "
      </block>
      <text>{{msgInfo.payload.text}}</text><text class="reedit" catch:tap="onReEdit" wx:if="{{msgInfo.isRevoked && msgInfo.isSelf && msgInfo.type == 'TIMTextElem' }}">重新编辑</text>
    </view>
  </view>
</view>
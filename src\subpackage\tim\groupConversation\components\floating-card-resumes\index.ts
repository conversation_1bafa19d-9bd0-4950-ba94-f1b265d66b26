/*
 * @Date: 2022-07-11 11:33:20
 * @Description: 招工悬浮卡片
 */
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { guid } from '@/utils/tools/common/index'

Component(class extends wx.$.Component {
  properties = {
    conversation: {
      type: Object,
      value: {},
    },
  }

  data = {
    cardInfo: {} as any,
    // 是否展开 悬空内容
    isShow: false,
    // 标题
    title: '',
    // 描述
    desc: '',
  }

  observers = {
    conversation(v) {
      if (wx.$.u.isEmptyObject(v)) return
      const { infoDetail = {} } = v || {}
      const { cardInfo } = infoDetail || {}
      const sData: any = { cardInfo }
      const { occupationName, sex, age, nation, hopeCity } = cardInfo || {}
      if (wx.$.u.isArrayVal(occupationName)) {
        sData.title = `期望：${occupationName.join('、')}`
      }
      let desc = `${sex}`
      if (age) {
        desc += `${desc ? ' · ' : ''}${age}岁`
      }
      if (nation) {
        desc += `${desc ? ' · ' : ''}${nation}`
      }
      if (wx.$.u.isArrayVal(hopeCity)) {
        desc += `${desc ? ' · ' : ''}工作地：${hopeCity.join('、')}`
      }
      if (desc) {
        sData.desc = desc
      }
      this.setData(sData)
    },
  }

  onChange() {
    const { isShow } = this.data
    this.setData({ isShow: !isShow })
    this.triggerEvent('change')
  }

  async onDetailClick() {
    const { conversation } = this.data as DataTypes<typeof this>
    const { infoDetail, telRightsInfo, relatedJobId } = conversation || {} as any
    const { infoId: uuid, relatedInfoId } = infoDetail || {}
    const { telType, infoId, infoType } = telRightsInfo || {}
    const options = wx.$.r.getQuery()
    wx.$.collectEvent.event('universal_click', {
      bis: 'ypzp',
      s_t: 'IM',
      code: options.conversationId,
    })

    const buryingPoint = {
      info: {
        request_id: guid(),
        source_id: '15',
      },
    }
    wx.$.r.push({
      path: '/subpackage/resume/detail/index',
      query: { sceneV2: '21', jobId: relatedJobId, uuid: Number(relatedInfoId) ? relatedInfoId : uuid, nearbyWorkerListApiSource: 'Im', type: 'groupConversation', telType, infoId, infoType, s_t: 'IM', r_s_code: options.conversationId, myepc: getPageCode() },
      params: { buryingPoint: JSON.stringify(buryingPoint) },
    })
  }
})

/*
 * @Date: 2022-04-08 10:59:16
 * @Description: errcode状态码处理
 */

import { store, dispatch, actions } from '@/store/index'

import { loginWhite } from './utils'
import { toLogin } from '../helper/common/toLogin'

/** 请求接口返回结果data:{errorcode},若有新增的值，需要添加到当前文件 */
export const allErrorCode = [
  'goback',
  'reload',
  '7405',
  '200',
  'JOB_112_06',
  'login_over_time',
  'ok',
  '0',
  'member_shielding',
  'freeze',
  'toCodeLogin',
  'is_abnormal',
  'has_used_up',
  'update',
  'success',
  'auth_checking',
  'pass_complaint',
  'auth_pass',
  'fail',
  'auth_check',
  'auth_fail',
  'auth_no',
  'deleted',
  'paid_recruit',
  'top_resume',
  'top_job',
  'user_view_tel_limit',
  'auth_forbid',
  'to_auth',
  'member_forbid',
  'status_error',
  'unusable',
  'get_integral',
  'job_update_number_get_integral',
  'job_max_update_times',
  'to_company_auth',
  'end',
  'ajax',
  'auth_not_pass',
  'none_tel',
  'to_share',
  'forbid',
  'discard',
  'logged',
  'paid_issue',
  'integral_lack',
  'to_new_top_page',
  'have_not_zg_ing',
  'have_not_zg',
  'checking_top',
  'to_face_auth',
  'to_ceiling',
  'not_zg',
  'go_pay',
  'update_factory_reach_limit',
  'job_max_update_times',
  'integral_confirm',
  'confirm',
  'tips_fail',
  'already_register',
  'code_register',
  'no_register',
  'not_exist',
  'bind_tel',
  'exist',
  'no',
  'no_to_bind_tel',
  'liar_library',
  'user_not_exist',
  'block',
  'goback',
  'no_free_auth',
  'to_update_page',
  'reset_top',
  'top_time_over',
  'auth',
  'must_have_tel',
  '5100',
  'validation_failed',
  'belong_factory',
  'reach_pub_limit',
  'to_face_auth',
  'to_ceiling',
  'to_bind_tel',
  'not_unfreeze',
  'user_must_issue_resume',
  'user_resume_check_failed',
  'factory_times_is_out',
  'bare_fail',
  'get_button',
  'not_check',
  'no_job_info',
  'top_info_expired',
  'no_resume_info',
  'factory',
  'logistics',
  'to_buy_prime',
  'logistics_issue',
  'free_company_auth_exhausted',
  '40001',
  'occ_version_atypism',
  '10000',
]

/** 系统跳转升级页面开关 */
let systemUpgradeFlag = true

/** 自定义的code提示-状态值对应handleErrorCode方法中的判断 */
export const errorCodeCustomTip = [
  'system_pause',
  'login_over_time',
  'is_abnormal',
  'unusable',
  'member_forbid',
  'member_shielding',
  'to_face_auth',
  'auth_forbid',
  'to_auth',
  'to_company_auth',
]

// 拆分url中路径
export const getUrlPath = (url: string) => {
  let path = url.split('?')[0]
  let domain = ''
  if (path.indexOf('http') > -1) {
    // eslint-disable-next-line prefer-destructuring
    path = path.split('//')[1]
    // eslint-disable-next-line prefer-destructuring
    domain = path.split('/')[0]
  }
  return path.replace(domain, '')
}

/** 处理请求接口返回异常兼容处理 */
export const handleErrorCode: (res, url: string) => boolean = (res, url: string) => {
  if (res) {
    let errcode = res.data ? res.data.errcode : res.errcode
    if (!errcode) {
      errcode = res.data ? res.data.error_code : res.error_code
    }
    const msg = res.head?.msg || ''
    // 去系统升级中页面
    if (errcode === 'system_pause') {
      if (!systemUpgradeFlag) {
        return false
      }
      systemUpgradeFlag = false

      const sysData = res?.data?.system_pause_data
      if (sysData?.type == 'url') {
        wx.$.r.reLaunch({
          path: '/subpackage/update-prompt/index',
          query: {
            url: sysData?.url,
            arrow: sysData?.allow_return,
          },
        })
      } else if (sysData?.type == 'html') {
        wx.$.r.reLaunch({ path: '/subpackage/update-prompt/index' })
      }
      return false
    }
    // 拦截登录过期，model接口显示过期状态下跳转到登录页
    if (errcode === 'login_over_time') {
      wx.$.l.timLogout()
      const urlPath = getUrlPath(url)
      wx.hideLoading()
      dispatch(actions.storageActions.removeItem('userState'))
      dispatch(actions.storageActions.removeItem('myResumeDetails'))
      if (loginWhite.indexOf(urlPath) === -1) {
        toLogin(true)
      }
      return false
    }
    // 被限制用户不能操作
    if (errcode === 'is_abnormal') {
      wx.$.confirm({
        content: msg,
        confirmText: '联系客服',
      }).then(() => {
        wx.$.u.callCustomerService()
      })
      return false
    }
    // 限制、屏蔽、骗子库
    if (errcode === 'unusable') {
      const isLogin = store.getState().storage.userState.login
      wx.$.confirm({
        cancelText: '知道了',
        confirmText: isLogin ? '查看详情' : '联系客服',
        content: msg,
      }).then(() => {
        if (isLogin) {
          wx.$.r.push({ path: '/subpackage/systips/index' })
        } else {
          wx.$.u.callCustomerService()
        }
      })
      return false
    }
    // 账号异常
    if (errcode === 'member_forbid' || errcode === 'member_shielding' || errcode === 'to_face_auth') {
      wx.$.confirm({
        cancelText: '知道了',
        content: msg,
        confirmText: '联系客服',
      }).then(() => {
        wx.$.u.callCustomerService()
      })
      return false
    }
    // 需要去实名或企业实名
    if (errcode === 'auth_forbid' || errcode === 'to_auth' || errcode === 'to_company_auth') {
      wx.$.confirm({
        title: res?.data.popup?.title || '温馨提示',
        content: res?.data.popup?.content || msg,
        confirmText: res?.data.popup?.r_btn || '去认证',
        cancelText: res?.data.popup?.l_btn || '取消',
      }).then(() => {
        const path = errcode === 'to_company_auth' ? '/subpackage/member/firmAuth/index' : '/subpackage/member/realname/index'
        wx.$.r.push({ path })
      }).catch(() => {
        wx.$.r.back()
      })
      return false
    }
    if (errcode == 'occ_version_atypism') {
      wx.$.msg(res.head.msg || '网络异常，请稍后再试').then(async () => {
        const pages = getCurrentPages()
        const perpage = pages[pages.length - 1]
        perpage.onLoad()
      })
      return false
    }
    // 其余需要过滤的异常处理
    if (errcode && !allErrorCode.includes(String(errcode))) {
      wx.$.alert({
        content: res.data.errmsg || msg || '请求超时，请稍后重试或联系客服',
        confirmText: '确定',
      })
      return false
    }
  }
  return true
}

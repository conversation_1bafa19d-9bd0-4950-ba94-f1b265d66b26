import { completeShare, getShareInfo, getSharePathInfo } from '@/utils/helper/share/index'
import { SHARE_BTN_LOCATION, SHARE_CHANNEL } from '@/config/share'
import { store } from '@/store/index'

/**
 * @name 获取格式化后的优质工人数据
 * @param query 路径参数
 */
export async function getQualityWorkerFmtQueryParams(query: any, isLogin: Boolean) {
  const { q, miniQualityWorkerEnterId, provinceId, cityId, cityIds, classifyIds } = query || {}
  // 是否来自找活大列表 左上角 优质工人 入口 3(找活大列表 顶部入口) 2(消息页 顶部入口)
  if (['3', '2'].includes(String(miniQualityWorkerEnterId))) {
    return { provinceId: provinceId || '', cityId: cityId || '', classifyIds: classifyIds || '' }
  }
  let finalQuery = cityIds && classifyIds ? query : null
  // 是否来自 鱼泡快招(找建筑工人) 小程序 扫二维码加入
  if (q) {
    finalQuery = getQrCodeQueryParams(q)
  }
  return verifyQRCodeQueryInfo(finalQuery, isLogin)

  function getQrCodeQueryParams(url) {
    const decodeUrl = decodeURIComponent(url)
    const questionMarkIdx = decodeUrl.indexOf('?')
    if (questionMarkIdx === -1) {
      return null
    }
    const queryArr = decodeUrl.slice(questionMarkIdx + 1).split('&')
    if (queryArr.length <= 1) {
      return null
    }
    return queryArr.reduce((obj, cur) => {
      const [key, val] = cur.split('=')
      return !val ? obj : { ...obj, [key]: val }
    }, {})
  }

  async function verifyQRCodeQueryInfo(query, isLogin: Boolean) {
    try {
      if (!query) {
        return null
      }
      const { classifyIds, provinceIds, cityIds } = query || {}
      const classIds: [] = classifyIds?.split(',') || []
      const arrCityIds: [] = cityIds?.split(',') || []
      const goToHomePage = () => {
        wx.$.r.push({ path: isLogin ? '/pages/resume/index' : '/pages/index/index' })
        return null
      }
      if (!classIds.length || !arrCityIds.length) {
        return goToHomePage()
      }
      const arrClassIds = await wx.$.l.getClassifyByIds(classIds)
      if (arrClassIds.length != classIds.length) {
        return goToHomePage()
      }
      // 判断城市id是否符合逻辑
      const isVerifyFail = await arrCityIds.some(async cityId => !((await wx.$.l.getAreaById(cityId)).current))
      if (isVerifyFail) {
        return goToHomePage()
      }
      return { provinceId: provinceIds || '', cityId: cityIds || '', classifyIds: classifyIds || '' }
    } catch (error) {
      return null
    }
  }
}

// ================================= 底下为未处理部分
/**
 * @name 获取 angle 信息
 * @description 计算滑动角度 start 起点坐标 end 终点坐标
 */
export function getAngleInfo({ startX, startY }, { touchMoveX, touchMoveY }) {
  const x = touchMoveX - startX
  const y = touchMoveY - startY
  // 返回角度 Math.atan()返回数字的反正切值
  return (360 * Math.atan(y / x)) / (2 * Math.PI)
}

/** @name 优质工人卡片点击分享路径参数配置 */
export const shareAppMessageContent = (options) => {
  // 分享的位置是按钮
  const isClickBtnShare = options.from == 'button'
  const sharePage = isClickBtnShare ? SHARE_BTN_LOCATION.RES_FREIEND_CARD_PATH : SHARE_BTN_LOCATION.WXCGCAPMENU_PAGE
  const btnId = isClickBtnShare ? SHARE_BTN_LOCATION.RES_FREIEND_CARD : SHARE_BTN_LOCATION.WX_CG_CAP_MENU_PATH
  const path = isClickBtnShare ? SHARE_BTN_LOCATION.RES_FREIEND_CARD_PATH : ''

  const dataset = options?.target?.dataset || {}
  const uuid = dataset.uuid || ''
  const resumeSubUuid = dataset.subUuid || ''
  const payload = { btnId, uuid, key: 'resumeSubUuid', val: resumeSubUuid }
  // 获取分享信息
  const shareInfo = getSharePathInfo(payload, store.getState().storage.userState.userId, path, sharePage)
  // 对接后台请求
  completeShare(SHARE_CHANNEL.SHARE_WECHAT_FRIEND, shareInfo)
  // 返回找活详情的分享信息
  return getShareInfo({ path: shareInfo.path, title: options?.title, from: options.from })
}

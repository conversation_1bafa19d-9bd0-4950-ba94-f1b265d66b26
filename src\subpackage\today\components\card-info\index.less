// ---- 工人👷🏻‍♂️卡片
.worker-card {
  position: relative;
  width: 100%;
  height: 65vh;
  padding-bottom: 20rpx;
  border-radius: 16rpx;
  background: #fff url('https://staticscdn.zgzpsjz.com/miniprogram/images/wyl/yp_mini_speicialbg.png') no-repeat 0 / cover;
}

// -------- 卡片审核中
.audit-card {
  width: 654rpx;
  height: 65vh;
  background-color: #fff;
  border-radius: 16rpx;
  margin: 0 auto;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  color: rgba(0, 0, 0, 0.85);
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
  line-height: 40rpx;
}

.audit-img {
  width: 296rpx;
  height: 290rpx;
}

.audit-tip {
  width: 420rpx;
  margin-top: 44rpx;
}

// -------- 用户信息盒子(可以上下滑动)
.worker-info {
  width: 100%;
  height: 65vh;
  padding: 32rpx 32rpx 90rpx;
  overflow-y: scroll;
}

.base-info {
  display: flex;
  align-items: center;
}

.avatar-box {
  position: relative;
  display: inline-flex;
  margin-right: 20rpx;
  vertical-align: middle;
}

.avatar {
  display: inline-block;
  width: 96rpx;
  height: 96rpx;
  border-radius: 16rpx;
}

.vip-img {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 32rpx;
  height: 32rpx;
}

.base-right {
  flex: 1;
}

.user-info {
  position: relative;
  display: flex;
  align-items: center;
  height: 48rpx;
}

.username {
  color: rgba(0, 0, 0, 0.85);
  font-size: 32rpx;
  font-weight: bold;
  line-height: 48rpx;
  margin-right: 16rpx;
}

.ip-home {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);

  width: fit-content;
  height: 34rpx;
  color: rgba(0, 0, 0, 0.65);
  font-size: 24rpx;
  font-weight: 400;
  line-height: 34rpx;
}

.sex-box {
  display: flex;
  align-items: center;
  justify-content: space-between;

  height: 44rpx;
  color: rgba(0, 0, 0, 0.45);
  font-size: 28rpx;
  line-height: 44rpx;
  margin-top: 6rpx;
}

// 工作状态
.work-status {
  flex: 1;
  height: 40rpx;

  color: rgba(0, 0, 0, 0.85);
  font-size: 24rpx;
  text-align: right;
  line-height: 40rpx;
}

.enter-day-num {
  padding: 0 8rpx;
  color: #0092ff;
}

// ------ 用户标签
.user-tags {
  z-index: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  min-height: 80rpx;
  border-bottom: 2rpx dotted #eff1f6;
  padding-bottom: 24rpx;
  margin: 4rpx -12rpx 0;
  color: rgba(0, 0, 0, 0.65);
}

.user-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16rpx 12rpx 0;
}

.user-tag-icon {
  display: inline-flex;
  margin-right: 8rpx;
  width: 32rpx;
  height: 32rpx;
}

// ------ 求职期望
.expected-work {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 48rpx;
  margin-top: 24rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  line-height: 48rpx;
}

.distance {
  color: rgba(0, 0, 0, 0.65);
  font-size: 28rpx;
  line-height: 44rpx;
}

// 期望薪资
.expect-salary {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 44rpx;
  margin-top: 16rpx;

  .money {
    color: #0092ff;
    font-weight: bold;
  }
}

.expect-addr {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 44rpx;
  margin-top: 20rpx;
}

// 工种标签
.work-type-tags {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  margin: 4rpx -8rpx 0;

  .work-type-tag {
    margin: 16rpx 8rpx 0 8rpx;

    color: #000;
    font-size: 24rpx;
    line-height: 48rpx;
    opacity: 0.65;
  }
}

.introduce {
  color: rgba(0, 0, 0, 0.65);
  line-height: 44rpx;
  white-space: normal;
  word-break: break-all;
  margin-top: 20rpx;
}

.expect-media {
  padding: 24rpx 0;
}

// ------ 项目经验
.project-header {
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 24rpx;

  font-size: 28rpx;
  font-weight: bold;
  line-height: 44rpx;

  &::before {
    content: '';
    width: 8rpx;
    height: 8rpx;
    background-color: #0092ff;
    margin-right: 8rpx;
  }
}

// ------ 固定的收藏/投诉/分享
.footer {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 99;

  width: 100%;
  height: 90rpx;
  background-color: #fff;
  padding: 0rpx 73rpx;
  border-radius: 0 0 16rpx 16rpx;
  box-shadow: 0rpx -4rpx 8rpx 0rpx rgba(50, 52, 60, 0.05);

  display: flex;
  align-items: center;
  justify-content: space-between;

  font-size: 28rpx;
  line-height: 44rpx;
}

.footer-item {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

// 收藏按钮/投诉按钮
.collect-btn,
.complain-btn {
  display: flex;
  align-items: center;
  justify-content: center;

  .text {
    margin-left: 8rpx;
    color: #262626;
  }
}

// 分享按钮
.share-btn {
  display: flex !important;
  flex-direction: row !important;
  justify-content: center !important;
  align-items: center !important;

  width: 120rpx !important;
  background: transparent !important;
  border: 0 !important;
  border-radius: 0 !important;
  padding: 0 !important;
  margin: 0 !important;

  color: rgba(0, 0, 0, 0.85) !important;
  font-weight: 400 !important;

  .item-icon {
    color: #262626 !important;
  }

  .text {
    margin-left: 8rpx !important;
    color: #262626 !important;
    font-size: 28rpx !important;
    font-weight: 400 !important;
    line-height: 44rpx !important;
  }
}

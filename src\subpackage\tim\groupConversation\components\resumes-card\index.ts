/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 普通找活卡片
 */

import { storage, store } from '@/store/index'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { fetchResumeExist } from '@/utils/helper/resume/index'

Component({
  properties: {
    msgInfo: {
      type: Object,
      value: {},
    },
  },
  data: {
    classNames: [],
    info: {},
  },
  lifetimes: {
    ready() {
      const { msgInfo } = this.data
      const content = wx.$.u.getObjVal(msgInfo, 'payload.data.content') || {}
      const { tags } = content || {}
      this.setData({
        classNames: tags.split(','),
        info: content,
      })
    },
  },
  methods: {
    /** 判断是否是自己的找活名片 */
    onIsSelf(userId) {
      const userState = storage.getItemSync('userState')
      if (userState.login) {
        return userState.userId == userId
      }
      return false
    },
    async onClick() {
      await wx.$.u.waitAsync(this, this.onClick, [], 1000)
      const { info } = this.data
      const { resume_uuid, resume_user_id } = info || {}
      const isSelf = this.onIsSelf(resume_user_id)
      const options = wx.$.r.getQuery()
      if (!isSelf) {
        const { conversation } = store.getState().timmsg
        const { telRightsInfo, relatedJobId } = conversation || {}
        const { telType, infoId, infoType } = telRightsInfo || {}
        wx.$.collectEvent.event('universal_click', {
          bis: 'ypzp',
          s_t: 'IM',
          code: options.conversationId,
        })
        wx.$.r.push({
          path: '/subpackage/resume/detail/index',
          query: { sceneV2: '21', jobId: relatedJobId, uuid: resume_uuid, nearbyWorkerListApiSource: 'Im', type: 'groupConversation', telType, infoId, infoType, s_t: 'IM', r_s_code: options.conversationId, myepc: getPageCode() },
        })
      } else {
        const resData = await fetchResumeExist(true)
        if (resData && !resData.exist) { // 找活名片不存在
          wx.$.r.push({ path: '/subpackage/resume/resume_publish/index', query: { origin: 'complete' } })
          return
        }
        wx.$.r.push({ path: '/subpackage/resume/publish/index' })
      }
    },
  },
})

.container {
  width: 100%;
  padding: 24rpx 32rpx;
  background-color: #f5f6fa;

  .card {
    width: 100%;
    margin-bottom: 16rpx;
    padding: 24rpx;
    background-color: #fff;
    border-radius: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .cardHeader {
      display: flex;
      align-items: center;
      width: 100%;
      height: 84rpx;
      margin-bottom: 16rpx;

      .cardHeaderImg {
        flex-shrink: 0;
        width: 80rpx;
        height: 80rpx;
        margin-right: 16rpx;
        object-fit: cover;
        border-radius: 10rpx;
      }

      .cardHeaderContent {
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: space-between;

        .title {
          font-weight: 700;
          font-size: 34rpx;
        }

        .text {
          color: @text-third-color;
          font-weight: 400;

          font-size: 26rpx;
        }
      }
    }

    .cardContent {
      width: 100%;
      padding: 24rpx;
      background-color: #f5f6fa;
      border-radius: 16rpx;

      .block {
        display: flex;
        width: 100%;
        margin-bottom: 16rpx;
        padding: 14rpx 16rpx;
        background-color: #fff;
        border-radius: 8rpx;

        .text {
          font-size: 26rpx;
          line-height: 39rpx;
        }
      }

      .desc {
        font-weight: 400;
        font-size: 30rpx;
        line-height: 50rpx;
      }
    }
  }
}

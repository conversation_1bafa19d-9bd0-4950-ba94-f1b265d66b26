/*
 * @Date: 2022-04-14 21:02:12
 * @Description: 接口请求
 */

import { getUrl, codeMessage, codeMessageWhite } from './utils'
import { beforeRequestPoint, afterRequestPoint, responseErrPoint } from './buryingPoint'
import miniConfig from '@/miniConfig/index'
import { handleErrorCode } from './errcode'
import { getHeaders } from './headers'
import { dealDialogByApi } from '../helper/dialog/index'

let isNoRequest = false
export const request = async <T>({ type, url, head: customHead = {}, extra, data }): Promise<T> => {
  /** 新老接口选择器 */
  const URL = getUrl(url)
  /* 请求参数 */
  const requestData = { wechat_token: miniConfig.token, ...JSON.parse(JSON.stringify(data || {})) }
  /* 请求头 */
  const headers = await getHeaders(requestData, customHead)
  /** 首屏加载优化-defer(true：使用该属性的请求被视为对页面首屏渲染无影响的请求，会被暂存至延迟队列中等待页面首屏渲染完成后进行间隔发送；false或不携带此参数，均为正常优先级，即时发送) */
  const defer = ENV_IS_SWAN && extra && extra.defer ? { defer: extra.defer } : {}
  return new Promise((resolve, reject) => {
    /** 请求接口前的埋点（部分接口) */
    beforeRequestPoint({ url: URL, requestData, headers })
    wx.request({
      url: ENV_MODE === 'dev' && extra?.mock ? `http://rap2api.taobao.org/app/mock/273365${url}` : URL,
      // 新增自定义header
      header: extra?.headers ? { ...headers, ...extra.headers } : headers,
      data: requestData,
      method: type,
      timeout: extra?.timeout || +ENV_REQUEST_TIMEOUT,
      ...defer,
      success(response) {
        success(URL, response, resolve, reject, extra)
      },
      fail(err) {
        responseErrPoint({ url: URL, err, requestType: 'PHP' })
        // handleSystemMaintenance(500)
        reject(err)
      },
    })
  })
}

/** 接口请求成功 */
async function success(url, response, resolve, reject, extra) {
  const hideErrCodes = extra?.hideErrCodes || []

  const hideMsg = extra?.hideMsg || false
  if (!response?.statusCode) {
    reject(response)
    return
  }
  let { data } = response
  const statusCode = Number(response.statusCode)
  /** 请求接口之后的埋点（部分接口) */
  afterRequestPoint({ url, responseData: data })

  const dataKeys = Object.keys(data)
  if (dataKeys.indexOf('error') > -1 && dataKeys.indexOf('message') > -1) {
    // !*  java接口的响应数据处理
    const { data: requestData, ...otherData } = data
    data = {
      data: requestData,
      head: {
        ...otherData,
        code: data.error ? 0 : 200,
        msg: data.message,
      },
    }
  }

  // 返回状态码不为200情况
  if (statusCode != 200) {
    const errMsg: string = codeMessage[statusCode]
    // const isOk = await handleSystemMaintenance(statusCode)
    // if (!isOk) {
    !codeMessageWhite.includes(statusCode) && wx.$.msg(`请求错误：${url} - ${errMsg}`, 1000)
    // }
    sentryReport(url, statusCode, errMsg)
    reject(data)
    return
  }

  // 进入风控2.0处理
  if (data?.head?.code == 500 && data?.data?.errcode == 'risk_intercept') {
    const errMsg: string = codeMessage[statusCode]
    const result = await dealDialogByApi(data?.data?.result?.riskRules[0]?.dialogIdentify)
    if (result) {
      wx.$.showModal(result)
      wx.hideLoading()
      sentryReport(url, statusCode, errMsg)
      return
    }
    wx.hideLoading()
    wx.$.msg('网络不给力，请稍后再试～')
    sentryReport(url, statusCode, errMsg)
  }

  // 返回状态码为200情况
  // 后端返回的msg提示
  const { msg } = data?.head || {}

  let errcode = data.data ? data.data.errcode : data.errcode
  if (!errcode) {
    errcode = data.data ? data.data.error_code : data.error_code
  }
  if (errcode == 'occ_version_atypism' && !isNoRequest) {
    isNoRequest = true
    setTimeout(() => {
      isNoRequest = false
    }, 1000)
    wx.$.msg(msg || '网络异常，请稍后再试').then(async () => {
      const pages = getCurrentPages()
      const perpage = pages[pages.length - 1]
      perpage.onLoad(perpage.options)
    })
    reject(data)
    return
  }
  // 是否需要调用wx.$.msg函数
  let showMsg = true
  if (hideErrCodes === true) {
    showMsg = false
  } else if (!hideErrCodes?.includes(data?.data?.errcode || data?.errcode)) {
    // hideErrCodes，接口自己处理的状态码，不做handleErroCode处理
    showMsg = await handleErrorCode(data, url)
  } else if (hideErrCodes?.includes(data?.data?.errcode || data?.errcode)) {
    showMsg = false
  }
  if (data?.head?.code == 200) {
    resolve(data)
  } else if (data?.head?.code == 500 && !hideMsg && showMsg) {
    // const isOk = await handleSystemMaintenance(data.head.code)
    // if (isOk) {
    //   resolve(data)
    //   return
    // }
    const msgMask = extra?.msgMask || false
    if (ENV_MODE === 'dev' && console && console.time) {
      console.time('开始')
    }
    wx.$.msg(msg, 2500, msgMask).finally(() => {
      // code不为200统一reject
      reject(data)
      if (ENV_MODE === 'dev' && console && console.timeEnd) {
        console.timeEnd('开始')
      }
    })
    // 上报错误
    sentryReport(url, statusCode, msg)
  } else {
    // let isOk = await handleSystemMaintenance(data?.head?.code)
    // if (isOk) {
    //   resolve(data)
    //   return
    // }
    // isOk = await handleSystemMaintenance(data?.data?.errcode || data?.errcode)
    // if (isOk) {
    //   resolve(data)
    //   return
    // }
    // 接口未做自定义处理、并且handleErrorCode函数未处理过，进行提示操作
    if (Array.isArray(hideErrCodes) && !hideErrCodes?.includes(data?.data?.errcode || data?.errcode) && showMsg && !hideMsg) {
      const msgMask = extra?.msgMask || false
      wx.$.msg(msg, 2500, msgMask).finally(() => {
        // code不为200统一reject
        reject(data)
      })
    } else {
      // code不为200统一reject
      reject(data)
    }
    // 上报错误
    sentryReport(url, statusCode, msg)
  }
}

function sentryReport(url, statusCode, message) {
  console.log('sentryReport', url, statusCode, message)
}

/*
 * @Description: 消息中心，找工作，找活页面 tab-bar红点
 */
import { store } from '@/store/index'

// 全部、推荐、附近对应值转换
export const FilterNum = {
  newest: 1,
  nearby: 3,
  recommend: 2,
  personal: 4,
  company: 5,
}

/** @name 招工展示红点接口请求 */
export const recruitShowPointRequest = async (time?) => {
  // time如果存在，代表这个接口是time参数是外面传递的为准
  const parmas:any = getRecruitLocalRequestData()

  if (time) {
    parmas.lastTime = time
  }
  const { location } = parmas
  const { longitude, latitude } = location || {}
  if (!longitude || !latitude) {
    delete parmas.location
  }

  if (parmas.occV2.length) {
    // 过滤industry为空的数据
    parmas.occV2 = parmas.occV2.filter((item) => item.industry)
  }

  if (!parmas.occV2?.length) {
    delete parmas.occV2
  }
  // eslint-disable-next-line no-return-await
  return await wx.$.javafetch['POST/job/v2/list/job/count'](parmas, { hideMsg: true, isNoToken: true })
}

/** @name 获取招工请求参数 */
export const getRecruitLocalRequestData = () => {
  const data: any = {
    areaId: '1',
    location: { longitude: '', latitude: '' },
    useIpLocation: !!store.getState().storage.userLocationCity?.isFirst,
    occV2: [],
    listType: 2,
  }
  /** 本地城市 */
  const { userLocationCity, L_IS_GEO_AUTH } = store.getState().storage
  /** 招工排序 */
  const { recruitSort } = store.getState().index
  const { recruitCityObj, id, pid } = userLocationCity || {}
  const { id: recId, pid: recPid } = recruitCityObj || {}
  /** 城市id */
  data.areaId = recId || recPid || id || pid || '1'

  const { recruitLocaltion, userLocation } = store.getState().storage
  const { recruitOcc2Value } = store.getState().classify
  // 招工列表附近筛选参数
  if (recruitSort.value === 'nearby' && (recruitLocaltion || userLocation)) {
    const location = recruitLocaltion?.location || userLocation
    const [longitude, latitude] = location.split(',')
    data.location = { longitude, latitude }
    data.useIpLocation = false
  } else if (L_IS_GEO_AUTH == 1 && userLocation) {
    const location = userLocation
    const [longitude, latitude] = location.split(',')
    data.location = { longitude, latitude }
  }
  // 工种2.0
  data.occV2 = recruitOcc2Value
  /** 排序类型 */
  if (recruitSort && recruitSort.value) {
    data.listType = FilterNum[recruitSort.value]
  }
  data.areaId = Number(data.areaId)
  return data
}

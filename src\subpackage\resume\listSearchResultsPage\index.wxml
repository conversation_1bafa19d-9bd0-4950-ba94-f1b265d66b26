<view class="body {{isNoJobData ? 'body-bg' : ''}}">
  <view class="header" id="resume-result-header" style="padding-top:{{clientRect.top}}px;" catch:touchmove="onDisableMove">
    <view class="pd-lr"  style="height:{{clientRect.height}}px">
      <view class="header-search">
        <view bind:tap="onBack" class="back-icon">
          <icon-font type="yp-icon_back" style="display: inline-flex;"></icon-font>
        </view>
        <view class="search-cont"> 人才库</view>
      </view>
    </view>
      
    <block wx:if="{{login && !isNoJobData}}">
      <search-head 
        custom-box="custom-box" 
        isInput="{{false}}"
        keywords="{{keyword}}"
        bind:selectjob="onSelectJobShow"
        bind:joblist="onJobList"
        bind:click="onSearchClick"
        bind:search="onSearch"
      />
      <!-- 过滤框 -->
      <resume-filter fixed id="filter-list-remuse-search" bind:refresh="onRefresh" bind:filterscreenchange="onFilterScreenChange" sourcePageName="简历搜索结果页" point selectTit="我选择的工种" />
    </block>
  </view>
  <no-job-empty wx:if="{{!login || isNoJobData}}"/>
  <!-- 大列表 -->
  <view wx:else class="list">
    <!-- 联系客服 -->
    <view class="box">
      <!-- <join-group custom-class="custom-class" groupComponentClass="group-component-class" group-class="group-class" wx:if="{{login}}" pageName="简历列表" sourceFrom="加群卡片-简历页" sectionId="{{1}}" showPhone="{{false}}" params="{{joinGroupParams}}"/> -->
    </view>
    <view style="position: relative;">
      <skeleton wx:if="{{!firstLoaded}}" />
    </view>
    <!-- 列表展示 包含广告和内容 -->
    <empty-box-network visible="{{isNetWorkShow}}" bind:onReset="onRefresh" bind:netWorkChange="onNetWorkChange" />
    <page-list wx:if="{{!isNetWorkShow}}" bind:service="onService" paddingBottom="{{58}}" id="page-list" isCusEmpty list="{{list}}" loading="{{resumeLoading}}" finish="{{finish}}">
      <view slot="default" id="result-resume-list">
        <empty-box wx:if="{{firstLoaded && isSupply}}" img-class="tj-data-img" title-class="tj-title" text-class="tj-text" title="暂无匹配信息，更换筛选项试一试吧" imgType='search' text='- 为您推荐其他优秀牛人 -' />
        <view wx:for="{{list}}" wx:key="guid">
          <view class="resume-result-item" data-index="{{index}}" data-item="{{item}}">
            <list-item
              bind:localchange="onLocalChange"
              isActiveAb="{{isActiveAb}}"
              item="{{item}}"
              index="{{index}}"
              data-index="{{index}}"
              origin="searchResult"
              class="{{'child-component-' + item.id}}"
            />
          </view>
        </view>
      </view>
      <view class="empty" slot="empty">
        <empty-box imgUrl="https://cdn.yupaowang.com/yupao_common/e1104d11.png" img-class="empty-img" img-type="search" title="暂时没有合适的人选，试试搜索其他吧">
          <recommend-occ bind:click="onRecOccClick"/>
        </empty-box>
      </view>
    </page-list>
  </view>
</view>
<!-- 选择职位弹框 -->
<select-job-pop 
    visible="{{isSelectJobShow}}"   
    list="{{jobList}}"
    bind:close="onSelectJobClose"
    bind:click="onSelectJobClick"
/>

<!-- 回到顶部 -->
<back-top />

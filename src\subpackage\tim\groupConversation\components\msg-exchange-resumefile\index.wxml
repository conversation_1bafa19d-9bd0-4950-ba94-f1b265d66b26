
<block wx:if="{{!msgInfo.isSelf }}">
    <view wx:if="{{msgInfo.payload.data.content.subType == 1}}" class="msg-main {{msgInfo.isSelf?'':'main-start'}}">
        <msg-head-img wx:if="{{!msgInfo.isSelf && msgInfo.isAvatar}}" msgInfo="{{msgInfo}}" />
        <view class="card-v">
            <view class="card-top-bg"></view>
            <view class="card-head">
                <view >
                    <view class="card-icon-v">
                        <image class="card-img" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp-min-resumefile.png" />
                    </view>
                </view>
                <view class="card-txt">{{msgInfo.payload.data.content.text}}</view>
            </view>
            <view class="card-footer">
                <block wx:if="{{msgInfo.payload.data.content.status == 1}}">
                    <view class="btn refuse" bind:tap="onRefuse">拒绝</view>
                    <view class="btn agree" bind:tap="onAgree">同意</view>
                </block>
                <block wx:elif="{{msgInfo.payload.data.content.status == 3}}">
                    <view class="btn agree-cl">已同意</view>
                </block>
                <block wx:else>
                    <view class="btn refuse-cl">已拒绝</view>
                </block>
            </view>
            <view class="card-composite" wx:if="{{msgInfo.payload.data.content.isCompositeCard}}">
                <view class="composite-title">或者发送</view>
                <view class="composite-btn-v">
                    <view class="composite-btn" bind:tap="onCompositeTel">
                        <image wx:if="{{msgInfo.payload.data.content.telStatus == 1}}" class="composite-img" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_call_huan.png" />
                        <image wx:else class="composite-img" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_exchangetel_no.png" />
                        <view class="composite-txt {{msgInfo.payload.data.content.telStatus == 1 ? '' : 'composite-txt-no'}}">电话号</view>
                    </view>
                    <view class="composite-btn" bind:tap="onCompositeWechat">
                        <image wx:if="{{msgInfo.payload.data.content.wechatStatus == 1}}" class="composite-img" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_change_wechat.png" />
                        <image wx:else class="composite-img" src="https://cdn.yupaowang.com/yp_mini/images/xjj/exchange_wechat_no.png" />
                        <view class="composite-txt {{msgInfo.payload.data.content.wechatStatus == 1 ? '' : 'composite-txt-no'}}">微信号</view>
                    </view>
                </view>
            </view>
        </view>
        <msg-head-img wx:if="{{msgInfo.isSelf && msgInfo.isAvatar}}" msgInfo="{{msgInfo}}" />
    </view>
</block>
<view wx:if="{{msgInfo.isSelf && msgInfo.payload.data.content.subType == 1 && role == 2}}" class="msg-main main-center">
    <view class="file-title">附件简历请求已发送</view>
</view>

<view wx:if="{{msgInfo.payload.data.content.subType == 3 && role == 2}}" class="msg-main main-center">
    <view class="file-title">{{msgInfo.payload.data.content.text}}</view>
</view>

<block wx:if="{{msgInfo.payload.data.content.fileInfo.fileName || msgInfo.payload.data.content.title}}">
    <view wx:if="{{msgInfo.payload.data.content.subType == 3 || (msgInfo.isSelf && msgInfo.payload.data.content.subType == 1 && role == 2)}}" class="msg-main main-center">
        <view class="card-v">
            <view class="card-top-bg"></view>
            <view class="card-head">
                <view >
                    <view class="card-icon-v">
                        <image class="card-img" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp-min-resumefile.png" />
                    </view>
                </view>
                <view class="card-content">
                    <view class="card-title">{{msgInfo.payload.data.content.fileInfo.fileName || msgInfo.payload.data.content.title  || ""}}</view>
                </view>
            </view>
            <view class="card-footer">
                <view class="btn btn-full agree" bind:tap="onPreview">点击预览附件简历</view>
            </view>
        </view>
    </view>
</block>
<!-- ------ 渐变背景色 -->
<view class="bgColor" />
<!-- ------ 自定义头部 -->
<custom-header bind:back="onBack" title="{{isFromPublishSuccess?'发布成功':'职位置顶'}}" content-out="header" customBack />
<!-- ------ 发布成功展示部分 -->
<block wx:if="{{isFromPublishSuccess}}">
  <!-- 发布-提升文案 -->
  <view class="auditTip">审核通过后牛人将通过安全号联系您，请注意接听</view>
  <!-- 发布-鱼泡安全号 -->
  <view wx:if="{{virtualNumberList.length > 0}}" class="safeNumRow">
    <view class="safeNumTitle">这些开头的都是鱼泡安全号:</view>
    <view class="safeNumBox">
      <view wx:for="{{virtualNumberList}}" wx:key="index" class="safeNumItem">{{item}}</view>
    </view>
  </view>
</block>
<!-- ------ 优势介绍 -->
<view class="topRow">
  <view class="topTipBox">
    <view class="tip">置顶职位信息，最快8s招到人</view>
    <view class="topDetailBox" bind:tap="onshowTopRuleClick">
      <view class="topDetailText">查看详情</view>
      <icon-font type="yp-icon_mbx" size="40rpx" color="#fff" custom-style="display: flex;" />
    </view>
    <image class="topRecruidImg" src="https://cdn.yupaowang.com/yp_mini/images/zddq/top_recruit.png" />
  </view>
  <!-- 提升曝光度-免费推广-免费自动刷新 -->
  <TopAdvantage n1="{{isTopping?topTimeActive.exposureNum:activeTopMeal.extInfo.exposureNum}}" n2="{{isTopping?topTimeActive.promotionNum:activeTopMeal.extInfo.promotionNum}}" n3="{{isTopping?topTimeActive.refreshNum:activeTopMeal.extInfo.refreshNum}}" />
</view>
<!-- 没有推荐的时候展示 -->
<view wx:if="{{noJob}}" class="no-job">
  <view class="no-job-title">选择职位</view>
  <image src="https://cdn.yupaowang.com/yupao_mini/rectopmset_nojob.png" mode="scaleToFill" class="no-job-img" />
  <view class="no-job-text">当前暂无可置顶职位，请先发布职位</view>
  <view class="no-job-btn-box">
    <m-button custom-class="no-job-btn" bind:tap="onPublish">
      <text class="no-job-btn-text">立即发布</text>
    </m-button>
  </view>
</view>
<view wx:else>
  <!-- ------ 选择城市 -->
  <view class="topSelectCityBox">
    <view class="titleRow">
      <view class="topCityTitle">当前{{!isTopping?'选择':''}}置顶范围</view>
      <!-- 非发布 | 未置顶且正在招 (包括审核中及审核通过) | -->
      <view wx:if="{{!pageLoading&&!isFromPublishSuccess&&!isTopping&&recommendList.length>=1}}" bind:tap="onOpenOtherToTopInfo" class="selectOther">
        <view class="selectOtherJobText">选择其他职位</view>
        <view class="selectOtherJobIcon">
          <icon-font type="yp-icon_mbx" size="40rpx" color="#0092FF" custom-style="display: flex;" />
        </view>
      </view>
    </view>
    <view wx:if="{{!isTopping}}" class="selectCitys">
      <!-- 有城市选择数据时展示 -->
      <view wx:for="{{topCityListNew}}" wx:key="id" bind:tap="onClickCityItem" data-index="{{index}}" class="cityItem">
        <view class="selectTopCity">{{item.name}}</view>
        <view class="selectTopCityIcon">
          <icon-font type="yp-icon_pop_close" size="32rpx" color="#0092ff" custom-style="display: flex;position:relative;top:1rpx;" />
        </view>
      </view>
      <!-- 继续添加城市,城市数量少于5时展示 -->
      <view wx:if="{{!pageLoading&&topCityListNew.length<5}}" bind:tap="onAddMoreTopCity" class="addMoreBtn">
        <view class="addMoreBtnIcon">
          <icon-font type="yp-jia" size="28rpx" color="#fff" custom-style="display: flex;position:relative;top:-1rpx;" />
        </view>
        <view class="addMoreBtnText">添加更多</view>
      </view>
    </view>
    <view wx:else bind:tap="onClickToppingCityTip" class="topCitys">
      <text wx:for="{{topCityListNew}}">{{index!==0?'、':''}}{{item.name}}</text>
    </view>
  </view>
  <!-- ------ 修改置顶：选择置顶时间 -->
  <view wx:if="{{isTopping}}" class="updateformItemRow">
    <view class="key">{{jobTopInfo.planEndDateTimeStr}}到期</view>
  </view>
  <!-- 我要置顶或预约置顶：选择置顶时长卡片 -->
  <view wx:else class="selectTimePanel">
    <view class="selectTimeHeader">
      <view class="topTimeTitle">置顶时长</view>
      <view class="topTimeExpire">
        <icon-font type="yp-icon_shz" size="30rpx" color="#0092ff" custom-style="display: flex;margin-right:10rpx;" />
        <text class="topTimeExpireDesc">预计 {{activeTopMeal.planEndTime}} 到期</text>
      </view>
    </view>
    <view class="selectTimeMain">
      <view class="{{activeTopMeal.configId == item.configId ? 'selectTimeMeal':'commonTimeMeal'}}" wx:for="{{jobTopMealInfo.configList}}" wx:key="item.configId" data-meal="{{item}}" bind:tap="onTopMealChange">
        <text class="selectTopTime">{{item.days}}天</text>
        <view class="selectTopIntegral">
          <text wx:if="{{item.label}}" class="originIntegral">原价{{item.originPrice}}积分</text>
          <text class="discountIntegral">{{item.discountPrice}}</text>
          <text class="discountUnit">积分</text>
        </view>
        <view wx:if="{{jobTopMealInfo.showLabelOnSelect?activeTopMeal.configId == item.configId:item.label}}" class="discountTip">
          <icon-font type="yp-zan" size="24rpx" color="#FFEFDE" custom-style="display: flex;margin-right:8rpx;" />
          <text class="discountTipText">{{item.label}}</text>
        </view>
      </view>
    </view>
  </view>
  <!-- ------ 置顶规则 -->
  <view wx:if="{{isTopping}}">
    <view class="topRuleTitle">置顶规则</view>
    <view wx:for="{{topRules}}" wx:key="item" class="topRuleText">
      <text>{{item}}</text>
      <a wx:if="{{index === topRules.length-1}}" bind:tap="onCallPhone">************</a>
    </view>
  </view>
  <view wx:if="{{!isTopping}}">
    <!-- ------ 置顶按钮 -->
    <BBFixedFooter class-my="topOkBtnBox" isAutoCalcHeight>
      <m-button bind:tap="onTopping" disabled="{{pageLoading}}" width="100%" height="96rpx" style="font-size: 32rpx; border-radius: 12rpx;">
        <text>确定置顶</text>
      </m-button>
    </BBFixedFooter>
  </view>
</view>
<!-- 置顶推荐弹窗 -->
<OtherJobInfoPopup wx:if="{{showRecommend}}" bind:close="onCloseTopRecommendJobModal" bind:setTop="onUpdatePageJobTopInfo" recommendList="{{recommendList}}" isHandleChoose="{{isHandleChoose}}" />
<!-- 返回时的弹窗 -->
<back-tip show="{{showBackTip}}" topTimeActive="{{topTimeActive}}" query="{{query}}" isFromPublishSuccess="{{isFromPublishSuccess}}" jobTopInfo="{{jobTopInfo}}" topCityListOld="{{topCityListOld}}" />
<!-- 充值弹窗 -->
<recharge-popup show="{{showRechargePopup}}" bind:success="onRechargeSuccess" bind:close="onRechargeClose" fuzzyValue="{{rechargeNum}}">
  <view slot="header" class="recharge-tip">
    <text>积分不足，还需消耗</text>
    <text class="number">{{rechargeNum}}</text>
    <text>个积分即可置顶</text>
  </view>
</recharge-popup>
<TopRulePopup visible="{{showTopRulePopup}}" bind:close="onTopRulePopupClose" />

/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 会话详情工具栏
 */
import { actions, dispatch, RootState, store } from '@/store/index'
import * as midUtils from '../../midUtils'

Component(class extends wx.$.Component {
  useStore(state: RootState) {
    const { userChooseRole, userImAcc } = state.storage
    return {
      role: userChooseRole,
      userImAcc,
    }
  }

  properties = {
    toolbarList: {
      type: Array,
      value: [],
    },
    conversation: {
      type: Object,
      value: {},
    },
    query: {
      type: Object,
      value: {},
    },
    pageCode: {
      type: String,
      value: '',
    },
  }

  data = {
    isMap: false,
  }

  pageLifetimes = {
    async show() {
      const { isMap } = this.data
      if (isMap) {
        this.setData({ isMap: false })
        const { selectArea, addManageSelect } = store.getState().map
        let nSelectArea:any = {}
        // 直辖市ID
        const directs = ['2', '25', '27', '32']
        if (!wx.$.u.isEmptyObject(addManageSelect)) {
          const { provinceId, address, houseNumber, provinceName, cityName, countyName, location, locationName } = addManageSelect || {} as any
          nSelectArea = { name: locationName, location, address, houseNumber }
          if (directs.includes(`${provinceId}`)) {
            nSelectArea.cityName = provinceName
            nSelectArea.countyName = cityName
          } else {
            nSelectArea.cityName = cityName
            nSelectArea.countyName = countyName
          }
          dispatch(actions.mapActions.setAddManageSelect({}))
        } else if (!wx.$.u.isEmptyObject(selectArea)) {
          const { adcode } = selectArea || {} as any
          const areaRes = await wx.$.l.getAreaByAdcode(adcode)

          const { province, city, district } = areaRes || {} as any
          nSelectArea = { ...selectArea }
          if (directs.includes(`${(province || {}).id}`)) {
            nSelectArea.cityName = (province || {}).name || ''
            nSelectArea.countyName = (city || {}).name || ''
          } else {
            nSelectArea.cityName = (city || {}).name || ''
            nSelectArea.countyName = district.name || ''
          }
        }
        if (!wx.$.u.isEmptyObject(nSelectArea)) {
          this.triggerEvent('onToolChange', { type: 'location', selectArea: nSelectArea })
          dispatch(actions.mapActions.setSelectArea({}))
        }
      }
    },
  }

  async onToolClick(e) {
    await wx.$.u.waitAsync(this, this.onToolClick, [e], 1000)
    const isRec = midUtils.jugeIntConRemind.call(this, null, {
      type: 'msg',
      chatsend: () => {
        this.onToolHandle(e)
      },
    })
    if (!isRec) {
      return
    }
    this.onToolHandle(e)
  }

  contactBtnText() {
    this.triggerEvent('contactBtnText')
  }

  onToolHandle(e) {
    const { type } = e.currentTarget.dataset
    switch (type) {
      case 'photo': // 发送图片
        this.selectImage(type)
        break
      case 'change_position': // 更换职位
        this.triggerEvent('onToolChange', { type })
        break
      case 'location':
        this.selectMap()
        break
      default:
        break
    }
  }

  selectMap() {
    const { conversation, role } = this.data as DataTypes<typeof this>
    const { infoDetail } = conversation || {} as any
    const { relatedJobLocation, relatedJobLocationAddress, relatedJobLocationName } = infoDetail || {}
    this.setData({ isMap: true })
    dispatch(actions.mapActions.setSelectArea({}))
    const params:any = {
      disabledCities: '33,34,35',
      isGetLocation: true,
      origin: 'im',
      from: 'im',
    }
    if (role == 1) {
      params.showAddressIcon = true
      params.methodType = 'location'
      params.location = relatedJobLocation
      params.locationName = relatedJobLocationName
      params.locationAddress = relatedJobLocationAddress
      params.pageTitle = '选择工作地址'
    } else {
      params.pageTitle = '选择地址'
    }
    wx.$.openLocation(params)
  }

  selectImage(type) {
    wx.chooseImage({
      count: 6,
      sourceType: ['album'], // 从相册选择
      success: (res) => {
        this.triggerEvent('onToolChange', { value: res, type })
      },
    })
  }
})

/* eslint-disable max-len */

import { buriedPoint } from './burying-point'
import { getRandomAvatar, getTodayTopUserNumber } from './tool'
import { actions, store } from '@/store/index'

/**
 * @name 招工置顶->返回->挽留弹窗
 */
Component({
  properties: {
    // 显示控制器
    show: { type: Boolean, value: false },
    // 是否来自发布成功页面
    isFromPublishSuccess: { type: Boolean, value: false },
    // 当前置顶时间配置
    topTimeActive: { type: Object, value: {} },
    // 当前招工置顶信息
    jobTopInfo: { type: Object, value: {} },
    // 当前招工置顶城市信息
    topCityListOld: { type: Array, value: [] },
    // 路径参数
    query: { type: Object, value: {} },
  },
  data: {
    // 显示遮罩
    startOpacity: false,
    // 加载挽留置顶弹窗
    loadedComp: false,
    // 随机头像
    randomAvatarNumArr: getRandomAvatar(),
    // 随机置顶用户数量
    topJobUserNum: 0,
  },
  observers: {
    show(newVal) {
      if (newVal) {
        buriedPoint('topUserPopupExposure')
        const randomAvatarNumArr = getRandomAvatar()
        const topJobUserNum = getTodayTopUserNumber(true)
        this.setData({ loadedComp: true, randomAvatarNumArr, topJobUserNum })
      }
    },
  },
  methods: {
    // 监听取消事件
    handleCancel() {
      const { isFromPublishSuccess } = this.data
      if (isFromPublishSuccess) {
        buriedPoint('topUserPopupClick', { click: '放弃置顶' })
      }
      const isSourcePath = store.dispatch(actions.otherActions.goToSourcePath())
      if (!isSourcePath) {
        wx.$.r.back()
      }
    },
    // 监听确认事件
    handleConfirm() {
      buriedPoint('topUserPopupClick', { click: '立即置顶' })
      this.setData({ show: false })
      this.setData({ startOpacity: false })
    },
    unloadComp() {
      this.setData({ loadedComp: false })
    },
    setOpacityBg() {
      this.setData({ startOpacity: true })
    },
  },
})

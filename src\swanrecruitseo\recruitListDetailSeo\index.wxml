<view class="bady">
    <custom-header custom-style="background:#0092FF;color:white" title="{{detailData.title}}" />
    <search-bar />
    <city-search-bar cityList="{{cityList}}" />
    <view class="seoList">
        <block wx:for="{{detailData.jobs}}" wx:key="*this">
            <recruit-card item="{{item}}" />
        </block>
    </view>
    <view class="recommend">
        <view wx:if="{{detailData.localUrgent.length > 0}}">
            <view class="recommend-title">附近招工</view>
            <view class="recommend-content">
                <block wx:for="{{detailData.localUrgent}}" wx:key="*this">
                    <view class="recommend-item" bind:tap="clickItem" data-item="{{item}}">
                        {{item.title}}
                    </view>
                </block>
            </view>
        </view>
        <view wx:if="{{detailData.relatedWork.length > 0}}">
            <view class="recommend-title">热门招工</view>
            <view class="recommend-content">
                <block wx:for="{{detailData.relatedWork}}" wx:key="*this">
                    <view class="recommend-item" bind:tap="clickItem" data-item="{{item}}">
                        {{item.title}}
                    </view>
                </block>
            </view>
        </view>
        <view wx:if="{{detailData.nearby.length > 0}}">
            <view class="recommend-title">相关招工</view>
            <view class="recommend-content">
                <block wx:for="{{detailData.nearby}}" wx:key="*this">
                    <view class="recommend-item" bind:tap="clickItem" data-item="{{item}}">
                        {{item.title}}
                    </view>
                </block>
            </view>
        </view>
    </view>
    <view style="height:200rpx;width:2rpx"></view>
    <m-stripes />
    <footer bind:onFooterBtn="onFooterBtn" title="{{footerTitle}}" />
</view>
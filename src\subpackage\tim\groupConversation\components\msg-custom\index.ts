/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 普通招工详情，底部tabbar
 */
import { actions, dispatch } from '@/store/index'

const SevenDaysTime = 1000 * 60 * 60 * 24 * 7
let timer
Component({
  properties: {
    msgInfo: {
      type: Object,
      value: {},
    },
    isShowTk: {
      type: Boolean,
      value: false,
    },
  },
  data: {},
  lifetimes: {
    ready() {},
  },
  methods: {
    callPhone(e) {
      const { msgInfo } = this.data
      const nowTime = new Date().getTime()
      const diffTime = nowTime - msgInfo.time * 1000
      if (diffTime > SevenDaysTime) {
        wx.$.msg('暂不支持拨打电话')
        return
      }
      if (msgInfo.isSelf) {
        return
      }
      const { tel } = e.target.dataset
      // if (tel) {
      this.triggerEvent('onMsgBtnClick', { tel })
      // callPhone(tel)
      // }
    },
    onLongpressMsg(e) {
      const { id } = e.currentTarget
      dispatch(actions.timmsgActions.setState({ longpressid: id }))
      if (timer) {
        clearTimeout(timer)
      }
      const query = this.createSelectorQuery()
      // 选择id
      query.select(`#${id}`).boundingClientRect()
      query.exec((res) => {
        this.setData({ isShowTk: true, tkWith: res[0].width })
        timer = setTimeout(() => {
          this.setData({ isShowTk: false })
        }, 3000)
      })
    },
    onTkClick(e) {
      this.setData({ isShowTk: false })
      const { type } = e.detail
      switch (type) {
        case 'copy':
          this.copy()
          break
        default:
          break
      }
    },
    copy() {
      let txt = ''
      const { msgInfo } = this.data
      if (!!msgInfo.payload.renderDom && msgInfo.payload.renderDom.length > 0) {
        msgInfo.payload.renderDom.forEach((item) => {
          txt += item.text
        })
      } else {
        txt = msgInfo.payload.data
      }
      wx.setClipboardData({
        data: txt,
        success() {},
      })
    },
  },
})

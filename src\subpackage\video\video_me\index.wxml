<view class="video-me" style="padding-top:{{statusBarHeight}}px">
  <view class="video-wrap" style="height:{{videoHeight}}px;" catch:tap="onClickVideo">
    <video
      id="video"
      class="video"
      style="height:{{videoRealHeight}}px"
      object-fit="{{videoObjectFit}}"
      controls="{{false}}"
      enable-play-gesture="{{false}}"
      show-center-play-btn="{{false}}"
      src="{{resumeVideoInfo.attach.filePath}}"
      bind:loadedmetadata="onLoadVideoData"
      bind:timeupdate="onTimeUpdate"
      bind:ended="onEnded">
    </video>
    <view class="header" style="top:{{menuButtonTop - statusBarHeight}}px">
      <view class="header-cell">
        <view class="back-btn" bind:tap="onBack">
          <icon-font type="yp-icon_back" size="48rpx" color="#fff" />
        </view>
        <view wx:if="{{resumeVideoInfo.checkStatus === 1}}" class="is-check">视频已上传，请等待审核!</view>
      </view>
      <view class="tips" wx:if="{{resumeVideoInfo.checkStatus !== 1}}">
        <block wx:if="{{resumeVideoInfo.checkStatus === 2}}">
          视频已发布，你可以播放查看，不满意可以选择【重新录制】。重新上传后，已发布视频将被删除
        </block>
        <block wx:if="{{resumeVideoInfo.checkStatus === 3}}">
          <view>审核失败原因：</view>
          <view>{{resumeVideoInfo.checkErrMsg}}</view>
        </block>
      </view>
    </view>
    <view class="video-container"></view>
    <image class="play-btn" wx:if="{{!playState}}" src="https://staticscdn.zgzpsjz.com/miniprogram/images/wjj/yp-mini_video_act.png?x-oss-process=image/format,webp/quality,q_91" />
  </view>
  <view id="video-foot" class="video-foot">
    <view class="progress-wrap">
      <slider class="progress-slider" value="{{progress}}" step="{{1}}" bind:change="onChangeProgress" bind:changing="onChangeProgress">
      </slider>
      <view class="progress-bar">
        <view class="progress-item" style="width:{{progress}}%" />
      </view>
    </view>
    <view class="footer {{!resumeVideoInfo ? 'hidden-block' : ''}}">
      <view class="footer-left">
        <view class="video-timeline">{{videoTime}}s</view>
        <view wx:if="{{resumeVideoInfo.checkStatus === 1}}" class="footer-action" catch:tap="onClickVideo">
          <icon-font type="{{playState?'yp-icon_video_stop':'yp-icon_video_action'}}" size="48rpx" color="#fff"></icon-font>
        </view>
      </view>
      <view class="footer-right" wx:if="{{true || resumeVideoInfo.checkStatus !== 1}}">
        <m-button
          btnTxt="删除"
          type="gray"
          width="152rpx"
          bind:tap="onDelete"
          custom-class="custom-btn"
        />
        <m-button
          btnTxt="重新录制"
          width="152rpx"
          type="{{resumeVideoInfo.checkStatus === 2 ? 'gray':'primary'}}"
          bind:tap="onClickToShoot"
          custom-class="custom-btn"
        />
        <m-button
          wx:if="{{resumeVideoInfo.checkStatus === 2 && downloadState !== 1}}"
          btnTxt="下载视频"
          width="152rpx"
          bind:tap="onClickDownload"
          custom-class="custom-btn"
        />
        <view wx:if="{{resumeVideoInfo.checkStatus === 2 && downloadState === 1}}" class="download-progress">
          <view class="download-progress-bar" style="width:{{downloadProgress}}%;" />
          <view class="progress-text">下载中</view>
        </view>
      </view>
    </view>
    <view class="safe-area" />
  </view>
</view>
<modal-body visible="{{showDownloadModal}}" close-icon="{{false}}">
  <view class="download-modal-wrap">
    <icon-font type="{{downloadState === 2?'yp-icon_pay_y':'yp-icon_tost_1'}}" size="96rpx" color="#ffffff" style="margin-bottom:16rpx;" />
    <view>{{downloadState === 2?'视频下载成功':'视频下载失败'}}</view>
    <view>{{downloadState === 2?'请至您的手机相册查看':'请重新下载'}}</view>
  </view>
</modal-body>

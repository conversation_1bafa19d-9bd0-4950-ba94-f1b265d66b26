/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 普通招工详情，底部tabbar
 */

import { dispatch, actions } from '@/store/index'

let timer
Component({
  properties: {
    /** 招工详情，接口内容，及自定义状态 */
    msgInfo: {
      type: Object,
      value: {},
    },
    /** 当前音频播放的ID */
    audioId: {
      type: String,
      value: '',
    },
    ispPlay: {
      type: Boolean,
      value: false,
    },
    isShowTk: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    // 是否正在播放
    isPlay: false,
    isSunhuai: false,
    tkWith: 0,
  },
  observers: {
    audioId(audioId) {
      if (audioId != this.data.msgInfo.id) {
        this.setData({ isPlay: false })
      }
    },
    ispPlay(ispPlay) {
      if (this.data.isPlay) {
        this.setData({ isPlay: ispPlay })
      }
    },
  },
  lifetimes: {
    ready() {},
  },
  methods: {
    onTkClick() {
      this.setData({ isShowTk: false })
    },
    onClick() {
      const { isPlay, msgInfo } = this.data
      this.triggerEvent('onAudioId', { audioId: msgInfo.id })
      if (!isPlay) {
        this.audioPlay()
      } else {
        this.audioPause()
      }
    },
    audioPlay() {
      this.setData({ isPlay: true })
      this.triggerEvent('audioPlay', { remoteAudioUrl: this.data.msgInfo.payload.remoteAudioUrl })
    },
    audioPause() {
      this.setData({ isPlay: false })
      this.triggerEvent('audioPause')
    },
    onLongpressMsg(e) {
      const { id } = e.currentTarget
      dispatch(actions.timmsgActions.setState({ longpressid: id }))
      if (timer) {
        clearTimeout(timer)
      }
      const query = this.createSelectorQuery()
      // 选择id
      query.select(`#${id}`).boundingClientRect()
      query.exec((res) => {
        this.setData({ isShowTk: true, tkWith: res[0].width })
        timer = setTimeout(() => {
          this.setData({ isShowTk: false })
        }, 3000)
      })
    },
    onStatusClcik(e) {
      const { msgInfo } = this.data
      this.triggerEvent('statusclcik', { msgInfo, type: e.detail.type })
    },
  },
})

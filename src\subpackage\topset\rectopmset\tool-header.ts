/*
 * @Date: 2022-08-10 14:01:09
 * @Description: 工具库
 */

/** 跳转到工厂找活大列表 */
export function jumpFactoryResume(query, factoryBackConfig) {
  const { classify_id, defaultTopArea } = query || {}
  const path = '/pages/resume/index'
  wx.$.r.reLaunch({ path, query: { classify_id, area_id: defaultTopArea } })
}

/** 跳转到物流找活大列表 */
export function jumpLogisticsResume(query) {
  const { classify_id = '', defaultTopArea = '' } = query || {}
  wx.$.r.reLaunch({ path: '/pages/resume/index', params: { classify_id, area_id: defaultTopArea } })
}

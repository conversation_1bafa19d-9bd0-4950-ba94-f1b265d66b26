.rec-occ-wrap {
  margin-top: 64rpx;
  padding: 0 32rpx;
}

.rec-occ-top {
  display: flex;
  align-items: center;
  justify-content: center;
  // padding: 6rpx 0;
}

.rec-head-img {
  width: 702rpx;
  height: 48rpx;
}

.rec-head-left {
  width: 66rpx;
  height: 2rpx;
  border-width: 2rpx;
  border-style: solid;
  border-image-source: linear-gradient(
    270deg,
    rgba(0, 146, 255, 1) 0%,
    rgba(245, 246, 250, 1) 100%
  );
  border-image-slice: 1;
  border-image-outset: 1rpx;
  margin-right: 16rpx;
}

.rec-head-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.rec-head-title {
  color: rgba(0, 146, 255, 1);
  font-size: 26rpx;
  line-height: 36rpx;
}

.rec-head-right {
  width: 66rpx;
  height: 2rpx;
  border-width: 2rpx;
  border-style: solid;
  border-image-source: linear-gradient(
    90deg,
    rgba(0, 146, 255, 1) 0%,
    rgba(245, 246, 250, 1) 100%
  );
  border-image-slice: 1;
  border-image-outset: 1rpx;
  margin-left: 16rpx;
}

.rec-occ-body {
  margin-top: 32rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 24rpx;
}

.occ-item {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 16rpx 32rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 1);
  overflow: hidden;
  width: 331rpx;
}

.occ-item-text {
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  .ellip(2);
}

/*
 * @Date: 2022-02-09 11:00:45
 * @Description: 举报
 */
import { isVaildVal } from '@/utils/tools/validator/index'

Page({
  data: {
    maxlength: 300,
    options: [],
    /** 路由参数 */
    query: {
      /** 用户ID */
      to_uid: '',
      /** 群组ID */
      groupId: '',
    },
    prohibit: false,
    focus: false,
    inputDisabled: true,
    remarks: '', // 备注
    value: '', // 原因
  },
  onTap() {
    if (!this.data.value) {
      wx.$.msg('请先选择举报原因')
      return
    }
    this.setData({ inputDisabled: false }, () => {
      this.setData({ focus: true })
    })
  },
  onLoad(options) {
    this.setData({ query: options })
    this.getReportRadios()
  },
  /** 获取举报配置 */
  async getReportRadios() {
    try {
      const res = await wx.$.javafetch['POST/audit/v1/reportCenter/category']({ projectId: 1102 })
      const list = res.data.list[0].childNode.map(item => ({
        value: item.id,
        label: item.name,
      }))
      this.setData({ options: list })
    } catch (e) {
      console.error(e)
    }
  },
  // 点击举报原因
  onClick(e) {
    const { value } = e.currentTarget.dataset.item
    this.setData({ value, prohibit: value && this.data.remarks })
  },
  onBlur() {
    this.setData({ inputDisabled: true, focus: false })
  },
  // 输入框变化
  onTextChange({ detail }) {
    // 没有选择举报原因不允许输入
    if (!this.data.value) {
      this.setData({ remarks: '' })
      return
    }
    // 安卓机上粘贴会超出限制
    const remarks = detail.value.slice(0, this.data.maxlength)
    this.setData({ remarks, prohibit: this.data.value && remarks })
  },
  // 点击提交
  onSubmit() {
    const { query, value, remarks, prohibit } = this.data
    const { to_uid, conversationId } = query || {}
    /* eslint-disable curly */
    if (!prohibit || !to_uid || !conversationId) return
    if (!isVaildVal(remarks, 15, 300)) {
      wx.$.msg('字数必须在15~300之间，且必须含有汉字')
      return
    }
    const parmas = {
      expendId: conversationId,
      source: 2,
      infoId: to_uid,
      projectId: 1102,
      categoryId: value,
      content: remarks,
    }
    wx.$.javafetch['POST/audit/v1/complaint/add']({ ...parmas }).then(async (res) => {
      if (res.code == 0) {
        await wx.$.alert({ title: '提交成功', content: '感谢您的反馈，平台将于1-3个工作日内审核处理', confirmText: '我知道了' })
        wx.$.r.back()
      }
    })
  },
})

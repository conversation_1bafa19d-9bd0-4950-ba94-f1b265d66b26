/*
 * @Date: 2021-12-25 09:25:32
 * @Description: 小程序启动文件，通过 npm start 启动
 */
const { spawn } = require('cross-spawn')
const fse = require('fs-extra')
const path = require('path')
const { printSystemInfo, checkList, closeReachRequest, reportVersion } = require('./utils')
const miniConfig = require('./miniConfig')
const { editionCachePath, webpackCachePath } = require(path.resolve(process.cwd(), './start/cachePath.js'))

/** 打印系统信息 */
printSystemInfo()

/** 运行构建命令 */
checkList(miniConfig).then(async (option) => {
  const { mode, miniType, audit, sub } = option
  process.env.RUN_INFO = JSON.stringify(option)
  process.env.MINI_CONFIG = JSON.stringify(miniConfig)

  // 当前选择的环境与缓存中的环境做对比(用于二次编译加速)
  try {
    if (audit) {
      const res = await closeReachRequest([miniConfig[miniType][sub].appid])
      console.log(`触达关闭成功${JSON.stringify(res)}`)
    }
    // 抖音百度打包时上报版本号
    if (mode === 'build' && miniType !== 'weapp') {
      reportVersion(miniType)
    }
    const current = option
    // 调试环境
    if (current.mode === 'dev') {
      if (fse.existsSync(editionCachePath)) {
        let cache = fse.readFileSync(editionCachePath) || {}
        cache = JSON.parse(cache)
        // 更新缓存文件
        if (cache.mode !== current.mode || cache.development !== current.development || cache.miniType !== current.miniType || cache.sub !== current.sub) {
          fse.writeFileSync(editionCachePath, JSON.stringify(current || {}))
        }
      } else {
        // 创建路径
        fse.ensureDirSync(webpackCachePath)
        fse.writeFileSync(editionCachePath, JSON.stringify(current || {}))
      }
    }
    spawn('node', ['./packages/mini-build-script/bin/mini-build-script', `${mode}:${miniType}`], {
      stdio: 'inherit',
      cwd: process.cwd(),
    })
  } catch (e) {
    console.log(e)
  }
})

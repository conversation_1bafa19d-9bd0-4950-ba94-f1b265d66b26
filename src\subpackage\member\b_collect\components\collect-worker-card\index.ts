import { communicate } from '@/utils/helper/member/index'

/*
 * @Author: wyl <EMAIL>
 * @Date: 2024-10-10 17:19:56
 * @LastEditors: 肖嘉俊 xia<PERSON><PERSON><PERSON><EMAIL>
 * @LastEditTime: 2025-05-26 10:23:23
 * @FilePath: \yp-mini\src\subpackage\member\c_collect\components\collect-boss-card\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
Component({
  properties: {
    item: {
      type: Object,
      value: {},
    },
    isNeedMarginBottom: {
      type: Boolean,
      value: true,
    },
  },
  data: {
    salaryText: '',
  },
  observers: {
    item(val) {
      if (val) {
        const sData: any = {}
        let salaryText = ''
        if (val.occupationStr) {
          salaryText = val.occupationStr
        }
        if (val.forwardSalary) {
          salaryText += val.occupationStr ? ` ・ ${val.forwardSalary}` : val.forwardSalary
        }
        sData.salaryText = salaryText
        this.setData(sData)
      }
    },
  },
  methods: {

    // 取消收藏
    async onCancelCollectBoss() {
      await wx.$.u.waitAsync(this, this.onCancelCollectBoss, [], 500)
      const { item } = this.data

      const res = await communicate.asyncCancelAttention(
        {
          collectInfoId: item.resumeSubUuid,
          collectType: 2, // 「1=招工, 2=找活, 3=老板」
        },
        {
          loadingText: '取消收藏',
          successMsg: '已取消收藏',
        },
      )
      if (res) {
        this.triggerEvent('cancelCollectWorker', { item })
      }
    },
  },
})

/*
 * @Date: 2022-02-09 11:00:45
 * @Description:
 *
 */

import { actions, storage, store } from '@/store/index'
import { tryPromise } from '@/utils/tools/common/index'
import { isIos } from '@/utils/tools/validator/index'
import { chatOutModal } from '@/components/widget/modal/core/index'
import { dayjs } from '@/lib/dayjs/index'
import { dealDialogRepByApi, uploadDialog } from '@/utils/helper/dialog/index'
import { handleErrorBeforeCallV3, newResumeMidPops, operationMidCallV3, resumeTelV3 } from './callv3'
import { collectService } from '../collect'
import { newRecruitMidPops, recruitTelChat } from '../../recruit/midphone'

export * as contactCard from './contactCard'

/**
 * 复制真实号码弹窗或者评价与复制真实号码合并弹窗（不需要评价合并弹窗页面后两个字段不用传）
 * @param is_special 是否特殊页面（是否需要搜藏按钮）
 * @param infoType 是招工还是找活 （job or resume)
 * @param id 招工或者找活id
 * @param ext  {from: 来源 1.找活详情页面 2.招工列表页 3.招工搜索结果页 4.消耗积分页面}
 */

// eslint-disable-next-line consistent-return
export const showMidPopup = (is_special: number, infoType?: string, uuid?: any, item?: any, ext?: any, buriedPointData?: any) => {
  try {
    return new Promise(async (resolve) => {
      setTimeout(() => {
        storage.setItemSync('isShowPopup', '')
      }, 200)
      const { from } = ext || {}
      // 获取复制真实号码弹窗数据
      const { data } = await wx.$.javafetch['POST/reach/v1/privacyTel/popWindow/getCallLogPopupNotice']({ is_special: !!is_special })
      const { callType, caller, called, tel, infoId, isCollect } = data || {}
      let show_popup = false
      let content = ''
      if (callType == 1 && caller) {
        show_popup = caller.show
        content = caller.content
      } else if (callType == 2 && called) {
        show_popup = called.show
        content = called.content
      }
      let showPopupData: any = {}
      if (infoType && uuid && !item) {
        const params = { cooperationTargetType: getCommentSourceType(infoType), cooperationTargetId: uuid }
        const { data: spData } = await wx.$.javafetch['POST/comment/v1/cooperationPop/info'](params)// 是否可以弹评价弹窗
        showPopupData = spData || {}
      }
      const showCooperation = storage.getItemSync('showCooperation')
      const showRealTel = storage.getItemSync('showRealTel')
      // eslint-disable-next-line no-nested-ternary
      // const cancelText = data.is_collect || is_special ? '关闭' : data?.info_type == 1 ? '收藏老板信息' : '收藏工友信息'
      // 如果是招工（开启评价时是复制号码评价合并弹窗用jobPopv2，未开启评价时是复制号码单独单独弹窗用jobPopv1）
      if (show_popup && infoType == 'job') {
        const { jobPopv2, jobPopv1 } = (data || {}) as any
        show_popup = showPopupData.labelConfigList && showPopupData.labelConfigList.length && !showCooperation[uuid] ? jobPopv2 : jobPopv1
      }
      if (!show_popup || !tel) {
        resolve({ type: 4 })
        return
      }
      if (showPopupData.labelConfigList && showPopupData.labelConfigList.length && !showCooperation[uuid] && from != 4) {
        if (!showRealTel) {
          resolve({ type: 4 })
          return
        }
        storage.setItemSync('showRealTel', '')
        storage.setItemSync('showRealTelState', null)
        // 弹出复制号码与评价合并弹窗
        resolve({ type: '3', tel })
        return
      }

      if (infoType == 'resume' && from == 1) {
        resolve({ type: 4 })
        return
      }
      // 招工列表和招工搜索结果页不需要弹出复制真实号码弹框
      // if (infoType == 'job' && ext && (ext.from == 2 || ext.from == 3)) {
      //   return
      // }
      if (!showRealTel) {
        return
      }
      storage.setItemSync('showRealTel', '')
      storage.setItemSync('showRealTelState', null)
      let cancelText = '关闭'
      if (infoType == 'job') {
        cancelText = isCollect || is_special ? '关闭' : '收藏'
      }
      wx.$.confirm({ content, cancelText, confirmText: '复制真实号码', cancelIcon: true }).then(() => {
        const reg = /^[0-9]*$/
        const success = () => {
          wx.hideToast()
          wx.$.msg('复制成功')
        }
        if (reg.test(tel)) {
          wx.setClipboardData({ data: tel, success })
          return
        }

        if (infoType != 'job') {
          resumeTelV3({ uuid, isPrivacy: 0, isPopup: 0, copyRealTel: 1 }, {}, buriedPointData).then(res => {
            if (res.data && res.data.tel) {
              wx.setClipboardData({ data: res.data.tel, success })
            }
            resolve('')
          })
          return
        }

        // 招工详情 我联系的人 联系老板
        const params:any = { jobId: uuid, scene: 13, isPrivacy: false, lookType: 1 }
        recruitTelChat(params, {}, {
          getRealPhone: (tel) => {
            if (tel) {
              wx.setClipboardData({ data: tel, success })
            }
          },
        })
      })
        .catch((res) => {
          if (!res.cancelIcon && infoId && cancelText !== '关闭' && infoType == 'job') {
            collectService({ collectType: 1, collectInfoId: infoId }, true)
              .then(() => {
                wx.$.msg('收藏成功！可在（会员中心-我的收藏）查看')
                resolve({ type: '2', tel })
              })
          }
        })
    })
  } catch (err) {
    console.error(err)
  }
}

/** source与commentSourceType对应处理 */
const getCommentSourceType = (source) => {
  const sourceTypes = {
    job: 1,
    resume: 2,
    contact_me: 3,
    my_contact: 4,
    push: 5,
    message: 6,
    resume_factory: 7,
    my_contact_factory: 8,
    toBeEvaluation: 9,
    myEvaluation: 10,
  }
  return sourceTypes[source] || 1
}

type IResumeTelChatData = {
  /** 子名片ID */
  uuid: string,
  /** 查看类型；1-查看电话；2-在线聊天  默认 1 */
  lookType?: number,
  /** 是否返回中间号；0-真实号码；1-中间号  默认 1 */
  isPrivacy?: number,
  /** 是否获取拨打弹窗；0-不获取弹窗；1-获取弹窗   默认 1 */
  isPopup?: number,
  /** 电话聊天前置弹窗 /resume/v1/resumeTelChat/getTelChatPrePopup请求的额外参数-5.0.0版本差异化定价新增 */
  popParams?: {
    /** 工种列表, 透传前端检索工种工种 */
    occupations?: number[],
    /** 城市列表, 透传前端城市列表 */
    cities?: number[],
  }
  /** 弹窗标识 */
  popupIdentification?: string
  /** 是否直接处理异常返回结果 默认 true */
  isHandleRes?: boolean
  /** 算法id */
  algorithmId?: string
}

type IHandleErrorData = {
  success?: Function,
  fail?: Function
  // 重新请求
  requestAgain?: Function,
}

type IOperExtParamsData = {
  query?: {
    /** 来源(从哪个页面进入拨打电话) */
    type?: string
  }
  /** 跳转到发布招工的路由参数 */
  fastQuery?: {
    /** 工种id: '301,302' */
    occV2: string
  } // 发布招工页面的路由参数
  /** 是否已拨打电话,用于判断是否需要请求拨打电话前置弹框接口 */
  hasShowPhone?: boolean
  /** 当前页面名称 */
  pageName?: string
  /** 来源；1-名片详情；2-联系记录 */
  from?: 1 | 2
  /** 查看类型；1-查看电话；2-在线聊天  默认 1 */
  lookType?: number,
  /** 判断拨打的是真实号码还是中间号 0-真实号码；1-中间号 */
  isPrivacy?: number
}
/**
 * 拨打手机号
 * @param param 子名片ID
 * @param params[popParams] 电话聊天前置弹窗请求的额外参数[可选] 5.0.0版本差异化定价新增
 * @param lookType  查看类型；1-查看电话；2-在线聊天；默认 1
 * @param isPrivacy 是否返回中间号；0-真实号码；1-中间号 默认 1
 * @param isPopup 是否获取拨打弹窗；0-不获取弹窗；1-获取弹窗 默认 1
 * @param popupIdentification 弹窗标识
 * @param isHandleRes 是否直接处理异常返回结果 默认 true
 * @param buriedPointData 列表接口透传数据
*/
export const resumeTelChat = (params: IResumeTelChatData, extparams: IOperExtParamsData = {}, buriedPointData: any = {}): any => {
  const { uuid, lookType = 1, isPrivacy = 1, isPopup = 1, popupIdentification = '', isHandleRes = true } = params
  const { popParams: _popParams = {} } = params
  const nParams: IResumeTelChatData = { uuid: `${uuid}`, lookType, isPrivacy, isPopup }
  if (popupIdentification) {
    nParams.popupIdentification = popupIdentification
  }
  return new Promise(async (resolve, reject) => {
    let res: any = {}
    const scene = getScene()
    if ((!extparams.hasShowPhone && isPopup == 1) || extparams.from) {
      const popParams: any = { uuid: `${uuid}`, from: 1, scene, lookType, ...(_popParams || {}) }
      // 本地有telPrePopup标识，就不再弹这个弹窗
      const telPrePopup = storage.getItemSync('telPrePopup')
      if (telPrePopup) {
        Object.assign(popParams, { popupList: popParams.popupList ? popParams.popupList.concat(telPrePopup) : [telPrePopup] })
      }
      if (extparams.from) {
        popParams.from = extparams.from
      }
      res = await tryPromise(wx.$.javafetch['POST/resume/v1/resumeTelChat/getTelChatPrePopup'](popParams, { isNoToken: true, hideMsg: true }), {})
    }
    const { dialogData } = res.data || {}
    const { dialogIdentify = '', dialogIdentifyDefault = '' } = dialogData || {}
    const code = ['0', '200', '10000', '10001']
    const { backend_id } = buriedPointData || {}
    if (backend_id) {
      nParams.algorithmId = backend_id
    }
    if (!(code.includes(`${res.code}`) && (dialogIdentify || dialogIdentifyDefault))) {
      res = await wx.$.javafetch['POST/resume/v1/resumeTelChat/getTelChat']({ ...nParams, ..._popParams })
    }
    if (!code.includes(`${res.code}`)) {
      handleErrorBeforeCall(res, { ...extparams, lookType }, {
        fail: () => {
          reject('')
        },
        requestAgain: (res) => {
          // isDelFrom 删除参数from,一般用于二次消费弹框点击联系拨打时触发
          const { isDelFrom, hasShowPhone = true } = res || {}
          const nExtparams = { ...extparams }
          if (isDelFrom) {
            delete nExtparams.from
          }
          resumeTelChat({ ...params, ...res.param }, { ...nExtparams, hasShowPhone }, buriedPointData).then((cRes) => {
            resolve(cRes)
          }).catch(() => {
            resolve({})
          })
        },
      })

      return
    }

    if (isHandleRes) {
      handleErrorBeforeCall(res, { ...extparams, lookType }, {
        success: () => {
          resolve(res)
        },
        fail: () => {
          resolve({})
        },
        requestAgain: (res) => {
          // isDelFrom 删除参数from,一般用于二次消费弹框点击联系拨打时触发
          const { isDelFrom, hasShowPhone = true } = res || {}
          const nExtparams = { ...extparams }
          if (isDelFrom) {
            delete nExtparams.from
          }
          resumeTelChat({ ...params, ...res.param }, { ...nExtparams, hasShowPhone }, buriedPointData).then((cRes) => {
            resolve(cRes)
          }).catch(() => {
            resolve({})
          })
        },
      })
      return
    }
    resolve(res)
  })
}

const getScene = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  switch (currentPage.route) {
    case 'subpackage/resume/detail/index':
      return 1
    case 'subpackage/member/myContactHistory/index':
      return 2
    case 'subpackage/member/integral/list/index':
      return 3
    case 'subpackage/member/evaluation/index':
    case 'subpackage/member/invite-evalute/index':
      return 5
    case 'pages/resume/index':
      return 6
    default:
      return 0
  }
}

/** 用于处理获取完整手机号时的所有异常情况
 * @extparams.fastQuery 跳转到发布招工页面的路由参数
 */
export async function handleErrorBeforeCall(res, extparams: IOperExtParamsData = {}, fn: IHandleErrorData = {}) {
  if (!res) {
    wx.$.msg('拨打失败，请稍后重试')
    return
  }

  const { dialogData, template } = res.data || {}
  let dialogIdentify = dialogData && dialogData.dialogIdentify
  let contentRep = dialogData && dialogData.template
  if (!dialogIdentify && res.data) {
    dialogIdentify = res.data.dialogIdentify
    contentRep = res.data.template
  }
  let popup
  if (dialogIdentify) {
    popup = await dealDialogRepByApi(dialogIdentify, contentRep, '', {}, { isNoLimit: true })
  }
  switch (String(dialogIdentify)) {
    // 列表外置拨打电话弹窗
    case 'phone_out1':
      wx.hideLoading()
      chatOutModal({
        identify: dialogIdentify,
        type: 'tel',
        surplusTimes: dialogData.template.surplusTimes,
        onCheck: (e) => {
          // 一天后才弹
          storage.setItemSync('telPrePopup', e.checked ? dialogIdentify : '', { outTime: dayjs().add(1, 'day').valueOf() })
        },
        success: () => {
          const re = {
            hasShowPhone: false,
            param: {
              popParams: {
                popupList: ['phone_out1'],
              },
              isPopup: 0,
            },
          }
          fn.requestAgain && fn.requestAgain(re)
        },
      })
      break
    case 'jifenbuzutishi':
      fn.fail && fn.fail()
      if (popup) {
        wx.$.showModal({
          ...popup,
          success: (suc) => {
            if (suc.jumpEventType != 3) {
              // eslint-disable-next-line no-lonely-if
              if (isIos()) {
                wx.$.r.push({ path: '/subpackage/member/getintegral/index' })
              } else {
                // 积分充值页
                wx.$.r.push({
                  path: '/subpackage/recharge/recharge/index',
                  query: {
                    isFromPage: extparams.lookType == 2 ? 'CLResumeChat' : 'CLResumePhone',
                  },
                })
              }
            }
          },
        })
      }
      break
    case 'ZZ-TSYLX':
      if (popup) {
        wx.hideLoading()
        wx.$.showModal({
          ...popup,
          success: ({ routePath }) => {
            if (routePath == 're-contactOtherSide') {
              const re = {
                hasShowPhone: false,
                param: {
                  popParams: {
                    popupList: ['ZZ-TSYLX'],
                  },
                  isPopup: 0,
                },
              }
              fn.requestAgain && fn.requestAgain(re)
            } else {
              fn.fail()
            }
          },
        })
      }
      break
    case 'goutongmianfeitishi':
    case 'goutongfufeitishi':
    case 'goutongfufeitishi2': // 2023-05-15 新增
    case 'bodatixing':
    case 'fuzhihaomaPAY':
    case 'mpgoutongfukoudingjia': // 因您上次联系已退费, 主要用在拨打记录也的我联系的人
    case 'resumeReconfirmPopV1': // 因您上次联系已退费,重新查看扣费弹窗
    case 'resumeReconfirmPopV2': // 因您上次联系已退费,复制号码重新扣费弹窗
    case 'mpgoutongfufeitishidingjia': // 差异化定价弹框-联系对方需消耗{integral}积分
    case 'chakanyouhuibiaoshi':
    case 'ckjlyckjlqy': // 有电话直拨权益，未退费
    case 'ckjlytfyckjlqy': // 有电话直拨权益，上次已退费
    case 'ckjljfczydgmbdhy': // 无会员、无电话直拨权益，未退费，积分充足，引导开通会员
    case 'ckjljfczydgmck': // 有会员，无电话直拨权益，未退费，积分充足，引导开通次卡
    case 'ckjlytfjfczydgmck': // 有会员，无电话直拨权益，上次已退费，积分充足，引导开通次卡
    case 'ckjlytjfczydgmbdhy':// 无会员，无电话直拨权益，上次已退费，积分充足，引导开通会员
      if (popup) {
        wx.hideLoading()
        wx.$.showModal({
          ...popup,
          success: (resSm) => {
            const routePath = resSm.routePath || ''
            if (resSm.jumpEventType == 4) {
              const re: any = {}
              if (dialogIdentify == 'bodatixing'
                || dialogIdentify == 'resumeReconfirmPopV1' || dialogIdentify == 'resumeReconfirmPopV2' || routePath == 'contactOtherSide') {
                re.isDelFrom = true
              }
              if (dialogIdentify == 'fuzhihaomaPAY') {
                re.param = { isPopup: 0 }
              }
              fn.requestAgain && fn.requestAgain(re)
            }
          },
        })
      } else {
        fn.fail && fn.fail()
      }
      break
    default:
      if (popup) {
        wx.hideLoading()
        // zhanghaoyichang:屏蔽   lainxigongrenshimingtishi:企业认证
        wx.$.showModal({
          ...popup,
          success: async (result) => {
            const routePath = result.routePath || ''
            switch (routePath) {
              case 'contactOtherSide': {
                const re: any = { isDelFrom: true }
                fn.requestAgain && fn.requestAgain(re)
                break
              }
              case 'confirm': {
                fn.fail && fn.fail()
                wx.$.r.push({
                  path: '/subpackage/recruit/fast_issue/index/index',
                  query: extparams.fastQuery || {},
                })
                break
              }
              case 'toEarnPoints': { // publishIntegralLack 积分不足弹窗 获取积分按钮
                fn.fail && fn.fail()
                wx.$.toGetIntegral({
                  isFromPage: 'CLResumePhone',
                })
                break
              }
              case 'publishJob': { // forcePublishJob: 弹框标识
                // 23245113【新增】发布招工-V1.9.0-B角色按工种强制发布
                await store.dispatch(actions.otherActions.setState({
                  recruitBackSource: {
                    path: `/${wx.$.r.getCurrentPage().route}`,
                    query: wx.$.r.getQuery(),
                    params: wx.$.r.getParams(),
                  },
                }))
                // 存store 跳转发布页面
                wx.$.r.push({
                  path: '/subpackage/recruit/fast_issue/index/index',
                  query: extparams.fastQuery || {},
                })
                break
              }
              case 'publish': {
                const { occIds } = template || {}
                wx.$.r.push({
                  path: '/subpackage/recruit/fast_issue/index/index',
                  query: { occV2: occIds },
                })
                break
              }
              default:
                fn.fail && fn.fail()
            }
          },
        })
        break
      }
      if (fn && fn.success) {
        fn.success()
      } else {
        wx.hideLoading()
        wx.$.msg(res.message)
      }
  }
}

type IMidCallData = {
  /** 回调中间号弹框信息 */
  callPopBack?: Function
  /** 拨打中间号 */
  callMidPhone?: Function
  /** 拨打真实或中间号码 */
  callRealPhone?: Function
  /** 点击拨打电话 */
  callPhone?: Function
  /** 发起电话成功 */
  callPhoneOk?: Function
}
/** 中间号相关弹窗--逻辑 */
export const operationMidCall = async (data, extParams: IOperExtParamsData = {}, fn: IMidCallData = {}) => {
  if (!data) {
    wx.$.msg('拨打失败，请稍后重试')
    return
  }
  const { dialogIdentifyDefault: dialogKy, tel } = data
  const { query, hasShowPhone } = extParams

  if (dialogKy) {
    let popContent = data.popContent?.popupOne || {}
    let newContent = {}
    let { popType } = data
    // 从谁联系过我的列表进入
    if (query && query.type === 'whoContacted') {
      popContent = [
        {
          color: '#000000',
          content: '为保证您的权益，请使用鱼泡安全号联系！',
        },
      ]
      popType = 4
    } else if (data.popContent && (dialogKy == 'tuifeierxuanyi' || dialogKy == 'fufeierxuanyi')) {
      newContent = {
        call_privacy_tel: data.popContent.callPrivacyTel,
        call_real_tel: data.popContent.callRealTel,
      }
      popType = 1
    } else if (data.popContent && (dialogKy == 'anquanhao' || dialogKy == 'tuifeianquanhao')) {
      newContent = {
        call_privacy_tel: data.popContent.callPrivacyTel,
      }
      popType = 2
    } else if (dialogKy == 'zhenshihaoma') {
      // 如果安全号配置打开，就走中间号逻辑
      if (hasShowPhone) {
        fn.callRealPhone && fn.callRealPhone()
        return
      }
      let ext = {}

      if (data.popContent && data.popContent.onlyRealTel) {
        ext = data.popContent.onlyRealTel
      }
      uploadDialog({ popCode: dialogKy, action: 1, text: '打开' })
      // 需要展示拨打前确认身份弹窗(鱼泡提醒弹框)
      wx.$.confirm({
        type: 'resumeIdentity',
        cancelText: '暂不提示',
        confirmText: '拨打电话',
        maskClose: true,
        ext,
      }).then(() => {
        uploadDialog({ popCode: dialogKy, action: 3, text: '拨打电话' })
      }).catch(() => {
        uploadDialog({ popCode: dialogKy, action: 3, text: '暂不提示' })
      }).finally(() => {
        fn.callRealPhone && fn.callRealPhone()
      })
      return
    } else {
      fn.callRealPhone && fn.callRealPhone(1)
      return
    }

    if (fn && fn.callPopBack) {
      fn.callPopBack({
        popType,
        popContent,
        newContent,
        popContentData: {
          has_expense_integral: data.has_expense_integral || (data.popContent && data.popContent.hasExpenseIntegral) || 0,
          is_expense_integral: data.is_expense_integral || (data.popContent && data.popContent.isExpenseIntegral) || 0,
        },
      })
    }
  } else if (tel) {
    fn.callPhoneOk && fn.callPhoneOk()
    const { timeRemaining, showRealTel } = data || {}
    if (extParams && extParams.isPrivacy == 1) {
      if (showRealTel) {
        const currentPage = wx.$.r.getCurrentPage()
        storage.setItemSync('showRealTel', currentPage.route)
      }
      if (timeRemaining) {
        await midPhoneTimeMsg(timeRemaining)
      }
    }
    setTimeout(() => {
      wx.$.u.callPhone(tel)
    }, 200)
  } else {
    fn.callRealPhone && fn.callRealPhone(1)
  }
}

// 默认为已拨打,拨打电话处理
export function resumeMidTel(params, ext: any = {}) {
  const { uuid, isPrivacy = 1, isPopup = 1 } = params
  ext && ext.loading && wx.showLoading({ title: '联系中...' })
  resumeTelChat(
    { ...params, isPrivacy, isPopup },
    { ...ext },
  ).then((res) => {
    wx.hideLoading()
    const { data } = res || {}
    if (!data) {
      return
    }
    const { tel, dialogIdentifyDefault, dialogIdentify } = data || {}
    if (tel || dialogIdentifyDefault || dialogIdentify) {
      operationMidCall(
        { ...res.data },
        { isPrivacy },
        {
          callPopBack: (pop) => {
            this.setData({
              showMiddleVisible: 'call',
              ...pop,
              popNewContent: pop.newContent,
            })
          },
          callRealPhone: (isPrivacy = 0) => {
            const next = { ...ext }
            delete next.from
            resumeMidTel.call(this, { uuid, isPrivacy, isPopup: 0 }, next)
          },
          callPhone: () => {
            const next = { ...ext }
            delete next.from
            resumeMidTel.call(this, { uuid }, next)
          },
          callPhoneOk: () => {
            this.setData({
              showMiddleVisible: '',
              phoneCallTitle: '',
            })
          },
        },
      )
    } else {
      resumeMidTel.call(this, { uuid, isPrivacy: 0, isPopup: 0 }, ext)
    }
  })
}

/**
 * 回拨电话
 * 接口: /reach/v1/privacyTel/popWindow/recallPopWindow
 * */
export async function recall(params, ext: any = {}, fn:any = {}) {
  const { failReport, successReport } = fn || {}
  const res = await wx.$.javafetch['POST/reach/v1/privacyTel/popWindow/privacyTelCallBackPopWindow']({ ...params })
  wx.hideLoading()
  const { message, data } = res
  const { isVirtual } = ext
  const { dialogData, callTel, timeRemaining, showRealTel, workerTel } = data || {}
  if (callTel) {
    successReport && successReport(res)
    ext.callfront && await ext.callfront(data)
    if (isVirtual) {
      if (showRealTel) {
        const currentPage = wx.$.r.getCurrentPage()
        storage.setItemSync('showRealTel', currentPage.route)
      }
      if (timeRemaining) {
        await midPhoneTimeMsg(timeRemaining)
      }
    }
    wx.$.u.callPhone(callTel)
    ext.callback && ext.callback(data)
    return
  }
  if (dialogData) {
    const { template, dialogIdentify } = dialogData
    /** 通用弹窗配置 */
    const popup = await dealDialogRepByApi(dialogIdentify, template)

    if (popup) {
      const midPops = ['recall_pop_force_privacy', 'anquanhao2_C', 'erxuanyi_C', 'zhenshihao_C']
      if (midPops.includes(dialogIdentify) || newRecruitMidPops().includes(dialogIdentify) || newResumeMidPops().includes(dialogIdentify)) {
        successReport && successReport(res)
        const midModel = newResumeMidPops().includes(dialogIdentify) ? wx.$.resumeMidModel : wx.$.recruitMidModal
        midModel({
          ...popup,
          workerTel,
          call: (val) => {
            const nParams: any = { ...params }
            if (val == 1) {
              nParams.getRealTel = true
            } else {
              nParams.getPrivacyTel = true
            }
            recall(nParams, { ...ext, isVirtual: val == 2 })
          },
          ...ext,
        })
      } else {
        failReport && failReport()
        wx.$.showModal({
          ...popup,
        })
      }
    }
  } else {
    failReport && failReport()
    wx.$.msg(message)
  }
}

/**
 * 加急拨v3
 * 接口: 加急拨（职位简历）
 * @param fromUserId 谁联系过我-用户id
 * @param getPrivacyTel 是否返回虚拟号，不为true时，返回的是弹窗标识
 * @param ext
 * @param isBoosCard 是否是联系老板
 * @param isPrivacy 是否返回中间号 1-是；0-否
 * @param isPopup 是否获取拨打弹窗 1-是；0-否
 * @param popupList 不需要弹的弹窗 Array
 * */
export async function recallV3(infoId, fromUserId, getPrivacyTel = false, ext: any = {}, isBoosCard, isPrivacy = 1, isPopup = 1, popupList: any = []) {
  let res: any = {
    data: {},
    message: '',
  }
  if (isBoosCard) {
    if (!getPrivacyTel) {
      res = await wx.$.javafetch['POST/job/v3/contact/urgent/tel/preCheck']({ resumeId: infoId, toUserId: fromUserId, notPopCodeList: popupList })
    } else {
      res = await wx.$.javafetch['POST/job/v3/contact/urgent/tel/call']({ resumeId: infoId, toUserId: fromUserId, isPrivacy, hasPopPrivacyTip: !isPopup })
    }
  } else if (!getPrivacyTel) {
    res = await wx.$.javafetch['POST/resume/v3/resumeTelChat/getTelChatPrePopupFromContactMe']({ jobId: infoId, fromUserId, popupList })
  } else {
    res = await wx.$.javafetch['POST/resume/v3/resumeTelChat/getTelChatFromContactMe']({ jobId: infoId, fromUserId, isPrivacy, isPopup })
  }
  // 老板

  wx.hideLoading()
  const { message, data, code } = res
  const { isVirtual, callPhoneOk, fn } = ext
  const { failReport, successReport } = fn || {}
  const { dialogData, dialogIdentifyDefault, tel, callTel, timeRemaining, showRealTel }: any = data || {}
  const { dialogIdentify, template } = dialogData || {}
  const phoneNum = callTel || tel
  if (phoneNum) {
    successReport && successReport(res)
    ext.callfront && await ext.callfront(data)
    if (isVirtual && timeRemaining) {
      await midPhoneTimeMsg(timeRemaining)
    }
    wx.$.u.callPhone(phoneNum)
    ext.callback && ext.callback(data)
    callPhoneOk && callPhoneOk()
    return
  }
  if (res.data && (phoneNum || dialogIdentifyDefault || newResumeMidPops().includes(dialogIdentify))) {
    successReport && successReport(res)
    operationMidCallV3(
      { ...res.data },
      { hasShowPhone: false },
      {
        callPopBack: (pop) => {
          wx.$.resumeMidModel({
            ...pop,
            zIndex: 10011,
            source: '2',
            infoId,
            pageName: '拨打记录',
            call: (e) => {
              const val = e.detail
              if (val == 2) {
                const showRealTelState = storage.getItemSync('showRealTelState')
                if (showRealTelState) {
                  const currentPage = wx.$.r.getCurrentPage()
                  storage.setItemSync('showRealTel', currentPage.route)
                }
              }
              recallV3(infoId, fromUserId, true, { ...ext, fn: {} }, isBoosCard, val == 2 ? 1 : 0, 0)
            },
          })
          this.callPhoneOk && this.callPhoneOk()
        },
        callRealPhone: (isPrivacy = 0) => {
          recallV3(infoId, fromUserId, true, { ...ext, fn: {} }, isBoosCard, isPrivacy, 0)
        },
        callPhone: () => {
          callPhoneOk && callPhoneOk()
          wx.$.u.callPhone(phoneNum)
        },
      },
    )
  } else if (dialogData) {
    /** 通用弹窗配置 */
    const popup = await dealDialogRepByApi(dialogIdentify, template)
    if (popup) {
      const pIds = [
        'job_call_pop_choose_privacy_free',
        'job_call_pop_force_privacy_free',
        'job_call_pop_force_privacy',
        'job_call_pop_choose_privacy',
        'job_call_pop_force_privacy_new',
        'job_call_pop_force_privacy_done',
        'job_call_pop_choose_privacy_done',
        'recall_pop_force_privacy',
        'erxuanyi_C', // 二选一弹窗-牛人
        'anquanhao1_C', // 强制安全号弹窗1-牛人
        'zhenshihao_C', // 真实号码弹窗-牛人
        'anquanhao2_C', // 强制安全号弹窗2-牛人
      ]
      const { workerTel } = template || {}
      if (pIds.includes(dialogIdentify) || newRecruitMidPops().includes(dialogIdentify)) {
        successReport && successReport(res)
        storage.setItemSync('showRealTelState', showRealTel)
        wx.$.recruitMidModal({
          zIndex: 10001,
          ...popup,
          workerTel,
          call: (val) => {
            let isPrivacy = 1
            if (val == 1) {
              isPrivacy = 0
            }
            if (val == 2) {
              const showRealTelState = storage.getItemSync('showRealTelState')
              if (showRealTelState) {
                const currentPage = wx.$.r.getCurrentPage()
                storage.setItemSync('showRealTel', currentPage.route)
              }
            }
            wx.showLoading({ title: '数据加载中' })
            recallV3(infoId, fromUserId, true, { ...ext, fn: {} }, isBoosCard, isPrivacy, 0)
          },
          ...ext,
        })
      } else {
        handleErrorBeforeCallV3(res, {}, {
          fail: () => {
          },
          successReport,
          failReport,
          requestAgain: (res) => {
            popupList.push(dialogIdentify)
            recallV3(infoId, fromUserId, true, { ...ext }, isBoosCard, 1, 1, popupList)
          },
        })
      }
    }
  } else if (code == 0 && !dialogIdentifyDefault && !phoneNum && !getPrivacyTel) {
    successReport && successReport(res)
    recallV3(infoId, fromUserId, true, { ...ext, fn: {} }, isBoosCard)
  } else if (code == 0 && !dialogIdentifyDefault && !phoneNum && getPrivacyTel) {
    successReport && successReport(res)
    recallV3(infoId, fromUserId, true, { ...ext, fn: {} }, isBoosCard, 0, 0)
  } else {
    failReport && failReport()
    wx.$.msg(message)
  }
}

/**
 * 中间号拨号有效时长提醒
 */
export function midPhoneTimeMsg(timeRemaining) {
  return new Promise((resolve) => {
    if (timeRemaining) {
      wx.$.msg(`此号码为鱼泡安全号，${timeRemaining}分钟有效，请尽快联系对方`)
      // 这里延迟1.2秒返回
      setTimeout(() => {
        resolve({ status: true })
      }, 1000)
    } else {
      resolve({ status: false })
    }
  })
}

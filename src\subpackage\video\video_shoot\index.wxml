<view class="video-shoot">
  <view class="mask {{videoType === '1' ? 'resume-mask' : ''}}" wx:if="{{shootState === 'default' || shootState === 'shooting' && videoType == '1'}}" style="padding-top:{{pagePaddingTop}}px;border-radius:{{shootState === 'shooting'?'0 0 16rpx 16rpx;':'none'}}" wx:if="{{screenDirect === 0}}">
    <!-- 问题列表 -->
    <view class="qa-list" wx:if="{{shootState === 'default'}}" style="margin-top:{{stickyTop}};display:{{screenDirect === 0 ?'block':'none'}}">
      <view class="qa-item" wx:for="{{questions}}" wx:key="index">
        <view class="qa-title">{{item.title}}</view>
        <view class="qa-content">{{item.question}}</view>
      </view>
    </view>
    <!-- 示例视频 -->
    <view class="qa-example" wx:if="{{shootState !== 'shooting' && videoType==='1'}}">
      <video-examples  videoUrl="{{videoUrl}}"  poster="{{poster}}" />
    </view>
    <swiper class="qa-swiper" interval="8000" current="{{currentQuestionIndex}}" autoplay vertical wx:if="{{shootState === 'shooting' && videoType ==='1'}}">
      <swiper-item wx:for="{{questions}}" wx:key="index">
        <view class="qa-item">
          <view class="qa-item-content">
            <view class="qa-title" style="padding-right: {{rectWidth+10}}px">{{item.title}}</view>
            <view class="qa-content">{{item.question}}</view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>
  <!-- 摄像机 -->
  <camera class="camera" wx:if="{{showCamera}}" device-position="{{devicePosition}}" resolution="{{resolution}}" flash="off" bind:error="onClickToPrev"></camera>
  <!-- 人像轮廓图 -->
  <image wx:if="{{videoType === '1'}}" class="person-outline" mode="widthFix" src="https://staticscdn.zgzpsjz.com/miniprogram/images/wjj/yp-mini_video_shoot_mbbg.png" />
  <!-- 底部操作按钮区域 -->
  <view class="footer">
    <block wx:if="{{shootState === 'default'}}">
      <!-- 退出拍摄 -->
      <view class="sub-btn" bind:tap="onClickToPrev" wx:if="{{videoType === '1' || source ==='0'}}">
        <icon-font type="yp-icon_cam_back" size="48rpx" color="#fff" />
        <view class="sub-btn-text">退出拍摄</view>
      </view>
      <view class="sub-btn-wrap" wx:if="{{videoType === '2' && source !=='0'}}">
        <view class="sub-btn-style-2" bind:tap="onClickToPrev">
          <icon-font type="yp-icon_cam_back" size="48rpx" color="#fff" />
          <view class="sub-btn-style-2-text">退出</view>
        </view>
        <view class="sub-btn-style-2 album" bind:tap="onClickPhotoAlbum">
          <icon-font type="yp-icon_video_photo" size="48rpx" color="#fff" />
          <view class="sub-btn-style-2-text">相册</view>
        </view>
      </view>
      <!-- 开始拍摄按钮 -->
      <image class="shoot-btn" bind:tap="onClickStartShoot" src="https://staticscdn.zgzpsjz.com/miniprogram/images/wjj/yp-mini_video_cam_act.png" />
      <!-- 翻转镜头 -->
      <view class="sub-btn" bind:tap="toggleCameraPosition" style="flex-direction:{{videoType === '1'?'row':'column'}};margin:{{videoType === '1'?'0':'0 35rpx'}}">
        <icon-font type="yp-icon_cam_change" size="48rpx" color="#fff" />
        <view class="sub-btn-text">翻转镜头</view>
      </view>
    </block>
    <view wx:if="{{shootState === 'shooting'}}" bind:tap="onStopShooting" class="shoot-btn-active">
      <!-- 拍摄中的进度条 -->
      <view class="stop-btn-wrap">
        <view class="btn-round">
          <view class="btn-square"></view>
        </view>
      </view>
      <canvas class="shoot-progress" canvas-id="progress" id="progress"></canvas>
      <view class="shoot-text">拍摄中{{currentShootingTime}}s</view>
    </view>
  </view>
</view>

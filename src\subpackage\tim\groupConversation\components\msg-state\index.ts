/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 普通招工详情，底部tabbar
 */
// import { store } from '@/store/index'

Component({
  properties: {
    // 'red'
    status: {
      type: String,
      value: {},
    },
    // 'left' || 'right'
    margin: {
      type: String,
      value: '',
    },
    msgInfo: {
      type: Object,
      value: {},
    },
    // 是否中间显示加载中
    isMid: {
      type: Boolean,
      value: false,
    },
  },
  data: {},
  lifetimes: {
    ready() { },
  },
  methods: {
    // 重发消息
    async onStatusClick(e) {
      await wx.$.u.waitAsync(this, this.onStatusClick, [e], 1000)
      this.triggerEvent('statusclcik', { type: e.currentTarget.dataset.type })
    },
  },
})

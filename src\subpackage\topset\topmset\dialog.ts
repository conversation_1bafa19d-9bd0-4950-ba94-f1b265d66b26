import { toLogin } from '@/utils/helper/common/toLogin'
import { isVerifyResumeCardStatusService } from '@/utils/helper/resume/index'

/**
 * @name 点击确定加急前置弹窗
 * @returns true 执行置顶接口请求 false 不执行
 */
export async function onSubmitPreShowModalDialog(pageData) {
  const { isLogin, form } = pageData
  try {
    if (!isLogin) {
      wx.$.confirm({ title: '您的登录已过期，请重新登录', cancelText: '取消', confirmText: '去登录' }).then(() => toLogin(true))
      return false
    }
    // 首次加急 或 未预约加急不需要进行审核中判断
    const isVerifySuccess = await isVerifyResumeCardStatusService({ isVerifyExist: true, isVerifyAuditFail: true })
    if (!isVerifySuccess) {
      return false
    }
    if (!form.city?.length) {
      wx.$.msg('请选择加急范围')
      return false
    }
    return true
  } catch (error) {
    return false
  }
}

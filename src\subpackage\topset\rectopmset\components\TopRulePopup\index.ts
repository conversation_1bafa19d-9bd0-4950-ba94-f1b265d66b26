/*
 * @Date: 2023-12-01 10:45:41
 * @Description:
 */

Component({
  properties: {
    visible: {
      type: Boolean,
      value: false,
    },
    zIndex: {
      type: Number,
      value: 10001,
    },
  },
  data: {
    tabs: [
      { id: 1, name: '产品介绍' },
      { id: 2, name: '用户评价' },
      { id: 3, name: '置顶规则' },
    ],
    activeTab: 1,
    /** 是否装载抽屉组件？ */
    loadedDrawerComp: false,
  },
  pageLifetimes: {},
  observers: {
    visible(val: boolean) {
      if (val) {
        this.setData({ loadedDrawerComp: true })
      }
    },
  },
  methods: {
    /** 抽屉组件已收起 */
    collapsedDrawer() {
      this.setData({ loadedDrawerComp: false }) // 卸载抽屉组件
    },

    /** 关闭整个弹窗 */
    async onClose() {
      await wx.$.u.waitAsync(this, this.onClose, [], 500)
      this.triggerEvent('close')
    },
    onTabChange(e) {
      const { detail } = e

      this.setData({
        activeTab: detail.item.id,
      })
    },
  },
})

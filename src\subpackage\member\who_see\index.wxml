<!-- -------- 页面导航栏 -->
<view class="page-navbar">
  <custom-header fixed="{{false}}" title="查看记录" border />
  <m-switchs tabList="{{tabList}}" tabIndex="{{activeTabIdx}}" bind:click="changeTab" />
</view>
<!-- -------- 谁看过我 -->
<view wx:if="{{activeTabIdx===0}}">
  <brows-list id="brows-list" tabHeight="{{tabHeight + 44}}px" advertUnitId="{{advertUnitId}}" />
</view>
<!-- -------- 我看过谁 -->
<view wx:if="{{activeTabIdx===1}}">
  <!-- <buoy-of-msg wx:if="{{role == 1 && hiddenNumber > 0}}" hiddenNumber="{{hiddenNumber}}" /> -->
  <view wx:for="{{myLookParams.list}}" class="mybrowsercard" data-index="{{index}}" data-item="{{item}}" wx:key="id">
    <block wx:if="{{role == 2}}">
      <my-browse-card item="{{item}}" nearbyWorkerListApiSource="BrowsingHistory" />
    </block>
    <block wx:if="{{role == 1}}">
      <view class="card">
        <resume-card-v4 sourceId="21" sceneV2="10" jobId="{{item.jobId}}" item="{{item}}" data-item="{{item}}" data-index="{{index}}" nearbyWorkerListApiSource="BrowsingHistory" custom-class="custom-card" class="mybrowsercard" />
      </view>
    </block>
    <!-- 广告每隔四条数据一条 -->
    <block wx:if="{{index && (index + 1) % 4 === 0 && advertUnitId}}">
      <advert custom-class="advert-class" unitId="{{advertUnitId}}" />
    </block>
  </view>
  <yp-load-more show="{{!myLookParams.isEmpty}}" state="{{myLookParams.loadMoreState}}" finishText="- 已展示您最近30天的查看记录 -" />
  <yp-empty show="{{myLookParams.isEmpty}}" img="list" img-class="img-class" tip="最近30天暂无联系记录" style="padding-top:176rpx;">
    <view wx:if="{{role == 2}}" bind:tap="clickEmptyBtn" data-type="look_zg" class="empty-btn">
      查看职位
    </view>
    <view wx:else bind:tap="clickEmptyBtn" data-type="look_zh" class="empty-btn white-btn">
      查看简历
    </view>
  </yp-empty>
</view>
<!-- -------- 一键到顶 -->
<back-top />
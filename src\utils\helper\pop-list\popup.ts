/*
 * @Date: 2022-03-19 16:34:58
 * @Description: 弹窗公共逻辑
 */

import { dispatch, actions, store, messageQueue, storage } from '@/store/index'
import dayjs from '@/lib/dayjs/index'
import { isIos } from '@/utils/tools/validator/index'
import type { PopupType } from '@/store/model/recruit/index/data'
import { requestSubscribeMessage } from '@/utils/helper/index'
import { dealDialogApi, dealDialogRepByApi, postResume1DataSave, uploadDialog } from '@/utils/helper/dialog/index'
import { isByAdcodeRegion } from '@/utils/helper/location/index'
import { getObjVal } from '@/store/model/storage/utils'

/**
 * 引导用户获取积分弹窗
 * @param dialog_identify 当前弹窗key
 * @returns
 */
export const userPointGuide = (dialog_identify) => {
  const { userState, common: { earnPointsExpiredTime } } = store.getState().storage
  const { userIntegralObj } = store.getState().user.userInfo || {}
  if (!userState || !userState.uuid || isIos() || (userIntegralObj?.integral && Number(userIntegralObj.integral) >= 11)) {
    return
  }
  // 该弹窗弹出的时间戳
  const earnPointsTime = dayjs().valueOf()
  if (!earnPointsExpiredTime || earnPointsTime >= earnPointsExpiredTime) {
    // 下次弹出弹窗的时间
    const currentWeekTime = dayjs()
      .add(7, 'day')
      .valueOf()
    // 弹窗曝光埋点
    const reportParams: any = {
      name: '引导用户进行充值1',
      page_name: '首页',
    }
    wx.$.collectEvent.event('rechargeUserPopupExposure', reportParams)
    wx.$.confirm({
      content: '马上获取积分，快速找到好活',
      confirmText: '获取积分',
    })
      .then(() => {
        // 点击上报
        wx.$.collectEvent.event('rechargeUserPopupClick', { ...reportParams, click: '获取积分' })
        uploadDialog({ popCode: dialog_identify, action: 3, text: '获取积分' })
        wx.$.toGetIntegral({
          isFromHomePage: 1,
          origin: 'CLRecruitPhone',
        })
      }).catch(() => {
        // 点击上报
        wx.$.collectEvent.event('rechargeUserPopupClick', { ...reportParams, click: '取消' })
        uploadDialog({ popCode: dialog_identify, action: 2, text: '取消' })
      }).finally(() => {
        setIndexModal(false)
      })
    // 储存下一次弹出的时间
    dispatch(actions.storageActions.setCommonItem({ earnPointsExpiredTime: currentWeekTime }))
  }
}

// ! 刷新弹窗, 发布招工弹窗
/**
 * 发布找活名片弹窗
 * @param dialog_identify 当前弹窗id
 */
export const publishPop = (dialog_identify) => {
  const uploadStatisticsData = (eventName, eventData = {}) => {
    wx.$.collectEvent.event(eventName, { name: '发布名片引导', page_name: '首页', ...eventData })
  }
  uploadStatisticsData('findJobUserPopupExposure')
  wx.$.confirm({
    title: '重要提示',
    content: '您在找活吗？免费发布简历，让老板主动联系你！',
    confirmText: '免费发布',
  }).then(() => {
    uploadDialog({ popCode: dialog_identify, action: 3, text: '确认' })
    uploadStatisticsData('findJobUserPopupClick', { click: '确认' })
    // 确定 跳转到发布找活 清空数据
    wx.$.r.push({ path: '/subpackage/resume/resume_publish/index?origin=complete&ptype=perfect' })
  }).catch(() => {
    uploadDialog({ popCode: dialog_identify, action: 2, text: '取消' })
    uploadStatisticsData('findJobUserPopupClick', { click: '取消' })
  }).finally(() => {
    setIndexModal(false)
  })
}

/** 发布找活名片弹窗 v4.0.0 弹框标识：post_resume1
 * 触发用户：选择了工人身份但是没有发布找活名片的用户
 * 触发时机：该类用户未发布过招工信息，当天查看了1条招工信息或点击3次招工信息后，返回列表时触发此弹窗
 * 触发频次: 单个用户触发一次
 * return: true-弹出弹框，false-不弹出弹框
 */
export const publishPopV4 = async function (): Promise<boolean> {
  const popCode = 'post_resume1'

  const { login } = store.getState().storage.userState
  if (!login) {
    // 未登录的不执行弹框逻辑 或者onShowNum执行次数小于2次
    return false
  }
  // 获取该弹框的缓存配置
  const popData = storage.getItemSync('post_resume1')
  if (popData.isShow) {
    // 今日已弹出
    return false
  }
  if (popData.contactNum < 1 && popData.detailNum < 3) {
    // 不满足弹出条件
    return false
  }
  const { data } = await wx.$.javafetch['POST/cms/popup/v1/showConditionCheck']({
    popCode,
  }).catch(() => {
    return { data: { result: false } }
  })
  if (!data || !data.result) {
    return false
  }

  const popup = await dealDialogRepByApi(popCode, {})
  if (!popup) {
    return false
  }
  wx.$.showModal(popup).then(modal => {
    const modalObj = modal || { jumpEventType: 3, btnIndex: -1 }
    if (modalObj.jumpEventType != 4) {
      return
    }
    if (modalObj.btnIndex == 1) {
      wx.$.r.push({ path: '/subpackage/resume/resume_publish/index?origin=complete&ptype=perfect' })
    }
  })
  // 弹出弹框更新弹框缓存
  postResume1DataSave('show')
  return true
}

// indexGetDialog
/** 城市切换温馨提示弹框 弹框标识: changeCity */
export const locationPopCity = async function (): Promise<boolean> {
  const popCode = 'changeCity'
  const { showCitySwitch } = store.getState().map
  const { login } = store.getState().storage.userState
  if (!login) {
    // 未登录的不执行弹框逻辑 或者onShowNum执行次数小于2次
    return false
  }
  // 获取该弹框的缓存配置
  const popData = storage.getItemSync('locationPopCity')
  const thatTime = dayjs().format('YYYYMMDD')
  if (!showCitySwitch) {
    // 不显示城市切换弹窗
    return false
  }
  if (popData && popData.dateTime && popData.dateTime > thatTime) {
    // 下一次弹出的时间小于当前时间
    return false
  }
  // 当前选择的地址
  const userLocationCity = storage.getItemSync('userLocationCity')
  const { recruitCityObj, id: curId, name: curName } = userLocationCity || {}
  const { id, name, citys } = recruitCityObj || {}
  // 当前定位的地址
  const userLocation_city = storage.getItemSync('userLocation_city')
  if (!id || !userLocation_city || !userLocation_city.cityId) {
    return false
  }
  if (((id || curId) == '1') || ((name || curName) == '全国')) {
    // 当前选择的地址是全国
    return false
  }
  const cityArea = (citys || []).find(item => !!item.id)
  let isEqual = false

  if (cityArea && cityArea.id) {
    const areaInfo = await wx.$.l.getAreaById(cityArea.id)
    if (areaInfo.current) {
      const isRegion = isByAdcodeRegion(areaInfo.current.ad_code, 'region')
      if (isRegion) {
        isEqual = userLocation_city.cityId == areaInfo.province.id
      } else {
        isEqual = userLocation_city.cityId == areaInfo.city.id
      }
    }
  }

  if (isEqual) {
    // 当前选择的地址和定位的地址一致
    return false
  }
  const { data } = await wx.$.javafetch['POST/cms/popup/v1/showConditionCheck']({
    popCode,
  }, { hideMsg: true }).catch(() => {
    return { data: { result: false } }
  })
  if (!data || !data.result) {
    return false
  }

  const popup = await dealDialogApi({
    dialogIdentify: popCode,
    template: {},
    isRule: true,
  })
  if (!popup) {
    return false
  }
  // 弹出前再检查一遍
  const city_v = storage.getItemSync('userLocationCity')
  const cityName = getObjVal(city_v, 'recruitCityObj.name') || getObjVal(city_v, 'name')
  const cur_city_v = storage.getItemSync('userLocation_city') || {}
  console.log('cityName', cityName, 'cur_city_v.cityName', cur_city_v.cityName)
  if (cityName === cur_city_v.cityName){
    return false
  }
  this.setData({
    currDialog: popup.currDialogConfig,
    showCitySelectPop: true,
  })
  const dateTime = dayjs().add(1, 'day').format('YYYYMMDD')
  storage.setItemSync('locationPopCity', {
    ...popData,
    dateTime,
    detailNum: popData.detailNum ? popData.detailNum + 1 : 1,
  })
  return true
}

// ! 中间号未读消息模版订阅弹框
/** 中间号未读消息模版订阅弹框-弹出逻辑-仅微信环境 */
export const midphoneMessageVisible = async () => {
  if (!ENV_IS_WEAPP) { // 非微信环境不弹出
    return false
  }
  // 打开弹窗
  const { login } = store.getState().storage.userState
  if (!login) {
    return false
  }
  const midphoneMessage = storage.getItemSync('midphoneMessage')
  const visible = await isShowPopType('midphoneMessagePop')
  /** 消息订阅的判断逻辑 */
  const midohoneBool = await requestSubscribeMessage.messageMidohoneBool()
  if (visible && !midphoneMessage && midohoneBool.code == 200) {
    return true
  }
  return false
}

/** 中间号未读消息模版订阅弹框-关闭逻辑 */
export const midphoneMessageClose = async () => {
  /** 中间号引导弹框关闭逻辑 */
  closePop()
}

/**
 * 积分刷新找活名片
 * @param currDialog 当前弹窗
 * @returns
 */
export const refreshCord = (currDialog) => {
  const { popCode, logicTransferData: data } = currDialog || {}
  const spliceContent = [{ text: '您的简历排名靠后，去加急或消耗' }, { text: `${data?.integral}积分`, color: '#0092FF' }, { text: '刷新简历，增强找活效率。' }]
  storage.setItemSync('indexResumeRefreshNum', 1)
  return wx.$.confirm({
    spliceContent,
    cancelText: '去加急',
    confirmText: `${data?.integral}积分刷新`,
    cancelIcon: true,
  }).then(() => {
    uploadDialog({ popCode, action: 3, text: '去刷新' })
    // 调用刷新
    wx.$.l.resumesRefresh({ }, 'index', {
      fail: () => { },
      success: () => {
        const myResumeRefreshFirst = storage.getItemSync('myResumeRefreshFirst')
        if (!myResumeRefreshFirst) {
          storage.setItemSync('myResumeRefreshFirst', true)
        }
      },
    })
  }).catch(({ cancelIcon }) => {
    if (cancelIcon) {
      uploadDialog({ popCode, action: 2, text: '取消' })
      return
    }
    uploadDialog({ popCode, action: 3, text: '去加急' })
    // wx.$.r.push({ path: '/subpackage/topset/topmset/index' })
    const urlTop = encodeURIComponent('/urgent-resume?epc=active_jiajijianlih5')
    wx.$.r.push({
      path: `/subpackage/web-view/index?url=${urlTop}&isLogin=true`,
    })
  }).finally(() => {
    setIndexModal(false)
  })
}

/** 通用弹框的点击按钮处理-前置逻辑处理 */
export const handlerShowModalBefore = (dialog_identify) => {
  switch (`${dialog_identify}`) {
    case 'shuaxinjiaji':// 刷新找活名片
    case 'shuaxinjiajinew':
      storage.setItemSync('indexResumeRefreshNum', 1)
      break
    default:
  }
}

/**
 * 通用弹框的点击按钮处理
 * @returns
 */
export const handlerShowModalClick = (modal, dialog_identify) => {
  setIndexModal(false)
  const modalObj = modal || { jumpEventType: 3, btnIndex: -1 }
  if (modalObj.jumpEventType != 4) {
    return
  }
  switch (`${dialog_identify}`) {
    case 'shuaxinjiaji':
    case 'shuaxinjiajinew':
      if (modalObj.btnIndex == 1) {
        // 刷新找活名片
        wx.$.l.resumesRefresh({ }, 'index', {
          fail: () => { },
          success: () => {
            const myResumeRefreshFirst = storage.getItemSync('myResumeRefreshFirst')
            if (!myResumeRefreshFirst) {
              storage.setItemSync('myResumeRefreshFirst', true)
            }
          },
        })
      }
      break
    default:
  }
}

// 计算名片相关弹窗弹出时间 是否已经达到弹出值
export const getPopupTime = (time: number): number => {
  return time * 24 * 60 * 60 * 1000
}
/** 重置计次 */
export const resetRefreshAndPublishPop = async (type) => {
  const thisTime = new Date().getTime()
  if (type == 'refresh') {
    const newPublishPopup = {
      time: thisTime,
      count: 0,
    }
    dispatch(actions.storageActions.setCommonItem({ recruitRefreshResumeCount: newPublishPopup }))
  }
  if (type == 'publish') {
    const newPublishPopup = {
      time: thisTime,
      count: 0,
    }
    dispatch(actions.storageActions.setCommonItem({ recruitPublishResumeCount: newPublishPopup }))
  }
}

/** 关闭弹框，计时计数 */
export const saveRefreshAndPublishPop = async (type) => {
  const thisTime = new Date().getTime()
  if (type == 'refresh') {
    const refreshPopup = store.getState().storage.common.recruitRefreshResumeCount
    const count = refreshPopup?.count || 0
    const newPublishPopup = {
      time: thisTime,
      count: count + 1,
    }
    dispatch(actions.storageActions.setCommonItem({ recruitRefreshResumeCount: newPublishPopup }))
  }
  if (type == 'publish') {
    const publishPopup = store.getState().storage.common.recruitPublishResumeCount
    const count = publishPopup?.count || 0
    const newPublishPopup = {
      time: thisTime,
      count: count + 1,
    }
    dispatch(actions.storageActions.setCommonItem({ recruitPublishResumeCount: newPublishPopup }))
  }
}

// ! 公共控制弹窗逻辑
/** 根据弹窗优先级判断是否显示 */
export const isShowPopType = async (val: PopupType) => {
  await messageQueue((state) => state.storage.L_IS_GEO_AUTH)
  /** 获取可展示弹窗类型 */
  const { popupType } = store.getState().index
  /** 获取所有弹窗的队列 */
  const popQueue = Object.keys(popupType)
  /** 获取当前的弹窗的索引 */
  const popIndex = popQueue.findIndex((item) => item == val)
  /** 获取异步方法执行之后新的popupType的model */
  const { popupType: nPopupType } = store.getState().index
  /** 获取所有的弹窗显示隐藏队列 */
  const showQueue = Object.values(nPopupType)
  /** 获取弹窗显示的索引 */
  const showIndex = showQueue.findIndex((item) => item == true)
  /** 判断是否显示隐藏 */
  let show = false
  // 判断如果弹窗的索引小于显示的索引则显示弹窗的索引
  if (popIndex < showIndex || showIndex < 0) {
    show = true
  }
  return show
}

/** 显示指定的弹窗 */
export const setShowPopType = (val: PopupType) => {
  const { popupType } = store.getState().index
  /** 获取所有的弹窗显示隐藏队列 */
  const showQueue = Object.values(popupType)
  /** 获取弹窗显示的索引 */
  const showIndex = showQueue.findIndex((item) => item == true)
  /** 获取所有弹窗的队列 */
  const popQueue = Object.keys(popupType)
  /** 获取当前的弹窗的索引 */
  const popIndex = popQueue.findIndex((item) => item == val)
  // 判断如果弹窗的索引小于显示的索引则显示弹窗的索引
  if (popIndex < showIndex || showIndex < 0) {
    setIndexModal(true)
    dispatch(actions.recruitIndexActions.setShowPopType(val))
  }
}

/** 只能出现一个弹窗，关闭则关闭所有弹窗 */
export const closePop = () => {
  setIndexModal(false)
  dispatch(actions.recruitIndexActions.closePopType())
}

/** 设置当前首页弹出窗口状态 */
export const setIndexModal = (status: boolean) => {
  storage.setItemSync('indexShowModal', status)
}

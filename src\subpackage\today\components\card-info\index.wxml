<view class="worker-card">
  <!-- ------ 审核中 -->
  <view wx:if="{{item.hide}}" class="audit-card">
    <image class="audit-img" src="https://staticscdn.zgzpsjz.com/miniprogram/images/wyl/yp_mini_bianzushenhe.png" lazy-load />
    <view class="audit-tip">该简历当前处于系统审核中，</view>
    <view>请稍后再来查看</view>
  </view>
  <block wx:else>
    <!-- ------ 可上下滑动的用户信息盒子 -->
    <view class="worker-info">
      <!-- ------ 用户信息 -->
      <view class="base-info">
        <view class="avatar-box">
          <image src="{{item.avatar}}" class="avatar" mode="aspectFill" lazy-load />
        </view>
        <view class="base-right">
          <view class="user-info">
            <view class="username">{{item.username}}</view>
            <yp-tag wx:if="{{item.realNameStatus==2}}" type="success">已实名</yp-tag>
            <view wx:if="{{item.ipHome}}" class="ip-home">IP属地：{{item.ipHome}}</view>
          </view>

          <view class="sex-box">
            <view>{{item.sexAgeNationStr}}</view>
            <view class="work-status">
              <block wx:if="{{item.workStatus==1}}">
                <view wx:if="{{item.days>0}}">预计<text class="enter-day-num">{{item.days}}</text>天后可进场</view>
                <view wx:else>随时进场</view>
              </block>
              <block wx:elif="{{!item.isSelf&&item.isFindWork}}">暂不找活</block>
             </view>
          </view>
        </view>
      </view>

      <!-- ------ 用户标签 -->
      <view class="user-tags">
        <view wx:for="{{item.userTags}}" wx:key="index" class="user-tag">
          <image wx:if="{{item.img}}" class="user-tag-icon" src="{{item.img}}" />
          <icon-font wx:else custom-class="user-tag-icon" type="{{item.icon}}" size="32rpx" />
          <text>{{item.text}}</text>
        </view>
      </view>

      <!-- ------ 求职期望 -->
      <view class="expected-work">
        <view class="title">求职期望</view>
        <view wx:if="{{isLoc&&item.distance}}" class="distance">距离您{{item.distance}}</view>
      </view>

      <!-- ------ 期望工资 -->
      <view wx:if="{{item.expectedSalary}}" class="expect-salary">
        期望工资：<text class="money">{{item.expectedSalary}}</text>
      </view>

      <!-- ------ 工作地 -->
      <view wx:if="{{item.workCity}}" class="expect-addr">工作地: {{item.workCity}}</view>

      <!-- ------ 工种标签 -->
      <view wx:if="{{item.craftTags.length}}" class="work-type-tags">
        <yp-tag wx:for="{{item.craftTags}}" wx:key="index" type="grey" my-class="work-type-tag">{{item}}</yp-tag>
      </view>

      <!-- ------ 介绍 -->
      <view wx:if="{{item.introduce}}" class="introduce">{{item.introduce}}</view>

      <!-- ------ 视频和图片 -->
      <img-view wx:if="{{item.expectMedia.length}}" list="{{item.expectMedia}}" custom-class="expect-media" />

      <!-- ------ 项目经验 -->
      <block wx:if="{{item.projectList.length}}">
        <view class="project-header">项目经验</view>
        <project-work-card projectList="{{item.projectList}}" />
      </block>
    </view>

    <!-- ------ 固定的收藏/投诉/分享 -->
    <view class="footer">
      <view class="footer-item">
        <view bind:tap="clickComplained" class="complain-btn">
          <icon-font type="yp-icon_tusu" size="40rpx" color="#000" />
          <text class="text">{{item.isComplained?'已投诉':'投诉'}}</text>
        </view>
      </view>
      <view class="footer-item">
        <button open-type="share" data-id="{{item.id}}" data-uuid="{{item.uuid}}" data-sub-uuid="{{item.subUuid}}" class="share-btn">
          <icon-font custom-class="item-icon" type="yp-icon_share" size="40rpx" color="rgba(0, 0, 0, 0.85)" />
          <text class="text">分享</text>
        </button>
      </view>
    </view>
  </block>
</view>

<view class="msg-txt-v {{msgInfo.isSelf?(msgInfo.payload.data.type == '5'?'msg-txt-v-r-w':'msg-txt-v-r'):'msg-txt-v-l'}}">
  <longpress-tk msgInfo="{{msgInfo}}" wx:if="{{isShowTk}}" tkWith="{{tkWith}}" catch:click="onTkClick" />
  <view class="js-hetong-v" wx:if="{{msgInfo.payload.data.type == '5'}}">
    <view class="js-hetong-title">电子合同签署</view>
    <view class="js-hetong-detail">对方给你发来了《{{msgInfo.customExts.title}}》，请前往客户端查看详情</view>
  </view>
  <view wx:else class="msg-txt {{msgInfo.isSelf?'':'msg-other'}}" catch:longpress="onLongpressMsg" id="{{msgInfo.id}}_tk">
    <view class="dom_em" wx:if="{{!!msgInfo.payload.renderDom && msgInfo.payload.renderDom.length > 0}}">
      <block wx:for="{{msgInfo.payload.renderDom}}" wx:for-item="emItem" wx:key="index">
        <view class="custom-btn {{msgInfo.isSelf?'':'callphone-click'}}" wx:if="{{!msgInfo.isSelf && emItem.name == 'btn'}}" data-tel="{{emItem.tel}}" catch:tap="callPhone">
          {{emItem.text}}
        </view>
        <text wx:elif="{{ emItem.name != 'btn'}}">{{emItem.text}}</text>
      </block>
    </view>
    <text wx:else>{{msgInfo.payload.data}}</text>
  </view>
  <!-- <view class="arrow {{msgInfo.isSelf?'arrow-r':'arrow-l'}}"></view> -->
</view>
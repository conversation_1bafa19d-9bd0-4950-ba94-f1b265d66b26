<view>
  <view class="search">
    <view class="search-cont">
      <view class="search-input">
        <icon-font type="yp-icon_search" custom-class="{{isValue ? 'input-icon' : ''}}"></icon-font>
        <input class="input-text" bind:input="onChange" bind:focus="onFocus" type="text" value="{{inputValue}}" placeholder="请输入城市名称" />
        <icon-font type="yp-icon_close" custom-class="input-close {{!isValue ? 'input-close-hide' : ''}}" bind:tap="onClear"></icon-font>
      </view>
      <view class="search-btn" bind:tap="onCancel" wx:if="{{searchShow}}">取消</view>
      <view class="search-btn blue-btn" bind:tap="onFocus" wx:if="{{!searchShow}}">搜索</view>
    </view>
    <view class="search-select">
      <view>{{selectCities.length > 0 ? '已选择：' :  '请选择' + originText + '范围'}}</view>
      <view class="search-select-text">
        <block wx:for="{{selectCities}}" wx:key="index">{{index > 0 ? ' 、' : ''}}{{item.name}}</block>
      </view>
    </view>
  </view>
</view>

/* eslint-disable consistent-return */
import { dispatch, actions, getState } from '@/store/index'
import dayjs from '@/lib/dayjs/index'

import { getExpendIntegralService, getUpgradeOrderInfoApi } from './api-server'
import { dealDialogApi } from '@/utils/helper/dialog/index'

/**
 * @name 设置默认选中加急范围
 * @param thisData 页面数据
 * @param upgradeOrderInfo 加急订单信息
 */
export async function setDefaultSelectRangeCity(thisData, upgradeOrderInfo) {
  const { basicResp, form } = thisData
  try {
    // 判断是否存在置顶信息 2 置顶中 4 暂停置顶
    const isTopping = upgradeOrderInfo.topStatus == 2 || upgradeOrderInfo.topStatus == 4
    const topArea = isTopping && Array.isArray(upgradeOrderInfo.topArea) && upgradeOrderInfo.topArea.length > 0 ? upgradeOrderInfo.topArea : null
    const showUpgradeArea: any[] = await fmtAreaToCityInfo(topArea || basicResp.hopeAreaCity.slice(0, 1) || [])

    if (!showUpgradeArea || showUpgradeArea.length === 0) {
      return form
    }

    const initSelectAreaInfo = []
    const promises = await Promise.all(showUpgradeArea.map(it => wx.$.l.getAreaById(it.id)))
    promises.forEach((res) => {
      if (res?.current) {
        initSelectAreaInfo.push(res.current)
      }
    })
    return { ...form, city: initSelectAreaInfo, oldCity: initSelectAreaInfo }
  } catch (error) {
    return form
  }

  // 过滤地区信息为城市信息
  async function fmtAreaToCityInfo(areaArr: any[]) {
    try {
      if (!Array.isArray(areaArr) || areaArr.length === 0) {
        return []
      }

      const tempAreaArr: any[] = []
      const promises = await Promise.all(areaArr.map(it => wx.$.l.getAreaById(it.id)))
      promises.forEach(res => {
        if (res) {
          const { province, city, current } = res
          if (current && province) {
            // 判断每个省 第一个市 下面是否存在一个区 用于过滤 区 (使用父级别地区信息)
            const hasArea = province.children?.[0]?.children?.[0]
            tempAreaArr.push(hasArea ? (city || province) : province)
          }
        }
      })
      return tempAreaArr
    } catch (error) {
      console.error(error)
      return []
    }
  }
}

/** 找活置顶配置信息返回状态 */
export type TopConfig = IModels['GET/job/resumes/topConfig']['Res']['data'] & {
  /** 供选择器使用的时间数组 */
  daysArr?: { name: string; value: number }[]
}

/** 找活名片流氓弹窗时间和状态设置 */
export function updateFirstSetTopTimeBack(status) {
  // 找活名片流氓弹窗时间和状态设置
  const { firstSetTopTimeBack } = getState().storage
  if (firstSetTopTimeBack.status == 2) {
    const canRunSetItem = !dayjs(firstSetTopTimeBack.time).isSame(dayjs(), 'day')
    if (canRunSetItem) {
      dispatch(
        actions.storageActions.setItem({
          key: 'firstSetTopTimeBack',
          immer(state) {
            state.firstSetTopTimeBack.status = status
            state.firstSetTopTimeBack.time = new Date().getTime()
          },
        }),
      )
    }
  }
}

/** 获取格式化后的 days 数组用于页面展示 */
export function getFmtDaysArr(days: any[]) {
  const daysMapItem = (day) => {
    const name = day < 4 ? `${24 * day}小时(${day}天)` : `${day}天`
    return { name, value: day }
  }
  return days.map(daysMapItem)
}

/**
 * @name 上一个页面是否为加急找活页面且已加急情况返回到找活名片页
 * @description 📢因为该页面不会存在套娃🪆的情况所以可以这么操作(其它情况需通过标识调整)
 */
export async function prePageIsToppingPageAndIsResumeTopping() {
  try {
    const res = await getUpgradeOrderInfoApi()
    /**
     * 置顶状态 topStatus 1未置顶 2置顶中 3置顶过期 4暂停加急 5未预约加急 6已预约加急
     */
    // 未购买加急, 弹邀请注册弹窗。(如果有挽留弹窗，挽留弹窗优先级最高)
    if (res?.topStatus == 1) {
      const popup = await dealDialogApi({ dialogIdentify: 'share_qy', isRule: true })
      if (popup) {
        wx.$.model.inviteWorkers({
          sharePage: 'jiaji/share/page',
          sharePath: 'jiaji/share/path',
          dialog_identify: 'share_qy',
          shareName: '加急简历',
        })

        return
      }
    }

    if (res?.topStatus != 2) {
      wx.$.r.back()
      return
    }

    const pages = getCurrentPages() as any[]
    const pageRoute = wx.$.r.getCurrentPage()?.route
    const findIdx = pages.findIndex((it) => it.route === pageRoute)
    if (findIdx === -1) {
      wx.$.r.back()
      return
    }

    const num = pages.length - findIdx
    wx.$.r.back(num)
  } catch (error) {
    console.error(error)
    wx.$.r.back()
  }
}

/**
 * @description 计算需要消耗的积分
 * @param form 加急表单信息
 * @param orderInfo 加急订单信息
 */
export async function calcIntegral(form, orderInfo) {
  const { city = [], time: day } = form
  const { topStatus, orderId } = orderInfo

  const isMod = topStatus == 2 || topStatus == 4
  const areaIds = city.map((it: any) => parseInt(it.id, 10)).filter(Boolean)
  const expendIntegralInfo = await getExpendIntegralService({ day, areaIds, orderId }, isMod)

  return expendIntegralInfo.discountPrice
}

export function isModTopAreaFn(orderTopArea: any[], city: any[]): boolean {
  return orderTopArea.length !== city.length || isModCityFn()

  function isModCityFn() {
    const ids = orderTopArea.map((it) => String(it.id))
    return city.some((it: any) => !ids.includes(String(it.id)))
  }
}

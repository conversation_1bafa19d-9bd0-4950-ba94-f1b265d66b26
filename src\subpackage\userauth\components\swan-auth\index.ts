/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-01-21 09:45:39
 * @Description: 百度授权
 */
import { MapStateToData, connect, store } from '@/store/index'
import { helper } from '@/utils/index'

import { LOGIN_LOGO } from '../common/data'
import { toLogin } from '@/utils/helper/common/toLogin'

const { login } = helper
const { jumpTo, detailUserLoginInfo, afterLogin } = login

const mapStateToData: MapStateToData = (state) => {
  const { storage } = state
  return {
    sourceCode: storage.sourceCode,
    sourceShare: storage.source_share,
  }
}

Component(
  connect(mapStateToData)({
    // 组件的属性列表
    properties: {
      // 判断是否为页面
      isPage: {
        type: Boolean,
        value: false,
      },
    },

    // 组件的初始数据
    data: {
      loading: false,
      LOGIN_LOGO,
    },

    // 组件的生命周期
    lifetimes: {
      // 在组件实例进入页面节点树时执行
      attached() {},
      // 销毁的时候通知其他地方登录成功还是失败
      detached() {
        // 登录失败
        if (!store.getState().storage.userState.login) {
          toLogin.callReject()
        }
      },
    },

    // 组件的方法列表
    methods: {
      // 授权登录获取手机号
      async onGetPhoneNumber(e) {
        if (e.detail.errMsg !== 'getPhoneNumber:ok') {
          /** 点击拒绝授权，啥也不干 */
          return
        }
        if (!this.data.loading) {
          return
        }
        this.setData({
          loading: false,
        })
        const { iv, encryptedData } = e.detail
        wx.getLoginCode({
          success: (res) => {
            if (res?.code) {
              this.userAuthRequest({ iv, ciphertext: encryptedData, code: res?.code })
            }
          },
          fail(err) {
            wx.$.alert({ content: JSON.stringify(err) })
          },
        })
      },
      // 百度授权登录
      async userAuthRequest(params) {
        wx.showLoading({ title: '登录中...' })
        const data = { ...params, refid: this.data.sourceCode, share_source: this.data.sourceShare }
        wx.$.fetch['GET/login/mobile/baiduLogin'](data).then(res => {
          wx.hideLoading()
          if (res.head.code === 200 && res.data.token) {
            detailUserLoginInfo(
              res,
              (resp, newMember) => this.newInterfaceDealInfo(resp, newMember),
              (tip) => wx.$.msg(tip),
            )
          }
        }).catch(res => {
          wx.hideLoading()
          wx.$.msg(res.head.msg)
        })
      },
      // 获取用户信息
      async newInterfaceDealInfo(resp, newMember) {
        const user = {
          userId: Number(resp.data.user_id),
          token: resp?.data?.sign?.token,
          tokenTime: resp?.data?.sign?.time,
          uuid: resp.data.uuid,
          login: true,
          tel: resp.data.tel,
          role: resp.data.role,
          newMember,
        }
        afterLogin(user)
        if (this.data.isPage) {
          jumpTo()
        } else {
          this.triggerEvent('hideLogin')
        }
      },
      onAuthLogin() {
        this.setData({
          loading: true,
        })
      },
      onJumpLogin() {
        wx.$.r.push({
          path: '/subpackage/userauth/tel/index',
        })
      },
    },
  }),
)

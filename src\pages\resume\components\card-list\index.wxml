<view class="body" style="min-height: calc(100vh - {{topHeight}} - 120rpx); ">
  <empty-box-network visible="{{isNetWorkShow}}" bind:onReset="onRefresh" bind:netWorkChange="onNetWorkChange" />
  <block wx:if="{{!isNetWorkShow}}">
    <view wx:if="{{!isTabChange}}">
      <view wx:for="{{list}}" wx:key="guid">
        <!-- 卡片内容 -->
        <view class="resume-item" data-index="{{index}}" data-item="{{item}}">
          <resume-card-v4 relatedInfoId="{{relatedInfoId}}" showContentBtn sourceId="1" sceneV2="1" bind:toResumeDetail="onToResumeDetail" bind:change="onLocalClick" wx:if="{{item.id}}" item="{{item}}" selectedTab="{{selectPositionTabId.selectItem}}" isActiveAb="{{isActiveAb}}" data-item="{{item}}" data-index="{{index}}" data-origin="{{origin}}" showDutyLabel nearbyWorkerListApiSource="ResumeList" bind:itchange="onItChange" bind:ischat="onIsChat" origin="{{origin}}" class="{{'child-component-' + item.id}}" bind:selectpostionback="onSelectPostionBack" />
        </view>
        <view wx:if="{{showTjDataTips.oguid && item.guid == showTjDataTips.oguid && selectPositionTabId.selectItem.checkStatus == 2}}" class="em-tj-v">
          <view class="em-tj-v-box-left"></view>
          <view class="em-tj-v-box-left-text">
            <image class="em-tj-v-box-img" src="https://cdn.yupaowang.com/other/星光@3x.png" />
            更多牛人推荐
          </view>
          <view class="em-tj-v-box-right"></view>
        </view>
      </view>
      <block wx:if="{{hasMore && !noData}}">
        <view class="no-more" wx:if="{{!resumeTabPosition.length}}">
          <view class="no-more-text">- 发布职位查看更多牛人 -</view>
        </view>
        <view class="no-more" wx:if="{{selectPositionTabId.selectItem.checkStatus == 2}}">
          <view class="no-more-text">获取更多牛人资源，试试</view>
          <view class="no-more-text">
            <view class="no-more-text-btn" bind:tap="onSearch">搜索</view>
            或
            <view class="no-more-text-btn" bind:tap="toCollectList">查看收藏列表</view>
          </view>
        </view>
        <view class="no-more" wx:if="{{selectPositionTabId.selectItem.checkStatus == 0}}">
          <view class="no-more-text">- 修改职位查看全部牛人 -</view>
        </view>
        <view class="no-more" wx:if="{{selectPositionTabId.selectItem.checkStatus == 1}}">
          <view class="no-more-text">- 职位审核通过后，查看全部牛人 -</view>
        </view>
        <view class="no-more" wx:if="{{selectPositionTabId.selectItem.isDraft}}">
          <view class="no-more-text">- 打开职位查看全部牛人 -</view>
        </view>
      </block>
    </view>
    <!-- loading -->
    <loading-refresh wx:if="{{loading}}" />
  </block>
</view>
/*
 * @Date: 2022-02-09 11:00:45
 * @Description: 查看其他用户资料
 */
import { dispatch, actions, store, RootState } from '@/store/index'
import { getConversation } from './utils'
import { isToComplaintOrClassify } from '@/utils/helper/common/index'


Page(class extends wx.$.Page {
  useStore(state: RootState) {
    const { timmsg } = state
    return {
      // 会话基本信息
      conversation: timmsg.conversation,
    }
  }

  data = {
    /** 导航名称 */
    headTitle: '',
    query: {
      conversationId: '',
    },
    /** 是否置顶 */
    isPinned: false,
    /** 是否免打扰 */
    isNotice: false,
    /** 是否拉黑 */
    toUserBlock: false,
    // 设置拉黑弹出提示弹框
    isWrap: false,
    // 是否同时删除和此人的聊天记录
    isCheck: true,
    // 是否显示标记
    isSign: false,
  }

  onLoad(options) {
    this.setData({ query: options })
    // 获取用户信息
    this.getSettingInfo(options)
  }

  // 获取配置信息和用户信息
  async getSettingInfo(options) {
    let { conversation } = this.data as DataTypes<typeof this>
    if (wx.$.u.isEmptyObject(conversation)) {
      const { conversationId } = options
      const res = await getConversation(conversationId)
      conversation = res.conversation
    }
    const { toUserName, toUserRemark, conversationID, toUserBlock } = conversation || {}
    const { myMsgGroupOjb } = store.getState().message
    const msgCvsObj = myMsgGroupOjb[conversationID]
    const { isPinned, isNotice } = msgCvsObj || {}
    this.setData({ headTitle: toUserRemark || toUserName, isPinned, isNotice, toUserBlock })
  }

  // 设置置顶
  async onOprPin() {
    await wx.$.u.waitAsync(this, this.onOprPin, [], 1000)
    const { conversation, isPinned } = this.data as DataTypes<typeof this>
    const { conversationID } = conversation || {}
    const nIsPinned = !isPinned
    dispatch(actions.messageActions.setGroupTop(conversationID, nIsPinned, {
      success: () => {
        this.setData({ isPinned: nIsPinned })
      },
      fail: (err) => {
        const { code } = err || {}
        if (code == 51012) {
          wx.$.msg('置顶会话数已达到上限，无法新增置顶')
        }
      },
    }))
  }

  // 设置群消息是否免打扰
  async setRemindType() {
    await wx.$.u.waitAsync(this, this.setRemindType, [], 1000)
    const { conversation, isNotice } = this.data as DataTypes<typeof this>
    const { toUserImId, conversationID } = conversation || {}
    const nIsNotice = !isNotice
    wx.$.l.msgRemindType([toUserImId], nIsNotice).then(() => {
      this.setData({ isNotice: nIsNotice })
      const { myMsgGroupOjb } = store.getState().message
      const nItem = { ...myMsgGroupOjb[conversationID], isNotice: nIsNotice }
      dispatch(actions.messageActions.fetchImMessageNumber())
      dispatch(actions.messageActions.fetchNewConverNum())
      dispatch(actions.messageActions.setState({ myMsgGroupOjb: { ...myMsgGroupOjb, [conversationID]: nItem } }))
    }).catch(() => {
      wx.$.msg('设置失败,请稍后重试')
    })
  }

  // 设置备注
  async toSetRemark() {
    await wx.$.u.waitAsync(this, this.toSetRemark, [], 1000)
    const { conversation } = this.data as DataTypes<typeof this>
    const { toUserRemark, conversationId, conversationID, toUserName } = conversation || {}
    wx.$.nav.push(
      '/subpackage/tim/setRemark/index',
      {
        title: '设置备注',
      },
      async (data) => {
        const { content } = data
        const remark = content.trim()
        const res = await wx.$.javafetch['POST/reach/v2/im/chat/changeRemark']({ conversationId, remark })
        if (res.code === 0) {
          const { myMsgGroupOjb } = store.getState().message
          dispatch(actions.timmsgActions.setState({ conversation: { ...conversation, toUserRemark: remark } }))
          dispatch(actions.messageActions.setState({
            myMsgGroupOjb: { ...myMsgGroupOjb, [conversationID]: { ...myMsgGroupOjb[conversationID], toUserRemark: remark } },
          }))
          this.setData({ headTitle: remark || toUserName })
          wx.$.msg('设置成功', 500)
        } else {
          wx.$.msg(res.message)
        }
      },
      {
        content: toUserRemark,
      },
    )
  }

  onCheck() {
    const { isCheck } = this.data
    this.setData({ isCheck: !isCheck })
  }

  onWrapClose() {
    this.setData({ isWrap: false })
  }

  // 设置拉黑
  async setUserBlock() {
    await wx.$.u.waitAsync(this, this.setUserBlock, [], 1000)
    const { toUserBlock, conversation } = this.data as DataTypes<typeof this>
    if (toUserBlock) {
      const { toUserId } = conversation || {}
      wx.$.javafetch['POST/griffin/v1/invisible/remove']({ toUserId }).then(() => {
        this.setData({ toUserBlock: false })
        dispatch(actions.timmsgActions.setState({ conversation: { ...conversation, toUserBlock: false } }))
      }).catch(() => {
        wx.$.msg('移除拉黑失败,请稍后重试')
      })
    } else {
      this.setData({ isWrap: true })
    }
  }

  // 保存拉黑状态
  async saveUserBlock() {
    await wx.$.u.waitAsync(this, this.saveUserBlock, [], 1000)
    const { conversation, isCheck } = this.data as DataTypes<typeof this>
    const { toUserId, conversationID } = conversation || {}
    wx.$.javafetch['POST/griffin/v1/invisible/add']({ toUserId, delHistoryChat: isCheck }).then(() => {
      if (isCheck) {
        wx.$.l.deleteConversation(conversationID)
        const { myMsgGroupOjb, myTopMsgGroup, myMsgGroup, imChatList } = store.getState().message
        const nMyMsgGroupOjb = { ...myMsgGroupOjb }
        const obj = nMyMsgGroupOjb[conversationID]
        const arr = [...((obj.isPinned ? myTopMsgGroup : myMsgGroup) || [])]
        const ky = obj.isPinned ? 'myTopMsgGroup' : 'myMsgGroup'
        if (wx.$.u.isArrayVal(imChatList)) {
          const nImChatList = [...imChatList]
          const idx = nImChatList.findIndex(ar => ar.conversationID == conversationID)
          nImChatList.splice(idx, 1)
          dispatch(actions.messageActions.setState({ imChatList: nImChatList }))
        }
        if (wx.$.u.isArrayVal(arr)) {
          const idx = arr.findIndex(ar => ar.conversationID == conversationID)
          arr.splice(idx, 1)
          dispatch(actions.messageActions.setState({ [ky]: arr }))
        }
        delete nMyMsgGroupOjb[conversationID]
        dispatch(actions.messageActions.setState({ myMsgGroupOjb: nMyMsgGroupOjb }))
        dispatch(actions.timmsgActions.clearConversation())
        wx.$.r.back(2)
      } else {
        dispatch(actions.timmsgActions.setState({ conversation: { ...conversation, toUserBlock: true } }))
        this.setData({ toUserBlock: true, isWrap: false, isCheck: true })
      }
    }).catch(() => {
      wx.$.msg('拉黑失败,请稍后重试')
    })
  }

  // 举报ta
  toReport() {
    const { conversation } = this.data as DataTypes<typeof this>
    console.log('conversation', conversation)
    const { toUserId, conversationId, infoDetail } = conversation || {}
    isToComplaintOrClassify({ id: infoDetail.relatedInfoId, projectId: '1102', targetUserId: toUserId, targetId: conversationId, complaintSource: 1007 })
    // wx.$.r.push({
    //   path: '/subpackage/tim/report/index',
    //   query: {
    //     to_uid: toUserId,
    //     conversationId,
    //   },
    // })
  }

  onSignShow() {
    this.setData({ isSign: true })
  }

  onSignClose() {
    this.setData({ isSign: false })
  }
})

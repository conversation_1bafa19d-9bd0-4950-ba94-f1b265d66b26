<view  class="card">
  <resume-card-v4 sourceId="14" sceneV2="11" jobId="{{item.sourceInfoId}}" infoSource="{{item.infoSource}}" item="{{item}}" isVague="{{item.infoSource == 1 && !hasRight}}" data-item="{{item}}" data-index="{{index}}" custom-class="custom-card" class="{{'child-component-' + item.id}}" />
  <!-- 取消收藏 -->
  <view class="footer {{isNeedMarginBottom ? '': 'no_margin_bottom'}}">
    <view catch:tap="onCancelCollectBoss" class="btn-coll" hover-class="hover-class">取消收藏</view>
  </view>
</view>
<view class="msg-main {{msgInfo.isSelf?'':'main-start'}}" wx:if="{{classNames}}">
  <msg-head-img wx:if="{{!msgInfo.isSelf && msgInfo.isAvatar}}" msgInfo="{{msgInfo}}" />
  <view class="resume-card-o" catch:tap="onClick">
    <view>
      <view class="user-info ">
        <text class="user-name-txt">{{info.user_name}}</text>
        <m-tag wx:if="{{info.auth == 2}}" size="small" type="success" icon="yp-icon_rz_sm" customStyle="margin-right:12rpx">
          <text class="real-name-txt">已实名</text>
        </m-tag>
      </view>
      <view class="classify-v">{{classNames}}</view>
    </view>
    <icon-font class="arr-icon" type="yp-icon_mbx" size="48rpx" color="#a6a6a7" />
  </view>
   <msg-head-img wx:if="{{msgInfo.isSelf && msgInfo.isAvatar}}" msgInfo="{{msgInfo}}" />
</view>

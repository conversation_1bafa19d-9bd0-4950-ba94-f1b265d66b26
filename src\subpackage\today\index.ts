import { getMenuButtonBoundingClientRect } from '@/utils/tools/common/index'
import { MapStateToData, connectPage, store, storage, dispatch, actions } from '@/store/index'
import { getRecruitOrResumeShareTitle } from '@/utils/helper/share/index'
import { actionReport } from '@/utils/helper/dialog/index'
import { getFilterData } from '@/utils/helper/resume/index'
import { helper } from '@/utils/index'

import { getAngleInfo, getQualityWorkerFmtQueryParams, shareAppMessageContent } from './utils'
import { getRecommendResumeSubUuidService, getWorkerInfoService } from './api-server'
import { BPOfQualityWorker } from './burying-point'
import pageStorage from './storage'
import { toLogin } from '@/utils/helper/common/toLogin'

const { top, height } = getMenuButtonBoundingClientRect()
const mapStateToData: MapStateToData = (state) => {
  const isLogin = state.storage.userState.login
  return { login: isLogin, isLogin }
}

const defaultData = {
  startX: 0,
  startY: 0,
  height: top + height + 20,
  vertical: false,
  // 用于点击投诉后 在返回到该页面时用于刷新当前的优质师傅信息
  isToRefresh: false,
  indicatorDots: false,
  resumeSubUuids: [],
  workerInfoArr: [],
  swiperIdx: 0,
  swiperIdxWorkerInfo: {},
  /**
   * @name 优质师傅卡片曝光埋点数据
   * @version v4.0.0
   * @description 进入来源 1、2、3、4（1-策略进入、2-消息页面按钮、3-找活大列表、4-新手使用指南、12-push、13-站内信）
   */
  miniQualityWorkerEnterId: '1',
  goodWorkers: { showGestureLeft: false, showGestureRight: false },
  isPageFirstLoading: true,
  // 轮播图 nextMargin 间距
  ENV_IS_WEAPP,
}

/**
 * @name 今日优质师傅推荐页面
 */
Page(connectPage(mapStateToData)({
  data: {
    ...defaultData,
    params: { provinceId: '', cityId: '', classifyIds: '', is_qr_code: 1 },
  },
  async onLoad(query) {
    // 1 策略假如(打开APP直接进来)
    const miniQualityWorkerEnterId = query.miniQualityWorkerEnterId || '1'
    const params = await getQualityWorkerFmtQueryParams(query, this.data.isLogin)

    if (params) {
      this.setData({ miniQualityWorkerEnterId, params: { ...this.data.params, ...params } })
      this.initQualityWorkerPageInfo()
    } else {
      this.setData({ isPageFirstLoading: false })
    }
  },
  onShow() {
    this.getShareTitle()
    // 在投诉/收藏后更新当前卡片的详情
    if (this.data.isToRefresh) {
      getWorkerInfoService.call(this, true)
    }
    const { workerRecommendLogin } = store.getState().storage.common
    // 登录后，数据刷新重置
    if (workerRecommendLogin) {
      this.setData({ ...defaultData })
      this.initQualityWorkerPageInfo(true)
      dispatch(actions.storageActions.setCommonItem({ workerRecommendLogin: false }))
    }
  },

  // -------- 推荐牛人卡片部分
  async initQualityWorkerPageInfo(isRefresh = false) {
    try {
      await getRecommendResumeSubUuidService.call(this, isRefresh)
      await getWorkerInfoService.call(this, isRefresh)
      this.initGoodWorkersConfig()
    } catch (error) {
      console.error(error)
    } finally {
      this.setData({ isPageFirstLoading: false })
    }
  },
  async initGoodWorkersConfig() {
    const goodWorkers = await pageStorage.getInitGoodWorkersConfig()
    this.setData({ goodWorkers })
  },
  // 滑动卡片--监听事件
  async onToSwiper({ detail: { current: swiperIdx } }) {
    if (swiperIdx === this.data.swiperIdx) {
      return
    }

    this.setData({ swiperIdx })
    getWorkerInfoService.call(this)
  },
  touchStart(e) {
    const { swiperIdx, resumeSubUuids } = this.data
    if (swiperIdx == resumeSubUuids.length - 1) {
      const { clientX: startX, clientY: startY } = e.changedTouches[0]
      this.setData({ startX, startY })
    }
  },
  async touchEnd(e) {
    const { startX, startY, resumeSubUuids, swiperIdx } = this.data
    const { clientX: touchMoveX, clientY: touchMoveY } = e.changedTouches[0]
    const angle = getAngleInfo({ startX, startY }, { touchMoveX, touchMoveY })
    const canPopViewed = resumeSubUuids.length - 1 == swiperIdx && Math.abs(angle) < 45 && touchMoveX < startX
    canPopViewed && this.popViewedAllCard()
  },
  // 更新刷新标识用于投诉返回后重新获取当前卡片信息
  onComplain({ detail: { isRefresh } }) {
    if (isRefresh) {
      this.setData({ isToRefresh: isRefresh })
    }
  },

  // -------- 空状态 及 底部操作 和 引导动画 部分
  viewMoreWorkers() {
    wx.$.r.push({ path: '/pages/resume/index' })
  },
  viewNextWorkerInfo() {
    const { swiperIdx } = this.data
    this.onToSwiper({ detail: { current: swiperIdx + 1 } })
  },
  // 点击拨打电话
  async onCallPhone(e) {
    await wx.$.u.waitAsync(this, this.onCallPhone, [e], 2000)
    const { currentTarget } = e
    const click_entry = currentTarget.dataset?.click_entry || ''
    click_entry && this.setData({ click_entry })

    const { storage: { userState } } = store.getState()
    if (!userState.login) {
      toLogin(true)
      return
    }

    // 拨打电话前需要进行实名判断
    wx.showLoading({ title: '正在联系...', mask: true })
    this.setData({ callPhoneType: 'call', isCallPhone: true })
    const { swiperIdxWorkerInfo, miniQualityWorkerEnterId } = this.data
    const { subUuid, viewed } = swiperIdxWorkerInfo || {}
    const filterData = await getFilterData()
    try {
      const { data } = await wx.$.l.resumeTelV3({
        uuid: subUuid,
        popParams: {
          cities: filterData.area_id,
          occupations: filterData.classify_id,
        },
      }, { pageName: '今日推荐', hasShowPhone: viewed })
      wx.hideLoading()
      if (!data) {
        return
      }
      // 拨打电话埋点 0、1、2（0-获取失败、1-获取成功（首次）、2-获取成功（非首次））
      const state = viewed ? 2 : 1
      const { tel, dialogIdentifyDefault, dialogData } = data
      const { dialogIdentify = '' } = dialogData || {}
      if (tel || dialogIdentifyDefault || (await wx.$.l.newResumeMidPops()).includes(dialogIdentify)) {
        const callRealPhone = (isPrivacy = 0) => this.callRealOrMidPhone(isPrivacy)
        const callPhone = () => this.onCallPhone(e)
        wx.$.l.operationMidCallV3(data, { hasShowPhone: viewed }, {
          callPopBack: (pop) => {
            wx.$.resumeMidModel({
              ...pop,
              zIndex: 10011,
              source: '2',
              infoId: subUuid,
              pageName: '今日推荐',
              call: (callresp) => {
                const { detail } = callresp || {}
                if (detail == 2) {
                  const showRealTelState = storage.getItemSync('showRealTelState')
                  if (showRealTelState) {
                    const currentPage = wx.$.r.getCurrentPage()
                    storage.setItemSync('showRealTel', currentPage.route)
                  }
                }
                this.onCallMidPhone(callresp)
              },
            })
          },
          callRealPhone,
          callPhone,
        })
      } else {
        await this.callRealOrMidPhone(0)
      }
      BPOfQualityWorker('qualityWorkersClick', swiperIdxWorkerInfo, {
        state,
        enter_id: miniQualityWorkerEnterId,
        /** 定价方案ID */
        fix_price_id: String(wx.$.u.getObjVal(data, 'pricingId', '0')),
        /** 消耗积分数 */
        consumption_product_score: Number(wx.$.u.getObjVal(data, 'expenseIntegral', '0')),
      }, 'phone')
    } catch (error) {
      wx.hideLoading()
      BPOfQualityWorker('qualityWorkersClick', swiperIdxWorkerInfo, {
        state: '0',
        enter_id: miniQualityWorkerEnterId,
        fix_price_id: '0',
        consumption_product_score: 0,
      }, 'phone')
    } finally {
      getWorkerInfoService.call(this, true)
    }
  },
  onCloseGestureLeft() {
    const { goodWorkers } = this.data
    if (goodWorkers.showGestureLeft) {
      this.setData({ goodWorkers: { ...goodWorkers, showGestureLeft: false } })
      pageStorage.saveTodayGestureLeftDateState()
    }
  },
  onCloseGestureRight() {
    const { goodWorkers } = this.data
    if (goodWorkers.showGestureRight) {
      this.setData({ goodWorkers: { ...goodWorkers, showGestureRight: false } })
      pageStorage.saveTodayGestureRightState()
    }
  },

  /** ************************ 拨打电话 逻辑*************************** */
  // 拨打电话(真实拨打或者中间号拨打)
  async callRealOrMidPhone(isPrivacy = 1) {
    const { swiperIdxWorkerInfo = {} } = this.data
    const filterData = await getFilterData()
    const { subUuid } = swiperIdxWorkerInfo || {}
    wx.$.l.resumeTelV3({
      uuid: subUuid,
      isPopup: 0,
      isPrivacy,
      popParams: {
        cities: filterData.area_id,
        occupations: filterData.classify_id,
      },
    }).then(async (resTel) => {
      if (resTel.code == 0) {
        await wx.$.l.operationMidCallV3(resTel.data, {}, {
          callPopBack: (pop) => {
            wx.$.resumeMidModel({
              ...pop,
              zIndex: 10011,
              source: '2',
              infoId: subUuid,
              pageName: '今日推荐',
              call: (callresp) => {
                const { detail } = callresp || {}
                if (detail == 2) {
                  const showRealTelState = storage.getItemSync('showRealTelState')
                  if (showRealTelState) {
                    const currentPage = wx.$.r.getCurrentPage()
                    storage.setItemSync('showRealTel', currentPage.route)
                  }
                }
                this.onCallMidPhone(callresp)
              },
            })
          },
        })
        this.callPhoneOk(swiperIdxWorkerInfo, resTel.data)
      }
    })
  },
  // 拨打电话返回结果处理逻辑
  callPhoneOk(swiperIdxWorkerInfo, data) {
    helper.list.cardViewHistory.setHistory('resume', swiperIdxWorkerInfo.subUuid, true, true)
    const freeCallNum = swiperIdxWorkerInfo.freeCallNum >= 1 ? swiperIdxWorkerInfo.freeCallNum - 1 : 0
    this.setData({ swiperIdxWorkerInfo: { ...swiperIdxWorkerInfo, freeCallNum, showTel: data.tel, viewed: true, hasShowPhone: true } })
  },
  // 组件回调拨打中间号
  async onCallMidPhone(e) {
    // val.detail 为 2 代表拨打虚拟号
    const val = e ? e.detail : 2
    const { swiperIdxWorkerInfo = {} } = this.data
    const filterData = await getFilterData()
    try {
      const { code, data } = await wx.$.l.resumeTelV3({
        uuid: swiperIdxWorkerInfo.subUuid,
        isPopup: 0,
        isPrivacy: val == 2 ? 1 : 0,
        popParams: {
          cities: filterData.area_id,
          occupations: filterData.classify_id,
        },
      })
      if (code == 0) {
        setTimeout(() => {
          wx.$.u.callPhone(data.tel)
          this.callPhoneOk(swiperIdxWorkerInfo, data)
        }, 200)
      }
    } catch (error) {
      console.error(error)
    }
  },
  // 判断拨打电话之前是否显示评分评价弹框
  judgeShowScoreEvaluation(tel, cb?) {
    wx.$.u.callPhone(tel).then(() => cb && cb())
  },
  // 获取分享的标题
  async getShareTitle() {
    if (this.data.isLogin) {
      /** 去请求获取分享title的接口 */
      const config = await getRecruitOrResumeShareTitle('resume')
      this.shareTitle = config.shareTitle
      return
    }
    this.shareTitle = ''
  },

  // ------ 其它部分
  // 提示浏览完所有的卡片-弹窗
  async popViewedAllCard() {
    await wx.$.u.waitAsync(this, this.popViewedAllCard, [], 500)
    // 弹窗上报
    actionReport({ popCode: 'The_last_worker', action: 1 })
    const content = '您已浏览到推荐的最后一个简历，点击查看更多牛人按钮去浏览更多优质牛人'
    const confirmText = '查看更多牛人'
    wx.$.confirm({ content, confirmText }).then(() => this.viewMoreWorkers())
  },
  // 用户点击右上角分享,需传入title
  onShareAppMessage(options) {
    return shareAppMessageContent({ ...options, title: this.shareTitle })
  },
}))

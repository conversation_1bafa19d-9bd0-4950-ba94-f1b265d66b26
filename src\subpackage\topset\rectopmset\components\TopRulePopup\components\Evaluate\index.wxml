<view class="container">
  <view wx:for="{{list}}" wx:key="index" class="card">
    <view class="cardHeader">
      <image class="cardHeaderImg" src="{{item.icon}}" />
      <view class="cardHeaderContent">
        <text class="title">{{item.name}}</text>
        <text class="text">{{item.profession}}</text>
      </view>
    </view>
    <view class="cardContent">
      <view class="block">
        <icon-font type="yp-zan" size="32rpx" color="#0092FF" custom-style="display: flex;margin-right:8rpx;" />
        <text class="text">{{item.desc}}</text>
      </view>
      <text class="desc">{{item.content}}</text>
    </view>
  </view>
</view>

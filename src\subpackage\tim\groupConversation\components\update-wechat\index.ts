import { actions, dispatch, store } from '@/store/index'
import { isWeChat } from '@/utils/tools/validator/index'
import { applyExchange, handleExchange } from '../../utils'

Component(class extends wx.$.Component {
  properties = {
    visible: { type: Boolean, value: false },
    // 1.发起换微信  2.同意换微信
    type: { type: Number, value: 1 },
    // 要修改微信号的消息记录ID
    exchangeWxMsgId: { type: String, value: '' },
    exchangeType: { type: String, value: '' },
  }

  observers = {
    visible(v) {
      if (v) {
        wx.$.collectEvent.event('exchange_wechat_exposure', { source_id: '1' })
      }
    },
  }

  data = {
    // 微信号
    wechatNumber: '',
  }

  lifetimes = {
  }

  onInput(e) {
    const { value } = e.detail
    this.setData({ wechatNumber: value })
  }

  onClose() {
    wx.$.collectEvent.event('exchange_wechat_click', { button_name: '取消', source_id: '1' })
    this.triggerEvent('close')
  }

  async onConfirm() {
    await wx.$.u.waitAsync(this, this.onConfirm, [], 1000)
    const { conversation } = store.getState().timmsg
    const { conversationId } = conversation || {}
    const { wechatNumber, type } = this.data as DataTypes<typeof this>
    if (!conversationId) {
      wx.$.msg('会话异常,请稍后重试', 1500).then(() => {
        wx.$.r.back()
      })
      return
    }
    if (!wechatNumber) {
      wx.$.msg('你还未填写')
      return
    }
    if (!isWeChat(wechatNumber)) {
      wx.$.msg('微信号格式错误，请重新输入')
      return
    }
    wx.$.collectEvent.event('exchange_wechat_click', { button_name: '确认', source_id: '1' })
    wx.$.loading('请求中...')
    // 保存微信号
    const res = await wx.$.javafetch['POST/account/v1/userBase/updateWechatNumber']({
      wechatNumber,
    })
    const { code, message } = res || {}
    if (code != 0) {
      wx.hideLoading()
      wx.$.msg(message || '请求失败')
      return
    }
    dispatch(actions.userActions.fetchUserInfo())
    this.triggerEvent('close')
    if (type == 2) {
    // 同意换微信
      this.exchangeTel()
    } else {
    // 发起换微信
      this.applyExchangeTel()
    }
  }

  // 同意换微信
  exchangeTel() {
    const { exchangeType, exchangeWxMsgId } = this.data as any
    handleExchange({ exchangeMsgId: exchangeWxMsgId, agree: true, exchangeType: exchangeType || 'EXCHANGE_WECHAT' }, {}, {
      success: () => {
        this.triggerEvent('close')
      },
    })
  }

  // 发起换微信
  applyExchangeTel() {
    applyExchange('EXCHANGE_WECHAT', {}, {
      success: () => {
        this.triggerEvent('close')
      },
    })
  }
})

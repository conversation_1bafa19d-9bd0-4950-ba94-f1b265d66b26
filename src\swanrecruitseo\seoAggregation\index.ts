/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-02-09 08:55:29
 * @FilePath: /yupao_mini_taro_recode-1.2.1-yh/src/swan-sitemap/resume/index.js
 * @Description:
 */
import { transDate } from './utils'

Page({
  data: {
    aggregationData: {},
    list_info: [],
  },

  async onLoad(option) {
    const { keyword } = option
    const { data } = await wx.$.fetch['POST/job/seoTagAggregation/getTagById']({ id: keyword || 771472 })
    this.setData({
      aggregationData: data,
    })
    this.getAggregationList(data.list_info)
    wx.$.setTDK({
      ...data.tdk,
    })
  },

  /** 跳转招工聚合页 */
  clickItemA({ target: { dataset: { item } } }) {
    if (item) {
      wx.$.r.push({ path: '/swanrecruitseo/seoAggregation/index', query: { keyword: item.id } })
    }
  },

  /** 跳转招工大全详情 */
  clickItemB({ target: { dataset: { item } } }) {
    if (item) {
      wx.$.r.push({ path: '/swanrecruitseo/recruitListDetailSeo/index', query: { id: item.id } })
    }
  },

  /** 列表数据清洗 */
  getAggregationList(list) {
    list.map((item) => {
      item.sort_time = transDate(item.sort_time)
    })
    this.setData({
      list_info: list,
    })
  },

  /** 点击面包屑 */
  toIndex({ target: { dataset: { item } } }) {
    if (item) {
      wx.$.r.push({ path: item })
    } else {
      wx.$.r.replace({ path: '/swanrecruitseo/seoAggregation/index' })
    }
  },

})

<view class="video-examples">
  <view class="dividing-line"></view>
  <view class="title" bind:tap="onOpenExamples">
    <view>示例视频</view>
    <icon-font type="yp-icon_tab_sx_nor" custom-class="icon {{openExample ? 'rotate' : ''}}" />
  </view>
  <view class="video-wrap {{openExample ? '' : 'video-hide'}}" bind:tap="onClickPlay">
    <video class="video" poster="{{poster}}" src="{{videoUrl}}" object-fit="cover" id="example-video" controls="{{false}}" show-center-play-btn="{{false}}" enable-play-gesture="{{false}}" bind:timeupdate="onTimeUpdate" bind:ended="onEnded" bind:loadedmetadata="onInitVideo"></video>
    <image class="play-btn" wx:if="{{!playState}}" src="https://staticscdn.zgzpsjz.com/miniprogram/images/wjj/yp-mini_video_act.png?x-oss-process=image/format,webp/quality,q_91" />
    <view wx:if="{{visiblePlayBtn}}">
      <view class="action-btn">
        <icon-font type="{{playState?'yp-icon_video_stop':'yp-icon_video_action'}}" size="60rpx" color="#fff" />
      </view>
      <view class="video-timeline">{{videoDuration}}</view>
      <view class="progress">
        <view class="progress-item" style="width:{{progress}}%" />
      </view>
    </view>
  </view>
</view>

/*
 * @Date: 2022-04-07 15:16:55
 * @Description: 卡片列表历史查看
 */

import { store, storage } from '@/store/index'

/** 招工找活两种类型 */
type CardType = 'recruit' | 'resume'

/**
 * 设置查看的历史记录
 * @param viewed 用于判断列表卡片是否置灰
 * @param dial 用于判断是否已查看(即已拨打电话)
 */
function setHistory(cardType: CardType, id: any, viewed = false, dial = false, expenseId = 0) {
  const nowTime = new Date().getTime()
  /** 未登录的key */
  const logoutKey = `LO${cardType}` as 'LOrecruit' | 'LOresume'
  /** 拼接的登录的key */
  const loginKey = `LI${cardType}` as 'LIrecruit' | 'LIresume'
  // 判断是否登录
  const { login, userId } = store.getState().storage.userState
  // 判断是否有用户id，如果有就用用户id当做key，否则用 temp
  const key = login ? userId : 'temp'
  const data = wx.$.u.deepClone(storage.getItemSync('cardViewedHistory')) as any
  const typeKey = login ? loginKey : logoutKey
  // 空值赋值
  if (!data[key]) {
    data[key] = {}
  }
  if (!data[key][typeKey]) {
    data[key][typeKey] = []
  }
  // 如果大于600条缓存，就删除前100条，保持有500个缓存
  if (data[key][typeKey].length > 600) {
    data[key][typeKey] = data[key][typeKey].filter((_, i) => i > 100)
  }
  // 查询数组中是否存在重复id
  const current = data[key][typeKey].find((item) => item.id == id)
  if (current) {
    current.viewed = viewed
    current.dial = dial
    current.expenseId = expenseId
  } else {
    data[key][typeKey].push({ id, viewed, dial, expenseId, outTime: nowTime + 7 * 24 * 3600 * 1000 })
  }
  // 招工删除过期的数据和未查看的数据（过期时间7天）
  if (cardType == 'recruit' && login) {
    data[key].LIrecruit = deleteExpiredData(data, key)
  }
  return storage.setItem('cardViewedHistory', data)
}

/** 通过列表ID判断是否需要将列表数据缓存已查看数据或者删除已查看数据 */
async function setHistoryList(cardType: CardType, list: any[], checkKey = 'id') {
  // 判断是否登录
  const { login, userId } = store.getState().storage.userState
  if (!login) {
    return
  }
  /** 未登录的key */
  const logoutKey = `LO${cardType}` as 'LOrecruit' | 'LOresume'
  /** 拼接的登录的key */
  const loginKey = `LI${cardType}` as 'LIrecruit' | 'LIresume'

  const key = login ? userId : 'temp'
  if (list.length) {
    const typeKey = login ? loginKey : logoutKey
    const data = wx.$.u.deepClone(storage.getItemSync('cardViewedHistory')) as any

    if (cardType == 'recruit') {
      list.forEach((item) => {
        if (item.isLook) {
          setHistory('recruit', item.jobId, true, true)
        } else {
          const value = data?.[key]?.[typeKey]?.find((item) => item.id == item.jobId)
          if (value) {
            setHistory('recruit', item.jobId, value.viewed, false)
          }
        }
      })
    }

    if (!data[key]) {
      data[key] = {}
    }
    if (!data[key][typeKey]) {
      data[key][typeKey] = []
    }
    const ids = list.map((item) => item[checkKey])
    const historyList = data?.[key]?.[typeKey]
      .filter((item) => ids.includes(item.id))
      .map((item) => item.expenseId)
      .filter((expenseId) => !!expenseId)
    if (historyList?.length) {
      const res = await wx.$.javafetch['POST/integral/v1/integralExpense/getExpenseReturnStatusByIdList']({ idList: historyList })
      if (res.data?.length > 0) {
        let isChange = false
        const haveIds = []
        res.data.forEach((item) => {
          haveIds.push(item.id)
          if (item.isReturn == 1) {
            const index = data?.[key]?.[typeKey].findIndex((history) => history.expenseId == item.id)
            if (index >= 0) {
              isChange = true
              data[key][typeKey][index].dial = false
            }
          }
        })
        const delids = historyList.filter((id) => !haveIds.includes(id))
        if (delids.length > 0) {
          isChange = true
          delids.forEach((id) => {
            const index = data?.[key]?.[typeKey].findIndex((history) => history.expenseId == id)
            if (index >= 0) {
              data[key][typeKey].splice(index, 1)
            }
          })
        }
        if (isChange) {
          storage.setItem('cardViewedHistory', data)
        }
      }
    }
  }
}

/** 获取查看的历史记录列表 */
function getHistoryList(cardType: CardType, list: any[], checkKey = 'id') {
  /** 未登录的key */
  const logoutKey = `LO${cardType}` as 'LOrecruit' | 'LOresume'
  /** 拼接的登录的key */
  const loginKey = `LI${cardType}` as 'LIrecruit' | 'LIresume'
  // 判断是否登录
  const { login, userId } = store.getState().storage.userState
  const key = login ? userId : 'temp'
  const data = wx.$.u.deepClone(storage.getItemSync('cardViewedHistory')) as any
  const typeKey = login ? loginKey : logoutKey
  const historyList = data?.[key]?.[typeKey] || []
  // 如果登录了，登录合并temp数据
  if (login && data.temp && Object.keys(data.temp).length) {
    const tempData = data.temp
    const userData = data[userId] || {}
    // eslint-disable-next-line no-restricted-syntax
    for (const attr in tempData) {
      if (Object.prototype.hasOwnProperty.call(tempData, attr)) {
        if (!userData[attr]) {
          userData[attr] = []
        }
        userData[attr] = userData[attr].concat(tempData[attr])
      }
    }
    // 合并后清空temp数据
    data.temp = {}
    storage.setItem('cardViewedHistory', data)
  }
  // 循环取出来历史记录
  return list.map((record) => {
    const value = historyList.find((item) => item.id == record?.[checkKey]) || { viewed: false, dial: false }
    return {
      ...(record || {}),
      viewed: value.viewed || record.viewed,
      dial: value.dial,
      // is_report: i % 5 == 0 ? 1 : 0,
    }
  })
}

function deleteExpiredData(data, key) {
  const nowTime = new Date().getTime()
  const arr = []
  data[key].LIrecruit.forEach((item) => {
    if (item.outTime > nowTime) {
      arr.push(item)
    }
  })
  return arr
}

const cardViewHistory = {
  getHistoryList,
  setHistory,
  setHistoryList,
}

export default cardViewHistory

page {
  background: rgba(255, 255, 255, 1);
}

.body {
  width: 100vw;
  padding: 0 32rpx;
}

.other-info {
  display: flex;
  align-items: center;
  padding: 48rpx 0;
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}

.head-img {
  position: relative;
  width: 96rpx;
  height: 96rpx;
  margin-right: 24rpx;
}

.i-img {
  width: 100%;
  height: 100%;
  border-radius: 48rpx;
}

.i-info {
  flex: 1;
  .ellip()
}

.i-name {
  font-size: 30rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
  .ellip()
}

.i-txt {
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 8rpx;
}

.set-btn {
  padding-left: 32rpx;
  background: #fff;
  margin-top: 16rpx;
}

.set-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 36rpx 0;
}

.item-p {
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}

.item-n {
  flex-direction: column;
}

.item-t {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-b {
  color: rgba(0, 0, 0, 0.45);
  font-size: 26rpx;
  margin-top: 8rpx;
}
.item-r {
  display: flex;
  align-items: center;
}

.item-r-name {
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
}

.item-r-txt {
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  max-width: 422rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.txt-h {
  color: rgba(0, 0, 0, 0.25);
}

.qianming-v {
  width: 100%;
  background: #f5f6fa;
  border-radius: 8px;
  margin-top: 24rpx;
  padding: 24rpx;
}

.ic_dian {
  margin-bottom: 16rpx;
}

.qianming-txt {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.65);
  line-height: 40rpx;
}

.wrap {
  border-radius: 16rpx 16rpx 0 0;
  background: rgba(255, 255, 255, 1);
  padding: 0 32rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.wrap-top {
  width: 100%;
  margin: 32rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.wrap-title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 34rpx;
}

.wrap-close {
  width: 40rpx;
  height: 40rpx;
}

.wrap-content {
  color: rgba(0, 0, 0, 0.65);
  font-size: 30rpx;
}

.wrap-txt {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
}

.wrap-check {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 24rpx 0;
}

.wrap-del-txt {
  color: rgba(0, 0, 0, 0.45);
  font-size: 26rpx;
  margin-left: 8rpx;
}

.wrap-btn {
  width: 100%;
  margin: 24rpx 0;
  height: 96rpx;
  border-radius: 12rpx;
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 34rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

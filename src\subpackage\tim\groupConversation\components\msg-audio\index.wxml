<view class="msg-txt-v {{msgInfo.isSelf?'msg-txt-v-r':'msg-txt-v-l'}}">
  <longpress-tk msgInfo="{{msgInfo}}" wx:if="{{isShowTk}}" isCopy="{{false}}" tkWith="{{tkWith}}" />
  <msg-state wx:if="{{msgInfo.isSelf}}" style="height: 100%;" margin="right" msgInfo="{{msgInfo}}" status="{{msgInfo.status}}" bind:statusclcik="onStatusClcik"/>
  <view class="msg-txt {{msgInfo.isSelf?'':'msg-other'}}" style="width:{{134+msgInfo.payload.second*10}}rpx;" catch:longpress="onLongpressMsg" id="{{msgInfo.id}}_tk" catch:tap="onClick">
    <view class="audio-v {{msgInfo.isSelf?'audio-self':'audio-other'}}">
      <image lazy-load="{{true}}" wx:if="{{!msgInfo.isSelf && !isPlay}}" class="image-play-yuyin" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_yuyin_left2.png" />
      <!-- <icon-font wx:if="{{!msgInfo.isSelf && !isPlay}}" class="triangle" type="yp-icon_yuyin_white" size="28rpx" color="#8c8c8c" /> -->
      <image lazy-load="{{true}}" wx:if="{{!msgInfo.isSelf && isPlay}}" class="image-play-yuyin" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_yuyin_left2_dt.png" />
      <view class="seco-txt">{{msgInfo.payload.second}}''</view>
      <!-- <icon-font wx:if="{{msgInfo.isSelf && !isPlay}}" type="yp-icon_yuyin_white" size="28rpx" color="#ffffff" /> -->
      <image lazy-load="{{true}}" wx:if="{{msgInfo.isSelf && !isPlay}}" class="image-play-yuyin img-right" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_yuyin_right2.png" />
      <image lazy-load="{{true}}" wx:if="{{msgInfo.isSelf && isPlay}}" class="image-play-yuyin img-right" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_yuyin_right2_dt.png" />
    </view>
  </view>
  <!-- <view class="arrow {{msgInfo.isSelf?'arrow-r':'arrow-l'}}"></view> -->
  <msg-state wx:if="{{!msgInfo.isSelf && !msgInfo.isRead}}" status="red" margin="left" />
</view>